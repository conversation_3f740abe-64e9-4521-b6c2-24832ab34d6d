# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
.pnp.loader.mjs

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local Environment Variables
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

# TypeScript
*.tsbuildinfo
next-env.d.ts

#IDE
.vscode/
.idea/

# Other
/exports/
/scripts/logs/
/scripts/venv/
*.log
logs/
.pnpm-store/
yalc.lock

