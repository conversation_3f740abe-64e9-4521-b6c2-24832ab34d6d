import { describe, it, expect, beforeEach, vi } from 'vitest'
import { prisma } from '@/lib/prisma'
import { sendWelcomeEmail, sendAdminNotification } from '@/lib/email'
import { waitlistSchema } from '@/lib/validations'

// Mock the modules
vi.mock('@/lib/prisma')
vi.mock('@/lib/email')

const mockPrisma = vi.mocked(prisma)
const mockSendWelcomeEmail = vi.mocked(sendWelcomeEmail)
const mockSendAdminNotification = vi.mocked(sendAdminNotification)

// Mock the API route logic function
async function processWaitlistSubmission(data: any) {
  try {
    // Validate the request body
    const validatedData = waitlistSchema.parse(data)
    
    // Check if email already exists
    const existingEntry = await prisma.waitlistEntry.findUnique({
      where: { email: validatedData.email }
    })
    
    if (existingEntry) {
      return {
        status: 400,
        body: { error: 'Email already registered' }
      }
    }
    
    // Create waitlist entry
    const waitlistEntry = await prisma.waitlistEntry.create({
      data: {
        email: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        healthConditions: validatedData.healthConditions || [],
        dietaryGoals: validatedData.dietaryGoals || [],
        currentDiet: validatedData.currentDiet,
        referralSource: validatedData.referralSource,
        interests: validatedData.interests || [],
        priority: data.priority || 1,
      }
    })
    
    // Send welcome email
    if (process.env.WAITLIST_WELCOME_EMAIL_ENABLED === 'true') {
      try {
        await sendWelcomeEmail(validatedData.email, validatedData.firstName)
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError)
        // Don't fail the request if email fails
      }
    }
    
    // Send admin notification
    try {
      await sendAdminNotification(waitlistEntry)
    } catch (adminEmailError) {
      console.error('Failed to send admin notification:', adminEmailError)
      // Don't fail the request if admin email fails
    }
    
    return {
      status: 200,
      body: {
        success: true,
        message: 'Successfully joined waitlist',
        id: waitlistEntry.id
      }
    }
    
  } catch (error) {
    console.error('Waitlist signup error:', error)
    
    if (error instanceof Error && error.name === 'ZodError') {
      return {
        status: 400,
        body: { error: 'Invalid form data' }
      }
    }
    
    return {
      status: 500,
      body: { error: 'Internal server error' }
    }
  }
}

describe('Waitlist API Logic', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock responses
    mockPrisma.waitlistEntry.findUnique.mockResolvedValue(null)
    mockPrisma.waitlistEntry.create.mockResolvedValue({
      id: 'mock-id-123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      healthConditions: ['Diabetes'],
      dietaryGoals: ['Weight Loss'],
      currentDiet: 'Standard',
      referralSource: 'Google',
      interests: ['Tracking'],
      priority: 3,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    
    mockSendWelcomeEmail.mockResolvedValue(undefined)
    mockSendAdminNotification.mockResolvedValue(undefined)
  })

  it('should successfully create a waitlist entry with valid data', async () => {
    const validData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      healthConditions: ['Diabetes'],
      dietaryGoals: ['Weight Loss'],
      currentDiet: 'Standard',
      referralSource: 'Google',
      interests: ['Tracking'],
      priority: 3
    }

    const result = await processWaitlistSubmission(validData)

    expect(result.status).toBe(200)
    expect(result.body.success).toBe(true)
    expect(result.body.message).toBe('Successfully joined waitlist')
    expect(result.body.id).toBe('mock-id-123')

    // Verify database interactions
    expect(mockPrisma.waitlistEntry.findUnique).toHaveBeenCalledWith({
      where: { email: '<EMAIL>' }
    })
    
    expect(mockPrisma.waitlistEntry.create).toHaveBeenCalledWith({
      data: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        healthConditions: ['Diabetes'],
        dietaryGoals: ['Weight Loss'],
        currentDiet: 'Standard',
        referralSource: 'Google',
        interests: ['Tracking'],
        priority: 3
      }
    })
  })

  it('should handle minimal data (email only)', async () => {
    const minimalData = {
      email: '<EMAIL>'
    }

    const result = await processWaitlistSubmission(minimalData)

    expect(result.status).toBe(200)
    expect(result.body.success).toBe(true)

    expect(mockPrisma.waitlistEntry.create).toHaveBeenCalledWith({
      data: {
        email: '<EMAIL>',
        firstName: undefined,
        lastName: undefined,
        healthConditions: [],
        dietaryGoals: [],
        currentDiet: undefined,
        referralSource: undefined,
        interests: [],
        priority: 1
      }
    })
  })

  it('should reject invalid email format', async () => {
    const invalidData = {
      email: 'invalid-email-format'
    }

    const result = await processWaitlistSubmission(invalidData)

    expect(result.status).toBe(400)
    expect(result.body.error).toBe('Invalid form data')
    
    // Should not call database methods
    expect(mockPrisma.waitlistEntry.findUnique).not.toHaveBeenCalled()
    expect(mockPrisma.waitlistEntry.create).not.toHaveBeenCalled()
  })

  it('should reject duplicate email addresses', async () => {
    // Mock existing user
    mockPrisma.waitlistEntry.findUnique.mockResolvedValue({
      id: 'existing-id',
      email: '<EMAIL>',
      firstName: 'Existing',
      lastName: 'User',
      healthConditions: [],
      dietaryGoals: [],
      currentDiet: null,
      referralSource: null,
      interests: [],
      priority: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    const duplicateData = {
      email: '<EMAIL>',
      firstName: 'New',
      lastName: 'User'
    }

    const result = await processWaitlistSubmission(duplicateData)

    expect(result.status).toBe(400)
    expect(result.body.error).toBe('Email already registered')
    
    // Should check for existing email but not create
    expect(mockPrisma.waitlistEntry.findUnique).toHaveBeenCalledWith({
      where: { email: '<EMAIL>' }
    })
    expect(mockPrisma.waitlistEntry.create).not.toHaveBeenCalled()
  })

  it('should handle database errors gracefully', async () => {
    mockPrisma.waitlistEntry.create.mockRejectedValue(new Error('Database connection failed'))

    const validData = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User'
    }

    const result = await processWaitlistSubmission(validData)

    expect(result.status).toBe(500)
    expect(result.body.error).toBe('Internal server error')
  })

  it('should handle email sending failures gracefully', async () => {
    // Mock email sending failure
    mockSendWelcomeEmail.mockRejectedValue(new Error('Email service unavailable'))
    
    // Set environment variable to enable email sending
    process.env.WAITLIST_WELCOME_EMAIL_ENABLED = 'true'

    const validData = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User'
    }

    const result = await processWaitlistSubmission(validData)

    // Should still succeed even if email fails
    expect(result.status).toBe(200)
    expect(result.body.success).toBe(true)
    
    // Should still create the database entry
    expect(mockPrisma.waitlistEntry.create).toHaveBeenCalled()
    
    // Clean up
    delete process.env.WAITLIST_WELCOME_EMAIL_ENABLED
  })

  it('should send welcome email when enabled', async () => {
    process.env.WAITLIST_WELCOME_EMAIL_ENABLED = 'true'

    const validData = {
      email: '<EMAIL>',
      firstName: 'Welcome',
      lastName: 'User'
    }

    const result = await processWaitlistSubmission(validData)
    expect(result.status).toBe(200)

    expect(mockSendWelcomeEmail).toHaveBeenCalledWith('<EMAIL>', 'Welcome')
    
    delete process.env.WAITLIST_WELCOME_EMAIL_ENABLED
  })

  it('should not send welcome email when disabled', async () => {
    const validData = {
      email: '<EMAIL>',
      firstName: 'No',
      lastName: 'Email'
    }

    const result = await processWaitlistSubmission(validData)
    expect(result.status).toBe(200)

    expect(mockSendWelcomeEmail).not.toHaveBeenCalled()
  })

  it('should send admin notification', async () => {
    const validData = {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'Test'
    }

    const result = await processWaitlistSubmission(validData)
    expect(result.status).toBe(200)

    expect(mockSendAdminNotification).toHaveBeenCalledWith({
      id: 'mock-id-123',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'Test',
      healthConditions: [],
      dietaryGoals: [],
      currentDiet: undefined,
      referralSource: undefined,
      interests: [],
      priority: 1,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
    })
  })

  it('should set default priority when not provided', async () => {
    const dataWithoutPriority = {
      email: '<EMAIL>',
      firstName: 'No',
      lastName: 'Priority'
    }

    const result = await processWaitlistSubmission(dataWithoutPriority)
    expect(result.status).toBe(200)

    expect(mockPrisma.waitlistEntry.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        priority: 1 // Default priority
      })
    })
  })

  it('should preserve custom priority when provided', async () => {
    const dataWithPriority = {
      email: '<EMAIL>',
      firstName: 'With',
      lastName: 'Priority',
      priority: 5
    }

    const result = await processWaitlistSubmission(dataWithPriority)
    expect(result.status).toBe(200)

    expect(mockPrisma.waitlistEntry.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        priority: 5 // Custom priority
      })
    })
  })
}) 