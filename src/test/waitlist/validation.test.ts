import { describe, it, expect } from 'vitest'
import { waitlistSchema } from '@/lib/validations'

describe('Waitlist Validation Schema', () => {
  describe('Email validation', () => {
    it('should accept valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]

      validEmails.forEach(email => {
        const result = waitlistSchema.safeParse({ email })
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'test@',
        '<EMAIL>',
        'test@domain',
        ''
      ]

      invalidEmails.forEach(email => {
        const result = waitlistSchema.safeParse({ email })
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toContain('valid email')
        }
      })
    })

    it('should require email field', () => {
      const result = waitlistSchema.safeParse({})
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email')
      }
    })
  })

  describe('Optional fields validation', () => {
    it('should accept valid optional string fields', () => {
      const validData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        currentDiet: 'Standard American Diet',
        referralSource: 'Google Search'
      }

      const result = waitlistSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should accept empty optional fields', () => {
      const dataWithEmptyOptionals = {
        email: '<EMAIL>',
        firstName: '',
        lastName: '',
        currentDiet: '',
        referralSource: ''
      }

      const result = waitlistSchema.safeParse(dataWithEmptyOptionals)
      expect(result.success).toBe(true)
    })

    it('should accept missing optional fields', () => {
      const minimalData = {
        email: '<EMAIL>'
      }

      const result = waitlistSchema.safeParse(minimalData)
      expect(result.success).toBe(true)
    })
  })

  describe('Array fields validation', () => {
    it('should accept valid array fields', () => {
      const validData = {
        email: '<EMAIL>',
        healthConditions: ['Diabetes', 'Autoimmune Disease'],
        dietaryGoals: ['Weight Loss', 'Improve Energy'],
        interests: ['Nutrition Tracking', 'Community Support']
      }

      const result = waitlistSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should accept empty arrays', () => {
      const dataWithEmptyArrays = {
        email: '<EMAIL>',
        healthConditions: [],
        dietaryGoals: [],
        interests: []
      }

      const result = waitlistSchema.safeParse(dataWithEmptyArrays)
      expect(result.success).toBe(true)
    })

    it('should accept missing array fields', () => {
      const minimalData = {
        email: '<EMAIL>'
      }

      const result = waitlistSchema.safeParse(minimalData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid array values', () => {
      const invalidData = {
        email: '<EMAIL>',
        healthConditions: 'not an array'
      }

      const result = waitlistSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('Complete valid form data', () => {
    it('should accept complete form submission', () => {
      const completeFormData = {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        healthConditions: ['Autoimmune Disease', 'Digestive Issues'],
        dietaryGoals: ['Reduce Inflammation', 'Better Sleep'],
        currentDiet: 'Paleo',
        referralSource: 'Friend Recommendation',
        interests: ['Meal Planning', 'Recipe Sharing', 'Progress Tracking']
      }

      const result = waitlistSchema.safeParse(completeFormData)
      expect(result.success).toBe(true)

      if (result.success) {
        expect(result.data.email).toBe(completeFormData.email)
        expect(result.data.firstName).toBe(completeFormData.firstName)
        expect(result.data.lastName).toBe(completeFormData.lastName)
        expect(result.data.healthConditions).toEqual(completeFormData.healthConditions)
        expect(result.data.dietaryGoals).toEqual(completeFormData.dietaryGoals)
        expect(result.data.currentDiet).toBe(completeFormData.currentDiet)
        expect(result.data.referralSource).toBe(completeFormData.referralSource)
        expect(result.data.interests).toEqual(completeFormData.interests)
      }
    })
  })

  describe('Edge cases', () => {
    it('should handle very long string values', () => {
      const longString = 'a'.repeat(1000)
      const dataWithLongStrings = {
        email: '<EMAIL>',
        firstName: longString,
        lastName: longString,
        currentDiet: longString,
        referralSource: longString
      }

      const result = waitlistSchema.safeParse(dataWithLongStrings)
      // Should still be valid unless specific length constraints are added
      expect(result.success).toBe(true)
    })

    it('should handle special characters in names', () => {
      const dataWithSpecialChars = {
        email: '<EMAIL>',
        firstName: "Jean-Luc O'Connor",
        lastName: "García-López",
        currentDiet: "Traditional & Organic",
        referralSource: "Dr. Smith's Clinic"
      }

      const result = waitlistSchema.safeParse(dataWithSpecialChars)
      expect(result.success).toBe(true)
    })

    it('should handle unicode characters', () => {
      const dataWithUnicode = {
        email: '<EMAIL>',
        firstName: '田中',
        lastName: '太郎',
        currentDiet: 'Méditerranéen',
        referralSource: 'Référence médicale'
      }

      const result = waitlistSchema.safeParse(dataWithUnicode)
      expect(result.success).toBe(true)
    })
  })
}) 