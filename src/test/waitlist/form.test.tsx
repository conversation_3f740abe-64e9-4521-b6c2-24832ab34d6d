import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DetailedWaitlistForm } from '@/components/forms/detailed-waitlist-form'

// Mock fetch globally
global.fetch = vi.fn()

// Mock react-confetti
vi.mock('react-confetti', () => ({
  default: ({ recycle }: { recycle: boolean }) => <div data-testid="confetti" />
}))

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  }
}))

const mockFetch = vi.mocked(fetch)

describe('DetailedWaitlistForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default successful response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        message: 'Successfully joined waitlist',
        id: 'test-id-123'
      })
    } as Response)
  })

  it('should render the form trigger button', () => {
    render(<DetailedWaitlistForm />)
    
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    expect(triggerButton).toBeInTheDocument()
  })

  it('should render custom trigger when provided', () => {
    const customTrigger = <button>Custom Join Button</button>
    render(<DetailedWaitlistForm trigger={customTrigger} />)
    
    const customButton = screen.getByRole('button', { name: /custom join button/i })
    expect(customButton).toBeInTheDocument()
  })

  it('should open dialog when trigger is clicked', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    // Should show the dialog with form
    expect(screen.getByText(/join the proper human diet waitlist/i)).toBeInTheDocument()
    expect(screen.getByText(/step 1 of/i)).toBeInTheDocument()
  })

  it('should validate email field on first step', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    // Try to proceed without email
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)
    
    // Should show validation error (form should prevent submission)
    expect(screen.getByText(/step 1 of/i)).toBeInTheDocument()
  })

  it('should proceed to next step when email is valid', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    // Fill in email
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Click next
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)
    
    // Should proceed to step 2
    await waitFor(() => {
      expect(screen.getByText(/step 2 of/i)).toBeInTheDocument()
    })
  })

  it('should allow navigation between steps', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and proceed to step 2
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)
    
    await waitFor(() => {
      expect(screen.getByText(/step 2 of/i)).toBeInTheDocument()
    })
    
    // Go back to step 1
    const prevButton = screen.getByRole('button', { name: /previous/i })
    await user.click(prevButton)
    
    await waitFor(() => {
      expect(screen.getByText(/step 1 of/i)).toBeInTheDocument()
    })
  })

  it('should handle form submission with minimal data', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    // Fill only email (minimal required data)
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Navigate to final step
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)
    
    await waitFor(() => {
      expect(screen.getByText(/step 2 of/i)).toBeInTheDocument()
    })
    
    await user.click(nextButton)
    await waitFor(() => {
      expect(screen.getByText(/step 3 of/i)).toBeInTheDocument()
    })
    
    await user.click(nextButton)
    await waitFor(() => {
      expect(screen.getByText(/step 4 of/i)).toBeInTheDocument()
    })
    
    await user.click(nextButton)
    await waitFor(() => {
      expect(screen.getByText(/step 5 of/i)).toBeInTheDocument()
    })
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should make API call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/waitlist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          firstName: '',
          lastName: '',
          healthConditions: [],
          dietaryGoals: [],
          currentDiet: '',
          referralSource: '',
          interests: [],
          priority: expect.any(Number)
        })
      })
    })
  })

  it('should handle form submission with complete data', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    // Step 1: Fill personal info
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    const firstNameInput = screen.getByPlaceholderText(/first name/i)
    await user.type(firstNameInput, 'John')
    
    const lastNameInput = screen.getByPlaceholderText(/last name/i)
    await user.type(lastNameInput, 'Doe')
    
    // Navigate through steps and fill data
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)
    
    // Step 2: Health conditions (select checkbox options)
    await waitFor(() => {
      expect(screen.getByText(/step 2 of/i)).toBeInTheDocument()
    })
    
    // Skip detailed form filling for brevity, but test that submission works
    // Navigate to final step
    await user.click(nextButton)
    await user.click(nextButton)
    await user.click(nextButton)
    
    await waitFor(() => {
      expect(screen.getByText(/step 5 of/i)).toBeInTheDocument()
    })
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should make API call with complete data
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/waitlist', expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: expect.stringContaining('<EMAIL>')
      }))
    })
  })

  it('should show success state after successful submission', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and submit minimal form
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Quick navigation to submit
    for (let i = 0; i < 4; i++) {
      const nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      await waitFor(() => {})
    }
    
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/welcome/i)).toBeInTheDocument()
      expect(screen.getByTestId('confetti')).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    // Mock API error
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        error: 'Email already registered'
      })
    } as Response)
    
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and submit
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Navigate to submit
    for (let i = 0; i < 4; i++) {
      const nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      await waitFor(() => {})
    }
    
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should stay on form and not show success
    await waitFor(() => {
      expect(screen.queryByText(/welcome/i)).not.toBeInTheDocument()
      expect(screen.queryByTestId('confetti')).not.toBeInTheDocument()
    })
  })

  it('should handle network errors gracefully', async () => {
    // Mock network error
    mockFetch.mockRejectedValueOnce(new Error('Network error'))
    
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and submit
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Navigate to submit
    for (let i = 0; i < 4; i++) {
      const nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      await waitFor(() => {})
    }
    
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should handle error gracefully
    await waitFor(() => {
      expect(screen.queryByText(/welcome/i)).not.toBeInTheDocument()
    })
  })

  it('should show loading state during submission', async () => {
    // Mock slow response
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true, id: 'test' })
      } as Response), 100))
    )
    
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and submit
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Navigate to submit
    for (let i = 0; i < 4; i++) {
      const nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      await waitFor(() => {})
    }
    
    const submitButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(submitButton)
    
    // Should show loading state
    expect(screen.getByText(/joining/i)).toBeInTheDocument()
    
    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText(/joining/i)).not.toBeInTheDocument()
    })
  })

  it('should reset form when dialog is closed and reopened', async () => {
    const user = userEvent.setup()
    render(<DetailedWaitlistForm />)
    
    // Open dialog and fill some data
    const triggerButton = screen.getByRole('button', { name: /join waitlist/i })
    await user.click(triggerButton)
    
    const emailInput = screen.getByPlaceholderText(/email address/i)
    await user.type(emailInput, '<EMAIL>')
    
    // Close dialog (simulate by clicking outside or escape)
    fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
    
    // Reopen dialog
    await user.click(triggerButton)
    
    // Should be back to step 1 with empty form
    expect(screen.getByText(/step 1 of/i)).toBeInTheDocument()
    const newEmailInput = screen.getByPlaceholderText(/email address/i)
    expect(newEmailInput).toHaveValue('')
  })
}) 