import { http, HttpResponse } from 'msw'

export const handlers = [
  // Mock successful waitlist submission
  http.post('/api/waitlist', async ({ request }) => {
    const body = await request.json() as any
    
    // Simulate validation failure for specific test cases
    if (body.email === '<EMAIL>') {
      return HttpResponse.json(
        { error: 'Invalid form data' },
        { status: 400 }
      )
    }
    
    // Simulate duplicate email
    if (body.email === '<EMAIL>') {
      return HttpResponse.json(
        { error: 'Email already registered' },
        { status: 400 }
      )
    }
    
    // Simulate server error
    if (body.email === '<EMAIL>') {
      return HttpResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
    
    // Success case
    return HttpResponse.json({
      success: true,
      message: 'Successfully joined waitlist',
      id: 'test-id-123'
    })
  }),

  // Mock waitlist export endpoint
  http.get('/api/waitlist/export', () => {
    return HttpResponse.json({
      exportedAt: new Date().toISOString(),
      totalEntries: 5,
      totalNotifications: 3,
      waitlistEntries: [
        {
          id: 'test-1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          createdAt: new Date().toISOString(),
        }
      ],
      notifications: []
    })
  }),

  // Mock health check endpoint
  http.get('/api/health-check', () => {
    return HttpResponse.json({ status: 'ok', timestamp: new Date().toISOString() })
  }),
] 