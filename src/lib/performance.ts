// Core Web Vitals optimization utilities
export const preloadCriticalResources = () => {
  if (typeof window !== 'undefined') {
    // Preload critical fonts
    const fontLink = document.createElement('link')
    fontLink.rel = 'preload'
    fontLink.href = '/fonts/inter-var.woff2'
    fontLink.as = 'font'
    fontLink.type = 'font/woff2'
    fontLink.crossOrigin = 'anonymous'
    document.head.appendChild(fontLink)
    
    // Preload critical images
    const heroImage = new Image()
    heroImage.src = '/images/hero-bg.webp'
    
    // Prefetch likely next pages
    const prefetchLinks = ['/about', '/contact']
    prefetchLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = href
      document.head.appendChild(link)
    })
  }
}

// Optimize images for Core Web Vitals
export const imageOptimizationConfig = {
  domains: ['localhost', 'theproperhumandiet.com'],
  formats: ['image/webp', 'image/avif'],
  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  minimumCacheTTL: 60,
  dangerouslyAllowSVG: false,
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
}

// Lazy loading configuration
export const lazyLoadingConfig = {
  rootMargin: '50px',
  threshold: 0.1,
} 