import type { WaitlistFormData } from "./validations";

// Import Mailgun using the correct package names
const formData = require("form-data");
const Mailgun = require("mailgun.js");

// Extend WaitlistFormData to include fields from the database entry
export interface WaitlistEntryData extends WaitlistFormData {
  priority?: number;
  id?: number;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
  metadata?: any;
}

// Initialize Mailgun with proper configuration
const MAILGUN_API_KEY = process.env.MAILGUN_API_KEY;
const MAILGUN_SENDER_EMAIL =
  process.env.MAILGUN_SENDER_EMAIL || "<EMAIL>";
// Extract domain from sender email, fallback to environment variable or default
const MAILGUN_DOMAIN =
  process.env.MAILGUN_DOMAIN ||
  MAILGUN_SENDER_EMAIL.split("@")[1] ||
  "mg.theproperhumandiet.net";
const EMAIL_MOCK_MODE = process.env.EMAIL_MOCK_MODE !== "false";
const MAILGUN_API_HOST = process.env.MAILGUN_API_HOST || "api.mailgun.net";

let mailgunClient: any = null;

// Initialize email service
if (!EMAIL_MOCK_MODE && MAILGUN_API_KEY) {
  try {
    const mailgun = new Mailgun(formData);
    mailgunClient = mailgun.client({
      username: "api",
      key: MAILGUN_API_KEY,
      url: `https://${MAILGUN_API_HOST}`,
    });
  } catch (error) {
    console.error("Error initializing Mailgun:", error);
  }
}

export async function sendWelcomeEmail(
  email: string,
  firstName?: string,
): Promise<void> {
  const subject = "Welcome to The Proper Human Diet Waitlist! 🥩";

  const html = `
    <div style="max-width: 600px; margin: 0 auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333;">
      <div style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Welcome ${firstName || "there"}!</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Your health transformation journey begins now</p>
      </div>
      
      <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <p style="font-size: 16px; margin-bottom: 25px;">Thank you for joining The Proper Human Diet waitlist. You're now part of an exclusive community focused on optimal health through the carnivore lifestyle.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h2 style="color: #dc2626; margin: 0 0 15px 0; font-size: 20px;">🎯 What to Expect:</h2>
          <ul style="margin: 0; padding-left: 20px;">
            <li style="margin-bottom: 8px;"><strong>Comprehensive Tracking:</strong> Monitor weight, body fat, blood biomarkers, energy levels, and more</li>
            <li style="margin-bottom: 8px;"><strong>Symptom Management:</strong> Track autoimmune symptoms, mood, digestive health, and inflammation markers</li>
            <li style="margin-bottom: 8px;"><strong>Achievement System:</strong> Earn badges for consistency streaks, health improvements, and community engagement</li>
            <li style="margin-bottom: 8px;"><strong>AI-Powered Insights:</strong> Personalized meal plans and health recommendations</li>
            <li style="margin-bottom: 8px;"><strong>Community Support:</strong> Connect with others on similar health journeys</li>
          </ul>
        </div>
        
        <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #0066cc;">
          <h2 style="color: #0066cc; margin: 0 0 15px 0; font-size: 20px;">🥩 Carnivore Diet Starter Guide:</h2>
          <div style="margin-bottom: 12px;"><strong>Week 1-2:</strong> Focus on simple meats (beef, lamb, fish). Avoid processed foods completely.</div>
          <div style="margin-bottom: 12px;"><strong>Week 3-4:</strong> Add organ meats and experiment with different cuts. Monitor how you feel.</div>
          <div><strong>Month 2+:</strong> Fine-tune based on your body's responses. Track everything!</div>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #16a34a;">
          <h2 style="color: #16a34a; margin: 0 0 15px 0; font-size: 20px;">💪 Health Benefits You Can Expect:</h2>
          <ul style="margin: 0; padding-left: 20px;">
            <li style="margin-bottom: 8px;">Reduced inflammation and autoimmune symptoms</li>
            <li style="margin-bottom: 8px;">Improved energy and mental clarity</li>
            <li style="margin-bottom: 8px;">Better sleep quality and mood stability</li>
            <li style="margin-bottom: 8px;">Weight loss and body composition improvements</li>
            <li style="margin-bottom: 8px;">Enhanced digestive health</li>
          </ul>
        </div>
        
        <div style="background: #fffbeb; padding: 20px; border-radius: 8px; margin: 25px 0; border: 2px dashed #f59e0b;">
          <p style="margin: 0; color: #92400e;"><strong>💡 Pro Tip:</strong> Start tracking your baseline metrics now! Weight, energy levels (1-10), sleep quality, and any symptoms. This will help you see dramatic improvements once you start.</p>
        </div>
        
        <p style="font-size: 16px; margin: 25px 0;">We'll send you weekly tips, recipes, and health insights while you wait for early access.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <div style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%); color: white; padding: 15px 30px; border-radius: 8px; display: inline-block;">
            <p style="margin: 0; font-weight: bold; font-size: 18px;">🚀 Welcome to the Revolution!</p>
          </div>
        </div>
        
        <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px; text-align: center; color: #6b7280;">
          <p style="margin: 0;">To your health,<br><strong style="color: #dc2626;">The Proper Human Diet Team</strong></p>
        </div>
      </div>
    </div>
  `;

  const textVersion = `Welcome ${firstName || "there"}! Thank you for joining The Proper Human Diet waitlist. You're now part of an exclusive community focused on optimal health through the carnivore lifestyle. We'll send you weekly tips, recipes, and health insights while you wait for early access. To your health, The Proper Human Diet Team`;

  if (EMAIL_MOCK_MODE || !mailgunClient) {
    return;
  }

  const messageData = {
    from: `The Proper Human Diet <${MAILGUN_SENDER_EMAIL}>`,
    to: [email],
    subject,
    html,
    text: textVersion,
  };

  try {
    await mailgunClient.messages.create(MAILGUN_DOMAIN, messageData);
  } catch (error) {
    console.error("Error sending welcome email:", error);
    // For auth errors, provide more context
    if (error && (error as any).status === 401) {
      console.error(
        `Mailgun authentication failed. Check API key and domain: ${MAILGUN_DOMAIN}`,
      );
    }
  }
}

export async function sendAdminNotification(
  waitlistData: WaitlistEntryData,
): Promise<void> {
  const subject = `New Waitlist Signup: ${waitlistData.email}`;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333;">
      <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">🎉 New Waitlist Registration</h1>
      </div>
      
      <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="display: grid; gap: 15px;">
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Email:</strong>
            <span>${waitlistData.email}</span>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Name:</strong>
            <span>${waitlistData.firstName || "Not provided"} ${waitlistData.lastName || ""}</span>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Priority Score:</strong>
            <span style="color: #dc2626; font-weight: bold;">${waitlistData.priority || 0}/5</span>
          </div>
          
          <div style="padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Health Conditions:</strong>
            <div style="margin-top: 5px;">${waitlistData.healthConditions?.join(", ") || "None specified"}</div>
          </div>
          
          <div style="padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Dietary Goals:</strong>
            <div style="margin-top: 5px;">${waitlistData.dietaryGoals?.join(", ") || "None specified"}</div>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Current Diet:</strong>
            <span>${waitlistData.currentDiet || "Not specified"}</span>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Monthly Budget:</strong>
            <span>${waitlistData.monthlyBudget || "Not specified"}</span>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Referral Source:</strong>
            <span>${waitlistData.referralSource || "Not specified"}</span>
          </div>
          
          <div style="padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Interests:</strong>
            <div style="margin-top: 5px;">${waitlistData.interests?.join(", ") || "None specified"}</div>
          </div>
          
          <div style="display: flex; justify-content: space-between; padding: 10px; background: #e7f3ff; border-radius: 5px; border-left: 4px solid #3b82f6;">
            <strong>Submitted:</strong>
            <span>${new Date().toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  `;

  const textVersion = `New Waitlist Registration: ${waitlistData.email} - Priority: ${waitlistData.priority}/5`;

  if (EMAIL_MOCK_MODE || !mailgunClient) {
    return;
  }

  const messageData = {
    from: `The Proper Human Diet - Waitlist Bot <${MAILGUN_SENDER_EMAIL}>`,
    to: [process.env.WAITLIST_NOTIFICATION_EMAIL],
    subject,
    html,
    text: textVersion,
  };

  try {
    await mailgunClient.messages.create(MAILGUN_DOMAIN, messageData);
  } catch (error) {
    console.error("Error sending admin notification:", error);
    // For auth errors, provide more context
    if (error && (error as any).status === 401) {
      console.error(
        `Mailgun authentication failed. Check API key and domain: ${MAILGUN_DOMAIN}`,
      );
    }
  }
}
