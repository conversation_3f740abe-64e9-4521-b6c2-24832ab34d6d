import { z } from "zod";

export const waitlistSchema = z.object({
  email: z
    .string({ required_error: "Email is required." })
    .trim()
    .min(1, { message: "Email is required." })
    .email({ message: "Please enter a valid email address." }),
  firstName: z
    .union([z.string(), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined) return null;
      if (typeof val === "string") {
        const trimmed = val.trim();
        return trimmed === "" ? null : trimmed;
      }
      return null;
    }),
  lastName: z
    .union([z.string(), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined) return null;
      if (typeof val === "string") {
        const trimmed = val.trim();
        return trimmed === "" ? null : trimmed;
      }
      return null;
    }),
  healthConditions: z
    .union([z.array(z.string()), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || !Array.isArray(val)) return [];
      return val;
    }),
  dietaryGoals: z
    .union([z.array(z.string()), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || !Array.isArray(val)) return [];
      return val;
    }),
  currentDiet: z
    .union([z.string(), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return undefined;
      return val;
    }),
  referralSource: z
    .union([z.string(), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return undefined;
      return val;
    }),
  interests: z
    .union([z.array(z.string()), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || !Array.isArray(val)) return [];
      return val;
    }),
  monthlyBudget: z
    .union([z.string(), z.null(), z.undefined()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return undefined;
      return val;
    }),
});

export type WaitlistFormData = z.infer<typeof waitlistSchema>;

export const contactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

export type ContactFormData = z.infer<typeof contactSchema>;
