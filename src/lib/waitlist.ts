import { prisma } from './prisma'
import { WaitlistFormData } from './validations'

export interface WaitlistStats {
  totalMembers: number
  weeklyGrowth: number
  averagePriority: number
}

export async function getWaitlistStats(): Promise<WaitlistStats> {
  const totalMembers = await prisma.waitlistEntry.count({
    where: { status: 'ACTIVE' }
  })

  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

  const weeklyGrowth = await prisma.waitlistEntry.count({
    where: {
      status: 'ACTIVE',
      createdAt: {
        gte: oneWeekAgo
      }
    }
  })

  const avgPriority = await prisma.waitlistEntry.aggregate({
    _avg: {
      priority: true
    },
    where: { status: 'ACTIVE' }
  })

  return {
    totalMembers,
    weeklyGrowth,
    averagePriority: avgPriority._avg.priority || 1
  }
}

export function calculatePriorityScore(data: Partial<WaitlistFormData>): number {
  let score = 1
  
  // Higher priority for autoimmune conditions
  if (data.healthConditions?.includes("Autoimmune Disease")) score += 2
  if (data.healthConditions?.includes("Inflammatory Bowel Disease")) score += 2
  
  // Higher priority for experienced carnivore users
  if (data.currentDiet?.includes("Carnivore")) score += 1
  
  // Higher priority for multiple health goals
  if ((data.dietaryGoals?.length || 0) >= 3) score += 1
  
  return Math.min(score, 5)
}
