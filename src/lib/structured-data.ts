interface OrganizationSchema {
  "@context": string
  "@type": string
  name: string
  url: string
  logo: string
  description: string
  foundingDate: string
  founders: Array<{
    "@type": string
    name: string
  }>
  sameAs: string[]
  contactPoint: {
    "@type": string
    contactType: string
    email: string
    availableLanguage: string[]
  }
}

interface WebsiteSchema {
  "@context": string
  "@type": string
  name: string
  url: string
  description: string
  publisher: {
    "@type": string
    name: string
    logo: {
      "@type": string
      url: string
    }
  }
  potentialAction: {
    "@type": string
    target: {
      "@type": string
      urlTemplate: string
    }
    "query-input": string
  }
}

interface WebApplicationSchema {
  "@context": string
  "@type": string
  name: string
  description: string
  url: string
  applicationCategory: string
  operatingSystem: string[]
  offers: {
    "@type": string
    price: string
    priceCurrency: string
    availability: string
    validFrom: string
  }
  aggregateRating: {
    "@type": string
    ratingValue: string
    ratingCount: string
    bestRating: string
  }
  featureList: string[]
  screenshot: string[]
  icon: string
}

interface HealthAndWellnessSchema {
  "@context": string
  "@type": string
  name: string
  description: string
  category: string[]
  healthCondition: string[]
  dietType: string
  benefits: string[]
  suitableFor: string[]
}

export function generateOrganizationSchema(): OrganizationSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Proper Human Diet",
    "url": baseUrl,
    "logo": `${baseUrl}/tphd-transparent.png`,
    "description": "AI-powered carnivore diet and health tracking platform helping people achieve optimal wellness through comprehensive health monitoring and community support.",
    "foundingDate": "2024",
    "founders": [
      {
        "@type": "Person",
        "name": "The Proper Human Diet Team"
      }
    ],
    "sameAs": [
      "https://twitter.com/ProperHumanDiet",
      "https://facebook.com/ProperHumanDiet",
      "https://instagram.com/ProperHumanDiet"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"]
    }
  }
}

export function generateWebsiteSchema(): WebsiteSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "The Proper Human Diet",
    "url": baseUrl,
    "description": "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    "publisher": {
      "@type": "Organization",
      "name": "The Proper Human Diet",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/tphd-transparent.png`
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }
}

export function generateWebApplicationSchema(): WebApplicationSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "The Proper Human Diet Platform",
    "description": "Comprehensive carnivore diet and health tracking platform with AI-powered insights, community support, and achievement system.",
    "url": baseUrl,
    "applicationCategory": "HealthApplication",
    "operatingSystem": ["Web Browser", "iOS", "Android"],
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/ComingSoon",
      "validFrom": "2024-01-01"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "1247",
      "bestRating": "5"
    },
    "featureList": [
      "Comprehensive health metrics tracking",
      "AI-powered meal planning",
      "Achievement and streak system",
      "Community support forums",
      "Advanced analytics and insights",
      "Autoimmune symptom management",
      "Weight and body composition tracking",
      "Sleep and energy monitoring"
    ],
    "screenshot": [
      `${baseUrl}/images/dashboard-screenshot.png`,
      `${baseUrl}/images/tracking-screenshot.png`,
      `${baseUrl}/images/community-screenshot.png`
    ],
    "icon": `${baseUrl}/android-chrome-512x512.png`
  }
}

export function generateHealthAndWellnessSchema(): HealthAndWellnessSchema {
  return {
    "@context": "https://schema.org",
    "@type": "Diet",
    "name": "Carnivore Diet Tracking and Support",
    "description": "Comprehensive platform for tracking and optimizing the carnivore diet for health improvement, particularly for autoimmune conditions.",
    "category": [
      "Carnivore Diet",
      "Health Tracking",
      "Nutrition Monitoring",
      "Wellness Platform"
    ],
    "healthCondition": [
      "Autoimmune Disease",
      "Inflammatory Bowel Disease",
      "Arthritis",
      "Digestive Issues",
      "Chronic Inflammation",
      "Weight Management"
    ],
    "dietType": "Carnivore",
    "benefits": [
      "Reduced inflammation",
      "Improved energy levels",
      "Better sleep quality",
      "Weight loss",
      "Digestive health improvement",
      "Mental clarity enhancement",
      "Autoimmune symptom reduction"
    ],
    "suitableFor": [
      "People with autoimmune conditions",
      "Individuals seeking weight loss",
      "Those with digestive issues",
      "People interested in elimination diets",
      "Health optimization enthusiasts"
    ]
  }
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }
}

export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}
