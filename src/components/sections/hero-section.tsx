"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ArrowRight, Play, TrendingUp, Heart, Brain, Users, Zap, Sparkles, Star, Target } from "lucide-react"
import { motion } from "framer-motion"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"

// TypeScript interfaces for mobile-optimized content
interface StoryPoint {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
  mobileDescription: string
  gradient: string
}

interface DemoData {
  tracking: {
    title: string
    subtitle: string
    metrics: Array<{
      label: string
      value: string
      change: string
      trend: string
      color: string
    }>
  }
  progress: {
    title: string
    subtitle: string
    achievements: Array<{
      name: string
      progress: number
      status: string
      description: string
    }>
  }
  community: {
    title: string
    subtitle: string
    stats: Array<{
      label: string
      value: string
      description: string
    }>
  }
}

export function HeroSection() {
  const [activeDemo, setActiveDemo] = useState<'tracking' | 'progress' | 'community'>('tracking')

  const demoData = {
    tracking: {
      title: "AI-Powered Insights",
      subtitle: "Real-time health intelligence",
      metrics: [
        { label: "Energy", value: "Tracked", change: "↗ Improving", trend: "up", color: "text-orange-500" },
        { label: "Symptoms", value: "Monitored", change: "↘ Reducing", trend: "up", color: "text-blue-500" },
        { label: "Mood", value: "Tracked", change: "↗ Trending up", trend: "up", color: "text-green-500" },
        { label: "Sleep", value: "Monitored", change: "↗ Better quality", trend: "up", color: "text-purple-500" },
      ]
    },
    progress: {
      title: "Smart Achievements",
      subtitle: "AI celebrates your wins",
      achievements: [
        { name: "First Week", progress: 0, status: "pending", description: "Coming soon!" },
        { name: "Energy Boost", progress: 0, status: "pending", description: "Track to unlock" },
        { name: "Pattern Found", progress: 0, status: "pending", description: "AI will discover" },
        { name: "Goal Reached", progress: 0, status: "pending", description: "Set your goals" },
      ]
    },
    community: {
      title: "AI Knowledge Hub",
      subtitle: "Learn from everything",
      stats: [
        { label: "Books", value: "Coming", description: "Health books to analyze" },
        { label: "Videos", value: "Soon", description: "YouTube intelligence" },
        { label: "Insights", value: "24/7", description: "AI chatbot ready" },
        { label: "Users", value: "You", description: "Be the first!" },
      ]
    }
  }

  const storyPoints: StoryPoint[] = [
    {
      icon: Users,
      title: "Share Stories",
      description: "Connect with others on similar health journeys.",
      mobileDescription: "Connect & share",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Heart,
      title: "Track Smart",
      description: "Food, symptoms, mood - all connected with insights.",
      mobileDescription: "Smart tracking",
      gradient: "from-red-500 to-pink-500"
    },
    {
      icon: Brain,
      title: "AI Support",
      description: "Get personalized health insights when you need them.",
      mobileDescription: "AI insights",
      gradient: "from-purple-500 to-indigo-500"
    },
    {
      icon: Zap,
      title: "Learn Fast",
      description: "Health books and videos summarized instantly.",
      mobileDescription: "Quick learning",
      gradient: "from-yellow-500 to-orange-500"
    }
  ]

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-2xl animate-pulse" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary/20 to-primary/20 rounded-full blur-2xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-full blur-3xl" />
      
      <div className="container relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Enhanced Content */}
          <motion.div 
            className="space-y-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Badge variant="outline" className="w-fit bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
                  <Sparkles className="h-3 w-3 mr-1" />
                  ✨ Your Health Revolution Starts Here
                </Badge>
              </motion.div>
              
              <motion.h1 
                className="text-3xl sm:text-4xl lg:text-6xl font-bold tracking-tight leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <span className="block">
                  <span className="hidden sm:inline">Transform Your Health with</span>
                  <span className="sm:hidden">Health Transformation</span>
                </span>
                <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent block mt-1 sm:mt-2">
                  <span className="hidden sm:inline">AI-Powered Tracking</span>
                  <span className="sm:hidden">Made Simple</span>
                </span>
              </motion.h1>
              
              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {/* Mobile-optimized subtitle */}
                <p className="text-lg sm:text-xl text-muted-foreground max-w-lg leading-relaxed">
                  <span className="hidden sm:inline">
                    Share your health journey with a supportive community. 
                    <strong>Track everything</strong>, <strong>learn from others</strong>, and get AI-powered insights to transform your health.
                  </span>
                  <span className="sm:hidden">
                    Join a supportive community. <strong>Track smart</strong>, <strong>learn together</strong>, get AI insights.
                  </span>
                </p>
              </motion.div>
            </div>

            {/* Enhanced Story Points - Mobile Optimized */}
            <motion.div 
              className="grid grid-cols-2 gap-3 sm:gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              {storyPoints.map((point, index) => (
                <motion.div
                  key={point.title}
                  className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-background/50 backdrop-blur border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:scale-105"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className={`p-1.5 sm:p-2 bg-gradient-to-r ${point.gradient} rounded-lg flex-shrink-0`}>
                    <point.icon className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-semibold text-xs sm:text-sm truncate">{point.title}</div>
                    <div className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
                      <span className="hidden sm:inline">{point.description}</span>
                      <span className="sm:hidden">{point.mobileDescription}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div 
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <DetailedWaitlistForm 
                trigger={
                  <Button size="lg" className="group bg-gradient-to-r from-primary via-primary to-primary/90 hover:from-primary/90 hover:via-primary hover:to-primary w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 shadow-xl hover:shadow-2xl transition-all duration-300">
                    <Star className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 animate-pulse" />
                    <span className="hidden sm:inline">Begin Your Transformation</span>
                    <span className="sm:hidden">Join Community</span>
                    <ArrowRight className="ml-1 sm:ml-2 h-4 w-4 sm:h-5 sm:w-5 transition-transform group-hover:translate-x-1" />
                  </Button>
                }
              />
              <Link href="/vision">
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="group border-primary/20 hover:border-primary/40 text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto"
                >
                  <Play className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden sm:inline">Watch Our Vision</span>
                  <span className="sm:hidden">Our Vision</span>
                </Button>
              </Link>
            </motion.div>

            <motion.div 
              className="flex items-center space-x-4 sm:space-x-8 text-xs sm:text-sm text-muted-foreground pt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
            >
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse flex-shrink-0"></div>
                <div>
                  <div className="font-semibold text-foreground">Health Pioneer</div>
                  <div className="hidden sm:block">Shape the future of health</div>
                  <div className="sm:hidden">Shape the future</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-500 flex-shrink-0"></div>
                <div>
                  <div className="font-semibold text-foreground">Early Access</div>
                  <div className="hidden sm:block">First to experience magic</div>
                  <div className="sm:hidden">First access</div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Interactive Demo */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="space-y-6">
              {/* Enhanced Demo Tabs - Mobile Optimized */}
              <div className="flex space-x-1 sm:space-x-2 bg-muted/50 p-1 sm:p-1.5 rounded-xl backdrop-blur">
                {(['tracking', 'progress', 'community'] as const).map((tab, index) => (
                  <motion.button
                    key={tab}
                    onClick={() => setActiveDemo(tab)}
                    className={`flex-1 px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium rounded-lg transition-all duration-300 ${
                      activeDemo === tab 
                        ? 'bg-background shadow-lg text-foreground border border-primary/20' 
                        : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="hidden sm:inline">{tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
                    <span className="sm:hidden">
                      {tab === 'tracking' && 'Track'}
                      {tab === 'progress' && 'Goals'}
                      {tab === 'community' && 'Learn'}
                    </span>
                  </motion.button>
                ))}
              </div>

              {/* Enhanced Demo Content - Mobile Optimized */}
              <Card className="border-0 shadow-2xl bg-background/95 backdrop-blur-xl overflow-hidden">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <motion.div
                    key={activeDemo}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="text-center mb-4 sm:mb-6">
                      <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-1 sm:mb-2">{demoData[activeDemo].title}</h3>
                      <p className="text-sm sm:text-base text-muted-foreground">{demoData[activeDemo].subtitle}</p>
                    </div>
                    
                    {activeDemo === 'tracking' && (
                      <div className="space-y-4">
                        {demoData.tracking.metrics.map((metric, index) => (
                          <motion.div
                            key={metric.label}
                            className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-muted/30 to-muted/10 hover:from-muted/50 hover:to-muted/20 transition-all duration-300"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <div>
                              <div className="font-semibold flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${metric.color} bg-current animate-pulse`}></div>
                                {metric.label}
                              </div>
                              <div className="text-sm text-muted-foreground">{metric.value}</div>
                            </div>
                            <div className="text-right">
                              <div className={`text-sm font-medium ${metric.color}`}>
                                {metric.change}
                              </div>
                              <TrendingUp className={`h-4 w-4 ${metric.color} ml-auto`} />
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                    
                    {activeDemo === 'progress' && (
                      <div className="space-y-4">
                        {demoData.progress.achievements.map((achievement, index) => (
                          <motion.div
                            key={achievement.name}
                            className="space-y-3 p-4 rounded-lg bg-gradient-to-r from-muted/30 to-muted/10"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <span className="font-semibold">{achievement.name}</span>
                                <div className="text-xs text-muted-foreground">{achievement.description}</div>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-bold">{achievement.progress}%</span>
                                {achievement.status === 'completed' && (
                                  <Target className="h-4 w-4 text-green-500" />
                                )}
                              </div>
                            </div>
                            <Progress value={achievement.progress} className="h-3" />
                          </motion.div>
                        ))}
                      </div>
                    )}
                    
                    {activeDemo === 'community' && (
                      <div className="grid grid-cols-2 gap-4">
                        {demoData.community.stats.map((stat, index) => (
                          <motion.div
                            key={stat.label}
                            className="text-center p-4 bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg border border-primary/10"
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.05 }}
                          >
                            <div className="text-lg font-bold text-primary mb-1">{stat.value}</div>
                            <div className="text-sm font-medium mb-1">{stat.label}</div>
                            <div className="text-xs text-muted-foreground">{stat.description}</div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </motion.div>
                </CardContent>
              </Card>

              {/* Call to Action in Demo - Mobile Optimized */}
              <motion.div
                className="text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <p className="text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-3">
                  <span className="hidden sm:inline">Your health community awaits.</span>
                  <span className="sm:hidden">Join the community.</span>
                </p>
                <DetailedWaitlistForm 
                  trigger={
                    <Button variant="outline" className="bg-background/80 backdrop-blur border-primary/20 hover:border-primary/40 text-sm px-4 py-2">
                      <Heart className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Start Your Journey</span>
                      <span className="sm:hidden">Start Journey</span>
                    </Button>
                  }
                />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
