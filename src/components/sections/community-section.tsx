"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Users, 
  MessageCircle, 
  Heart, 
  Star, 
  BookOpen,
  Award,
  Calendar,
  UserCheck,
  ArrowRight,
  Sparkles,
  Zap,
  Brain,
  Activity
} from "lucide-react"

const features = [
  {
    icon: MessageCircle,
    title: "Real-Time Chat & Forums",
    description: "Connect instantly with others on similar health journeys through our integrated chat system and discussion forums.",
    focus: "Live conversations"
  },
  {
    icon: BookOpen,
    title: "Knowledge Sharing Hub",
    description: "Share recipes, tips, research findings, and personal experiences across various dietary approaches.",
    focus: "Collaborative learning"
  },
  {
    icon: Users,
    title: "Support Groups",
    description: "Join focused groups based on health goals, dietary preferences, or specific conditions for targeted support.",
    focus: "Targeted communities"
  },
  {
    icon: Calendar,
    title: "Virtual Events & Workshops",
    description: "Participate in live Q&As, educational sessions, and group challenges to accelerate your journey.",
    focus: "Interactive learning"
  }
]

const buildingTogether = [
  {
    name: "Health Enthusiasts",
    role: "Exploring Optimal Nutrition",
    avatar: "/avatars/health-enthusiast.jpg",
    badge: "Early Adopter",
    quote: "Excited to be part of building a platform where we can truly learn from each other's experiences.",
    verified: true
  },
  {
    name: "Wellness Advocates",
    role: "Community Builders",
    avatar: "/avatars/wellness-advocate.jpg",
    badge: "Founding Member",
    quote: "Looking forward to creating a supportive space where everyone's health journey is valued and supported.",
    verified: true
  },
  {
    name: "Research Contributors",
    role: "Evidence-Based Approach",
    avatar: "/avatars/researcher.jpg",
    badge: "Knowledge Sharer",
    quote: "Can't wait to share the latest research and learn from real-world experiences in our community.",
    verified: true
  }
]

const communityGoals = [
  { icon: MessageCircle, label: "Real-Time Support", description: "24/7 peer connections" },
  { icon: Brain, label: "Shared Learning", description: "Collective knowledge building" },
  { icon: Heart, label: "Inclusive Environment", description: "All dietary approaches welcome" },
  { icon: Zap, label: "Rapid Innovation", description: "Community-driven features" }
]

const chatFeatures = [
  {
    feature: "Direct messaging for personal support",
    benefit: "Get help when you need it most"
  },
  {
    feature: "Group chats for specific topics",
    benefit: "Deep dive into areas of interest"
  },
  {
    feature: "Expert office hours",
    benefit: "Access to professional guidance"
  },
  {
    feature: "Progress sharing channels",
    benefit: "Celebrate wins together"
  }
]

export function CommunitySection() {
  return (
    <section id="community" className="py-24 bg-muted/30">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="bg-background">
            👥 Community
          </Badge>
          <h2 className="text-4xl font-bold tracking-tight">
            Building Our{" "}
            <span className="text-primary">Community</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join us in creating a supportive space where people exploring health, nutrition, 
            and wellness can connect, share, and grow together.
          </p>
        </motion.div>

        {/* Community Goals Grid */}
        <motion.div 
          className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          {communityGoals.map((goal, index) => (
            <Card key={goal.label} className="text-center border-0 shadow-lg bg-background/60 backdrop-blur">
              <CardContent className="p-6">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                  <goal.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="text-sm font-medium mb-1">{goal.label}</div>
                <div className="text-xs text-muted-foreground">{goal.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-background/60 backdrop-blur">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-primary/10 rounded-lg">
                      <feature.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                      <Badge variant="secondary" className="mt-1 text-xs">
                        {feature.focus}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16">
          {/* Building Together */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold mb-6">Building Together</h3>
            <div className="space-y-6">
              {buildingTogether.map((person, index) => (
                <Card key={person.name} className="border-0 shadow-md bg-background/60 backdrop-blur">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={person.avatar} alt={person.name} />
                        <AvatarFallback>{person.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold">{person.name}</h4>
                          {person.verified && (
                            <UserCheck className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{person.role}</p>
                        <Badge variant="outline" className="mt-1 text-xs">
                          {person.badge}
                        </Badge>
                      </div>
                    </div>
                    <blockquote className="text-sm italic leading-relaxed">
                      "{person.quote}"
                    </blockquote>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Chat Features */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold mb-6">Chat & Communication Features</h3>
            <Card className="border-0 shadow-lg bg-background/60 backdrop-blur">
              <CardContent className="p-6">
                <div className="space-y-6">
                  {chatFeatures.map((item, index) => (
                    <div key={index} className="flex items-start gap-3 p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                      <div className="p-2 bg-primary/10 rounded-lg mt-1">
                        <MessageCircle className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm mb-1">{item.feature}</div>
                        <p className="text-sm text-muted-foreground">{item.benefit}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 pt-4 border-t">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground mb-3">
                      Real-time communication for instant support and connection
                    </p>
                    <Badge variant="outline" className="bg-primary/5">
                      <Activity className="h-3 w-3 mr-1" />
                      Coming Soon
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Join CTA */}
        <motion.div 
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/10 to-primary/5">
            <CardContent className="p-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
                <Sparkles className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold mb-4">Help Us Build Something Amazing</h3>
              <p className="text-muted-foreground text-lg mb-6 max-w-2xl mx-auto">
                Be part of creating a platform designed by the community, for the community. 
                Your input will shape how we support each other's health journeys.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="gap-2">
                  Join the Waitlist
                  <Users className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="lg" className="gap-2">
                  Learn More
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
