"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  Brain, 
  Users, 
  Heart, 
  Zap, 
  Shield,
  Activity,
  TrendingUp,
  MessageCircle,
  Sparkles,
  Star,
  Target
} from "lucide-react"

const features = [
  {
    icon: BarChart3,
    title: "Your Health Story Unfolds",
    description: "Watch your transformation happen in real-time as you track 25+ health metrics that paint the complete picture of your wellness journey.",
    benefits: ["See patterns emerge", "Celebrate daily wins", "Track what matters to YOU"],
    badge: "Core Feature",
    story: "Imagine opening the app each morning and seeing your energy levels climbing, your sleep improving, and your confidence soaring—all visualized in beautiful, easy-to-understand charts.",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    icon: Brain,
    title: "Your Personal Health Detective",
    description: "Our <PERSON> becomes your health detective, learning your unique patterns and uncovering insights that help you make breakthrough discoveries about your body.",
    benefits: ["Spot hidden connections", "Get personalized guidance", "Predict what works"],
    badge: "AI-Powered",
    story: "Picture having a brilliant health coach who never sleeps, constantly analyzing your data to whisper, 'Try this' or 'Avoid that' at just the right moment.",
    gradient: "from-purple-500 to-indigo-500"
  },
  {
    icon: Users,
    title: "Your Tribe of Transformation",
    description: "Connect with fellow health heroes who understand your struggles and celebrate your victories, creating bonds that last beyond your health journey.",
    benefits: ["Find your people", "Share breakthroughs", "Get instant support"],
    badge: "Community-Driven",
    story: "Envision having a group of friends who truly get it—people who cheer when you hit a milestone and lift you up when you need encouragement.",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    icon: Heart,
    title: "Mind, Body, Soul Harmony",
    description: "Because true health isn't just about numbers—it's about feeling alive, confident, and at peace with yourself every single day.",
    benefits: ["Emotional wellness tracking", "Stress pattern recognition", "Holistic health view"],
    badge: "Holistic",
    story: "Think about waking up not just physically healthy, but emotionally balanced, mentally sharp, and spiritually aligned—that's the harmony we're building.",
    gradient: "from-red-500 to-pink-500"
  },
  {
    icon: Zap,
    title: "Lightning-Fast Insights",
    description: "Get instant feedback that helps you make smart decisions in the moment, turning every choice into an opportunity for better health.",
    benefits: ["Real-time notifications", "Smart suggestions", "Immediate feedback"],
    badge: "Instant",
    story: "Imagine getting a gentle nudge right when you need it: 'Your energy dips around 3 PM—try a 5-minute walk' or 'You sleep better when you eat dinner before 7 PM.'",
    gradient: "from-yellow-500 to-orange-500"
  },
  {
    icon: Shield,
    title: "Your Data, Your Rules",
    description: "Your health story is sacred. We protect it with military-grade security while giving you complete control over who sees what.",
    benefits: ["Bank-level encryption", "You own your data", "Privacy by design"],
    badge: "Secure",
    story: "Rest easy knowing your most personal information is safer than money in a bank vault, yet always accessible when you need it.",
    gradient: "from-gray-600 to-gray-800"
  }
]

const transformationStories = [
  { 
    icon: Activity, 
    title: "Data Becomes Wisdom", 
    description: "Transform overwhelming health information into clear, actionable insights",
    metric: "Clarity"
  },
  { 
    icon: Users, 
    title: "Isolation Becomes Connection", 
    description: "Turn your health journey from a lonely struggle into a shared adventure",
    metric: "Community"
  },
  { 
    icon: Brain, 
    title: "Confusion Becomes Confidence", 
    description: "Replace guesswork with personalized guidance based on your unique patterns",
    metric: "Confidence"
  },
  { 
    icon: Heart, 
    title: "Surviving Becomes Thriving", 
    description: "Move beyond just 'getting by' to truly flourishing in all aspects of life",
    metric: "Vitality"
  }
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-muted/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      <div className="absolute top-20 right-20 w-40 h-40 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-20 left-20 w-32 h-32 bg-gradient-to-r from-secondary/10 to-primary/10 rounded-full blur-2xl animate-pulse delay-1000" />
      
      <div className="container relative">
        <motion.div 
          className="text-center space-y-6 mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="bg-background border-primary/20">
            <Sparkles className="h-3 w-3 mr-1" />
            ✨ Platform Features
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold tracking-tight">
            <span className="block">Building the Future of</span>
            <span className="text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent block mt-2">
              Health Transformation
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Every feature we're building has one purpose: to help you write the most amazing health story of your life. 
            Here's how we'll make that happen, together.
          </p>
        </motion.div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-background/60 backdrop-blur group hover:scale-105">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-4 bg-gradient-to-r ${feature.gradient} rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs font-medium">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors duration-300">
                    {feature.title}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg">
                    <p className="text-sm italic text-muted-foreground leading-relaxed">
                      "{feature.story}"
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold flex items-center gap-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      What This Means for You:
                    </h4>
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <motion.div 
                        key={benefitIndex} 
                        className="flex items-center gap-2 text-sm"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: (index * 0.1) + (benefitIndex * 0.05) }}
                        viewport={{ once: true }}
                      >
                        <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${feature.gradient}`} />
                        <span className="text-muted-foreground">{benefit}</span>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Transformation Stories */}
        <motion.div 
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {transformationStories.map((story, index) => (
            <motion.div
              key={story.title}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center border-0 shadow-md bg-background/60 backdrop-blur hover:shadow-lg transition-all duration-300 hover:scale-105">
                <CardContent className="p-6 space-y-4">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full">
                    <story.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="text-sm font-bold text-primary mb-1">{story.metric}</div>
                    <div className="text-xs font-medium mb-2">{story.title}</div>
                    <div className="text-xs text-muted-foreground leading-relaxed">{story.description}</div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Vision Statement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-primary/5 via-background to-secondary/5 overflow-hidden">
            <CardContent className="p-12 text-center relative">
              {/* Background decoration */}
              <div className="absolute top-0 left-0 w-full h-full opacity-10">
                <div className="absolute top-10 left-10 w-20 h-20 bg-primary rounded-full blur-2xl animate-pulse" />
                <div className="absolute bottom-10 right-10 w-16 h-16 bg-secondary rounded-full blur-xl animate-pulse delay-1000" />
              </div>
              
              <div className="relative z-10 space-y-8">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-primary to-secondary rounded-full mb-6">
                  <MessageCircle className="h-10 w-10 text-white" />
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-3xl lg:text-4xl font-bold">
                    <span className="block">Join Us in Building</span>
                    <span className="text-primary bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent block mt-2">
                      Something Revolutionary
                    </span>
                  </h3>
                  <p className="text-lg text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
                    This isn't just another health app. We're creating a movement where technology serves humanity, 
                    where your voice shapes the future, and where every person's health journey matters. 
                    Your story will help us build features that could change millions of lives.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                  <motion.div
                    className="p-6 rounded-xl bg-background/50 backdrop-blur border border-primary/10"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Badge variant="outline" className="mb-3 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800">
                      <Users className="h-3 w-3 mr-1" />
                      Community-Driven
                    </Badge>
                    <p className="text-sm text-muted-foreground">
                      Your feedback shapes every feature. We build what you need, not what we think you need.
                    </p>
                  </motion.div>
                  
                  <motion.div
                    className="p-6 rounded-xl bg-background/50 backdrop-blur border border-primary/10"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Badge variant="outline" className="mb-3 bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border-purple-200 dark:border-purple-800">
                      <Brain className="h-3 w-3 mr-1" />
                      Research-Informed
                    </Badge>
                    <p className="text-sm text-muted-foreground">
                      Every feature is backed by the latest health research and real-world results.
                    </p>
                  </motion.div>
                  
                  <motion.div
                    className="p-6 rounded-xl bg-background/50 backdrop-blur border border-primary/10"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Badge variant="outline" className="mb-3 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-green-200 dark:border-green-800">
                      <Heart className="h-3 w-3 mr-1" />
                      Inclusive & Supportive
                    </Badge>
                    <p className="text-sm text-muted-foreground">
                      All health approaches welcome. Your journey is unique, and we celebrate that.
                    </p>
                  </motion.div>
                </div>

                <motion.div
                  className="pt-6"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  viewport={{ once: true }}
                >
                  <p className="text-sm text-muted-foreground mb-4">
                    Ready to be part of something bigger than yourself?
                  </p>
                  <div className="flex items-center justify-center gap-2">
                    <Target className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">Your transformation starts with one click</span>
                  </div>
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
