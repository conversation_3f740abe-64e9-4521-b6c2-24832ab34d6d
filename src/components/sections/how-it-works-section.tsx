"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  UserPlus, 
  Brain, 
  Target, 
  TrendingUp, 
  ArrowRight,
  CheckCircle,
  Smartphone,
  Users
} from "lucide-react"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"

const steps = [
  {
    step: 1,
    icon: UserPlus,
    title: "<PERSON>in & Assess",
    description: "Complete our comprehensive health assessment to understand your current state, goals, and begin your personal health journey story.",
    details: [
      "Health condition evaluation",
      "Dietary history analysis", 
      "Goal setting & prioritization",
      "Start your story timeline"
    ],
    duration: "5 minutes"
  },
  {
    step: 2,
    icon: Brain,
    title: "AI Personalization",
    description: "Our AI analyzes your profile and creates a customized carnivore approach while connecting you with others on similar journeys.",
    details: [
      "Personalized meal plans",
      "Community-matched insights",
      "Smart tracking priorities",
      "Learn from others' success stories"
    ],
    duration: "Instant"
  },
  {
    step: 3,
    icon: Target,
    title: "Track & Share",
    description: "Track your food intake, monitor symptoms, and share your progress story with the community for support and inspiration.",
    details: [
      "Food & symptom tracking",
      "Progress story updates",
      "Community interactions",
      "Learn from diet insights"
    ],
    duration: "2 min/day"
  },
  {
    step: 4,
    icon: TrendingUp,
    title: "Inspire Others",
    description: "Celebrate your health transformation and inspire others by sharing your success story with the community.",
    details: [
      "Share your success story",
      "Inspire fellow members",
      "Celebrate milestones together",
      "Build lasting connections"
    ],
    duration: "Ongoing"
  }
]

const benefits = [
  {
    icon: Smartphone,
    title: "Mobile-First Experience",
    description: "Track anywhere, anytime with our responsive design and upcoming mobile apps."
  },
  {
    icon: Brain,
    title: "AI-Powered Insights",
    description: "Get personalized recommendations based on your unique health data patterns."
  },
  {
    icon: Users,
    title: "Share Your Story",
    description: "Connect with others, share your health journey, and inspire fellow members with your transformation story."
  },
  {
    icon: CheckCircle,
    title: "Learn Together",
    description: "Discover insights about food and diet from real community experiences and expert knowledge."
  }
]

export function HowItWorksSection() {
  return (
    <section id="how-it-works" className="py-24 bg-muted/30">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="bg-background">
            🎯 Simple Process
          </Badge>
          <h2 className="text-4xl font-bold tracking-tight">
            Track, Learn, Share Your{" "}
            <span className="text-primary">Health Story</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join a vibrant community where you'll track your food and symptoms, learn from others, and share your inspiring health transformation story.
          </p>
        </motion.div>

        {/* Steps */}
        <div className="relative md:flex md:flex-col md:items-center space-y-12">
          {/* Vertical line */}
          <div className="absolute left-1/2 -translate-x-1/2 top-6 bottom-6 w-0.5 bg-border hidden md:block" />
          
          {steps.map((step, index) => (
            <motion.div
              key={step.step}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="md:w-1/2 md:odd:self-start md:even:self-end md:odd:pr-8 md:even:pl-8"
            >
              <div className="relative">
                <div className="md:absolute top-1/2 -translate-y-1/2 bg-background p-1 rounded-full border hidden md:block 
                              md:odd:right-0 md:odd:translate-x-[calc(50%+1.05rem)] 
                              md:even:left-0 md:even:-translate-x-[calc(50%+1.05rem)]">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <step.icon className="h-5 w-5 text-primary" />
                  </div>
                </div>
                
                <Card className="border-0 shadow-lg bg-background/60 backdrop-blur w-full">
                  <CardContent className="p-6">
                    <div className="flex items-start md:items-center gap-4 mb-4">
                      <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-full text-primary-foreground font-bold shrink-0 md:hidden">
                        {step.step}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold">{step.title}</h3>
                        <Badge variant="secondary" className="mt-1">
                          {step.duration}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      {step.description}
                    </p>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
                      {step.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 shrink-0" />
                          <span className="text-sm">{detail}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Benefits Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="text-center my-12">
            <h3 className="text-3xl font-bold mb-4">Why Join Our Waitlist?</h3>
            <p className="text-muted-foreground text-lg">
              Be among the first to experience the ultimate platform for tracking, learning, and sharing your health journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center border-0 shadow-lg bg-background/60 backdrop-blur h-full">
                  <CardContent className="p-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                      <benefit.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h4 className="font-semibold mb-2">{benefit.title}</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Health Journey?</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Get exclusive early access to the most comprehensive platform for tracking food, symptoms, and sharing your inspiring health story with a supportive community.
            </p>
            <DetailedWaitlistForm 
              trigger={
                <Button size="lg" className="gap-2">
                  Join the Waitlist
                  <ArrowRight className="h-4 w-4" />
                </Button>
              }
            />
          </div>
        </motion.div>
      </div>
    </section>
  )
}
