"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  Brain, 
  Zap, 
  Shield, 
  TrendingDown,
  Activity,
  Moon,
  Smile,
  Scale,
  FlameKindling
} from "lucide-react"

const benefits = [
  {
    icon: Heart,
    title: "Cardiovascular Health",
    description: "Research suggests proper nutrition can support heart health, blood pressure, and cholesterol balance.",
    timeframe: "Varies by individual",
    color: "text-red-500"
  },
  {
    icon: Brain,
    title: "Mental Clarity",
    description: "Many report enhanced cognitive function, reduced brain fog, and improved focus with dietary optimization.",
    timeframe: "Varies by individual",
    color: "text-purple-500"
  },
  {
    icon: Zap,
    title: "Energy Levels",
    description: "Stable energy throughout the day without crashes through balanced nutrition approaches.",
    timeframe: "Varies by individual",
    color: "text-yellow-500"
  },
  {
    icon: Shield,
    title: "Inflammation Support",
    description: "Certain dietary approaches may help support the body's natural inflammatory response.",
    timeframe: "Varies by individual",
    color: "text-green-500"
  },
  {
    icon: Scale,
    title: "Weight Management",
    description: "Sustainable approaches to achieving and maintaining healthy weight through lifestyle changes.",
    timeframe: "Varies by individual",
    color: "text-blue-500"
  },
  {
    icon: Moon,
    title: "Sleep Quality",
    description: "Nutrition and lifestyle changes may support better sleep patterns and recovery.",
    timeframe: "Varies by individual",
    color: "text-indigo-500"
  }
]

const researchAreas = [
  { name: "Ketogenic Diets", focus: "Metabolic health research" },
  { name: "Elimination Diets", focus: "Food sensitivity studies" },
  { name: "Anti-Inflammatory Nutrition", focus: "Chronic condition support" },
  { name: "Circadian Rhythm", focus: "Sleep and energy optimization" },
  { name: "Gut Health", focus: "Microbiome research" },
  { name: "Autoimmune Protocols", focus: "Symptom management studies" },
  { name: "Personalized Nutrition", focus: "Individual response tracking" },
  { name: "Stress & Diet", focus: "Mind-body connection research" }
]

export function HealthBenefitsSection() {
  return (
    <section id="health-benefits" className="py-24 bg-background">
      <div className="container">
        <motion.div 
          className="text-center space-y-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="bg-muted">
            🏥 Health & Wellness
          </Badge>
          <h2 className="text-4xl font-bold tracking-tight">
            Potential Health{" "}
            <span className="text-primary">Benefits</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our platform will help you explore various dietary approaches and track your personal 
            health journey to discover what works best for your unique needs.
          </p>
        </motion.div>

        {/* Main Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-background/60 backdrop-blur">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-muted rounded-lg">
                      <benefit.icon className={`h-6 w-6 ${benefit.color}`} />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {benefit.timeframe}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl">{benefit.title}</CardTitle>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {benefit.description}
                  </p>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Research Areas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mb-20"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Areas We'll Explore Together</h3>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Our platform will support various dietary approaches and health optimization strategies based on emerging research.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {researchAreas.map((area, index) => (
              <motion.div
                key={area.name}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                viewport={{ once: true }}
              >
                <Card className="p-6 border-0 shadow-md bg-muted/30">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold">{area.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      Research Focus
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{area.focus}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Scientific Foundation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/5 to-primary/10">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold mb-4">Built on Science</h3>
                <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                  We're building a platform informed by research, designed for discovery, and focused on your individual health journey.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                {[
                  {
                    icon: Activity,
                    title: "Research-Informed",
                    description: "Platform design based on current nutrition and health research"
                  },
                  {
                    icon: TrendingDown,
                    title: "Personalized Tracking",
                    description: "Tools to monitor your unique responses to different approaches"
                  },
                  {
                    icon: FlameKindling,
                    title: "Community Learning",
                    description: "Collaborative environment for sharing experiences and insights"
                  }
                ].map((item, index) => (
                  <div key={item.title} className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                      <item.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h4 className="font-semibold mb-2">{item.title}</h4>
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
