"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  ArrowRight, 
  CheckCircle, 
  Clock, 
  Star,
  Users,
  TrendingUp,
  Shield,
  Zap,
  Gift,
  Timer
} from "lucide-react"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"

const benefits = [
  { icon: CheckCircle, text: "Comprehensive health tracking" },
  { icon: Users, text: "Access to a founding member community" },
  { icon: TrendingUp, text: "AI-powered personalized insights" },
  { icon: Shield, text: "Shape the future of the platform" },
  { icon: Zap, text: "Direct line to the development team" },
  { icon: Star, text: "Exclusive early access to new features" }
]

const urgencyItems = [
  { icon: Timer, text: "Limited early access spots available", highlight: true },
  { icon: Gift, text: "Bonus: Free initial health assessment" },
  { icon: Clock, text: "Join our growing list of founding members" }
]

export function CtaSection() {
  return (
    <section id="cta" className="py-24 bg-gradient-to-br from-primary/5 via-background to-primary/5">
      <div className="container">
        <motion.div 
          className="text-center space-y-6 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="bg-background border-primary/20">
            🚀 Get Involved
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold tracking-tight">
            Become a Founding Member of Our{" "}
            <span className="text-primary">Health Community</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Join our waitlist to get early access, receive progress updates, and help shape the future of our platform. We're building this for you, with you.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <Card className="border-0 shadow-2xl bg-gradient-to-r from-background to-background/95 backdrop-blur overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent" />
            <CardContent className="p-8 md:p-12 relative">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-2xl md:text-3xl font-bold mb-4">
                      What You Get as a Founding Member
                    </h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      By joining the waitlist, you're not just waiting, you're becoming part of our core development circle.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    {benefits.map((benefit, index) => (
                      <motion.div
                        key={benefit.text}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className="flex items-center gap-3"
                      >
                        <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                          <benefit.icon className="h-4 w-4 text-green-600" />
                        </div>
                        <span className="text-sm font-medium">{benefit.text}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>

                <div className="space-y-6">
                  <Card className="border-2 border-primary/20 bg-background/50 backdrop-blur">
                    <CardContent className="p-6">
                      <div className="space-y-3 mb-6">
                        {urgencyItems.map((item, index) => (
                          <div key={item.text} className="flex items-center gap-3">
                            <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                              item.highlight ? 'bg-red-100' : 'bg-primary/10'
                            }`}>
                              <item.icon className={`h-3 w-3 ${
                                item.highlight ? 'text-red-600' : 'text-primary'
                              }`} />
                            </div>
                            <span className={`text-sm ${
                              item.highlight ? 'text-red-600 font-semibold' : 'text-muted-foreground'
                            }`}>
                              {item.text}
                            </span>
                          </div>
                        ))}
                      </div>

                      <div className="text-center mb-6">
                        <div className="text-4xl font-bold text-primary mb-1">Join for FREE</div>
                        <p className="text-sm text-muted-foreground">
                          No commitment. Get early access and updates.
                        </p>
                      </div>
                      
                      <DetailedWaitlistForm 
                        trigger={
                          <Button size="lg" className="w-full gap-2 text-lg py-6">
                            Secure Your Spot
                            <ArrowRight className="h-5 w-5" />
                          </Button>
                        }
                      />

                      <div className="text-center mt-6 pt-6 border-t">
                        <p className="text-xs text-muted-foreground">
                          Be part of the movement to revolutionize health.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
