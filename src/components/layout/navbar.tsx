"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/layout/theme-toggle"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"
import { Menu, X, Heart, Users, BarChart, Brain } from "lucide-react"
import { cn } from "@/lib/utils"
import { Logo } from "./logo"

const navigation = [
  {
    name: "Features",
    href: "#features",
    description: "Comprehensive health tracking and AI-powered insights"
  },
  {
    name: "How It Works",
    href: "#how-it-works", 
    description: "Simple steps to transform your health"
  },
  {
    name: "Community",
    href: "#community",
    description: "Join us in building the future of health optimization"
  },
  {
    name: "About",
    href: "/about",
    description: "Learn about our mission and team"
  }
]

const features = [
  {
    title: "Health Tracking",
    href: "#tracking",
    description: "Monitor 25+ health metrics including weight, energy, sleep, and biomarkers",
    icon: BarChart
  },
  {
    title: "AI Meal Planning", 
    href: "#meal-planning",
    description: "Personalized carnivore meal recommendations and nutrition optimization",
    icon: Brain
  },
  {
    title: "Community Support",
    href: "#community",
    description: "Connect with others, share experiences, and get expert guidance",
    icon: Users
  },
  {
    title: "Achievement System",
    href: "#achievements", 
    description: "Gamified progress tracking with streaks, badges, and milestones",
    icon: Heart
  }
]

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b transition-all duration-200",
      isScrolled 
        ? "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" 
        : "bg-background"
    )}>
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Logo />

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden lg:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Features</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                  {features.map((feature) => (
                    <li key={feature.title}>
                      <NavigationMenuLink asChild>
                        <a
                          href={feature.href}
                          className={cn(
                            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          )}
                        >
                          <div className="flex items-center space-x-2">
                            <feature.icon className="h-4 w-4" />
                            <div className="text-sm font-medium leading-none">{feature.title}</div>
                          </div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            {feature.description}
                          </p>
                        </a>
                      </NavigationMenuLink>
                    </li>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            
            {navigation.slice(1).map((item) => (
              <NavigationMenuItem key={item.name}>
                <Link href={item.href} legacyBehavior passHref>
                  <NavigationMenuLink className={cn(
                    "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                  )}>
                    {item.name}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Desktop Actions */}
        <div className="hidden lg:flex items-center space-x-4">
          <ThemeToggle />
          <DetailedWaitlistForm 
            trigger={
              <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
                Join Waitlist
              </Button>
            }
          />
        </div>

        {/* Mobile Menu */}
        <div className="flex lg:hidden items-center space-x-2">
          <ThemeToggle />
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="lg:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-8">
                <div className="pb-4 border-b">
                  <Logo size={32} showText={true}/>
                </div>
                
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block px-2 py-1 text-lg hover:bg-accent rounded-md transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
                
                <div className="pt-4 border-t">
                  <DetailedWaitlistForm 
                    trigger={
                      <Button className="w-full bg-gradient-to-r from-primary to-primary/90">
                        Join Waitlist
                      </Button>
                    }
                  />
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
