"use client"

import Image from "next/image"
import Link from "next/link"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

export function Logo({ size = 40, showText = true }) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const logoSrc = theme === "dark" && mounted 
    ? "/brand/logo-transparent.png" 
    : "/brand/logo.png"

  if (!mounted) {
    return (
      <div style={{ width: size, height: size }} className="animate-pulse bg-muted-foreground/20 rounded-md" />
    )
  }

  return (
    <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
      <Image
        src={logoSrc}
        alt="The Proper Human Diet Logo"
        width={size}
        height={size}
        className={`w-${size/4} h-${size/4}`}
        priority
      />
      {showText && (
        <div className="hidden sm:block">
          <div className="font-bold text-lg leading-none">The Proper Human Diet</div>
          <div className="text-xs text-muted-foreground">The Science of Eating, The Foundation of Wellness.</div>
        </div>
      )}
    </Link>
  )
} 