"use client"

import React from 'react';
import { ThemeProvider } from 'next-themes';
import { Toaster } from "sonner";

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange={false}
      storageKey="theme-preference"
    >
      {children}
      <Toaster />
    </ThemeProvider>
  );
}
