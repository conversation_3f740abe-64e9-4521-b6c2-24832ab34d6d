"use client"

import { <PERSON>, Sun } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useThemeToggle } from "@/hooks/use-theme"

export function ThemeToggle() {
  const { theme, mounted, toggleTheme } = useThemeToggle()

  if (!mounted) {
    return (
      <Button variant="outline" size="icon">
        <Sun className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">Loading theme</span>
      </Button>
    )
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={(e) => toggleTheme(e as unknown as MouseEvent)}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
} 