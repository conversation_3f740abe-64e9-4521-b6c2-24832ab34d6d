"use client";

import Image from "next/image";
import Link from "next/link";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Heart, Mail } from "lucide-react";

export function Footer() {
  const currentYear = new Date().getFullYear();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const logoSrc =
    theme === "dark" && mounted
      ? "/brand/logo-transparent.png"
      : "/brand/logo.png";

  if (!mounted) {
    return (
      <footer className="bg-background border-t">
        <div className="container py-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="w-12 h-12 animate-pulse bg-muted-foreground/20 rounded-md" />
            <div className="h-4 w-48 animate-pulse bg-muted-foreground/20 rounded" />
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className="bg-background border-t">
      <div className="container py-8">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          {/* Brand Section */}
          <div className="flex items-center space-x-3">
            <Image
              src={logoSrc}
              alt="The Proper Human Diet Logo"
              width={48}
              height={48}
              className="w-12 h-12"
              priority
            />
            <div>
              <div className="font-bold text-lg">The Proper Human Diet</div>
              <div className="text-sm text-muted-foreground">
                Health Optimization Platform
              </div>
            </div>
          </div>

          {/* Contact Link */}
          <Link
            href="mailto:<EMAIL>"
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <Mail className="w-4 h-4" />
            <span><EMAIL></span>
          </Link>

          {/* Copyright and Made with Love */}
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm text-muted-foreground">
            <span>
              © {currentYear} The Proper Human Diet. All rights reserved.
            </span>
            <span className="hidden sm:inline">•</span>
            <span className="flex items-center">
              Made with <Heart className="w-4 h-4 mx-1 text-red-500" /> for your
              health
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
