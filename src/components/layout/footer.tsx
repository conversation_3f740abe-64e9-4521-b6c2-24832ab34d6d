import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"
import { 
  Heart, 
  Mail, 
  Twitter, 
  Facebook, 
  Instagram, 
  Youtube,
  ArrowRight
} from "lucide-react"

const footerNavigation = {
  platform: [
    { name: 'Features', href: '#features' },
    { name: 'How It Works', href: '#how-it-works' },
    { name: 'Community', href: '#community' },
    { name: 'Pricing', href: '#pricing' },
  ],
  resources: [
    { name: 'Blog', href: '/blog' },
    { name: 'Research', href: '/research' },
    { name: 'Help Center', href: '/help' },
    { name: 'API Documentation', href: '/docs' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press Kit', href: '/press' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'GDPR', href: '/gdpr' },
  ],
}

const socialLinks = [
  {
    name: 'Twitter',
    href: 'https://twitter.com/ProperHumanDiet',
    icon: Twitter,
  },
  {
    name: 'Facebook', 
    href: 'https://facebook.com/ProperHumanDiet',
    icon: Facebook,
  },
  {
    name: 'Instagram',
    href: 'https://instagram.com/ProperHumanDiet', 
    icon: Instagram,
  },
  {
    name: 'YouTube',
    href: 'https://youtube.com/@ProperHumanDiet',
    icon: Youtube,
  },
]

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-background border-t">
      <div className="container">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <Image
                  src="/tphd-transparent.png"
                  alt="The Proper Human Diet Logo"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
                <div>
                  <div className="font-bold text-xl">The Proper Human Diet</div>
                  <div className="text-sm text-muted-foreground">Health Optimization Platform</div>
                </div>
              </div>
              <p className="text-muted-foreground mb-6 max-w-sm">
                Join us in building the ultimate health optimization platform with 
                comprehensive tracking, AI-powered insights, and a supportive community.
              </p>
              
              {/* Newsletter Signup */}
              <div className="space-y-3">
                <h4 className="font-semibold">Stay Updated</h4>
                <p className="text-sm text-muted-foreground">
                  Get health insights and early access updates.
                </p>
                <DetailedWaitlistForm 
                  trigger={
                    <Button className="w-full sm:w-auto bg-gradient-to-r from-primary to-primary/90">
                      <Mail className="w-4 h-4 mr-2" />
                      Join Waitlist
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  }
                />
              </div>
            </div>

            {/* Navigation Links */}
            <div>
              <h3 className="font-semibold mb-4">Platform</h3>
              <ul className="space-y-3">
                {footerNavigation.platform.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-3">
                {footerNavigation.resources.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-3">
                {footerNavigation.company.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-3">
                {footerNavigation.legal.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <Separator />

        {/* Bottom Footer */}
        <div className="py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>© {currentYear} The Proper Human Diet. All rights reserved.</span>
              <span className="hidden md:inline">•</span>
              <span className="flex items-center">
                Made with <Heart className="w-4 h-4 mx-1 text-red-500" /> for your health
              </span>
            </div>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="w-5 h-5" />
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
