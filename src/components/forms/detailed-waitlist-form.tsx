"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import {
  Loader2,
  Mail,
  CheckCircle,
  Heart,
  Brain,
  Users,
  TrendingUp,
  AlertCircle,
  Sparkles,
  DollarSign,
  Rocket,
  Star,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import Confetti from "react-confetti";
import { waitlistSchema, type WaitlistFormData } from "@/lib/validations";
import {
  HEALTH_CONDITIONS,
  DIETARY_GOALS,
  CURRENT_DIETS,
  INTERESTS,
} from "@/lib/constants";
import { calculatePriorityScore } from "@/lib/waitlist";

interface DetailedWaitlistFormProps {
  trigger?: React.ReactNode;
}

export function DetailedWaitlistForm({ trigger }: DetailedWaitlistFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [priorityScore, setPriorityScore] = useState(0);

  const form = useForm<WaitlistFormData>({
    resolver: zodResolver(waitlistSchema),
    mode: "onChange", // Change back to onChange for better UX, but handle validation carefully
    reValidateMode: "onChange",
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      healthConditions: [],
      dietaryGoals: [],
      currentDiet: "",
      referralSource: "",
      interests: [],
      monthlyBudget: "",
    },
  });

  const totalSteps = 5;
  const progress = (currentStep / totalSteps) * 100;

  // Reset loading state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setIsLoading(false);
      setIsSuccess(false);
      setShowConfetti(false);
    }
  }, [isOpen]);

  // Ensure loading state is reset on component mount
  useEffect(() => {
    setIsLoading(false);
  }, []);

  const validateCurrentStep = async (): Promise<boolean> => {
    // Define fields required for each step
    const stepFields: Record<number, (keyof WaitlistFormData)[]> = {
      1: ["email"], // Step 1 requires email (firstName and lastName are optional)
      2: [], // Step 2 has no required fields (all optional)
      3: [], // Step 3 has no required fields (all optional)
      4: [], // Step 4 has no required fields (all optional)
      5: [], // Step 5 has no required fields (all optional)
    };

    const fieldsToValidate = stepFields[currentStep] || [];

    if (fieldsToValidate.length === 0) {
      return true; // No validation needed for this step
    }

    // For step 1, specifically validate email field
    if (currentStep === 1) {
      const emailValue = form.getValues("email");

      // Check if email is empty or invalid
      if (!emailValue || emailValue.trim() === "") {
        form.setError("email", {
          type: "required",
          message: "Email is required.",
        });
        return false;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailValue)) {
        form.setError("email", {
          type: "pattern",
          message: "Please enter a valid email address.",
        });
        return false;
      }

      // Clear any existing email errors if validation passes
      form.clearErrors("email");
      return true;
    }

    // Trigger validation for specific fields for other steps
    const result = await form.trigger(fieldsToValidate);
    return result;
  };

  const handleNext = async () => {
    if (currentStep < totalSteps) {
      // Validate current step before proceeding
      const isValid = await validateCurrentStep();

      if (!isValid) {
        // Show error toast if validation fails
        toast.error("Please complete all required fields", {
          description: "Check the highlighted fields and try again.",
        });
        return;
      }

      setCurrentStep(currentStep + 1);
      const score = calculatePriorityScore(form.getValues());
      setPriorityScore(score);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: WaitlistFormData) => {
    // Prevent double submission
    if (isLoading) {
      console.log(
        "⚠️ Form submission already in progress, ignoring duplicate submission",
      );
      return;
    }

    console.log("📝 Form submission started with data:", data);
    setIsLoading(true);

    try {
      const payload = {
        ...data,
        priority: priorityScore,
      };

      console.log("📝 Sending payload to API:", payload);

      const response = await fetch("/api/waitlist", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();
      console.log("📝 API response:", response.status, result);

      if (!response.ok) {
        throw new Error(result.error || "Failed to join waitlist");
      }

      setIsSuccess(true);
      setShowConfetti(true);

      toast.success("🎉 Welcome to our founding community!", {
        description: "Your health transformation journey is about to begin!",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Something went wrong";

      console.error("❌ Form submission error:", error);
      toast.error("Oops! Something went wrong", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    form.reset();
    setCurrentStep(1);
    setIsSuccess(false);
    setIsLoading(false);
    setShowConfetti(false);
    setPriorityScore(0);
    setIsOpen(false);
  };

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1:
        return "Your Health Story Begins Here";
      case 2:
        return "Understanding Your Journey";
      case 3:
        return "Designing Your Success Path";
      case 4:
        return "Building Our Partnership";
      case 5:
        return "Ready to Transform?";
      default:
        return "Welcome";
    }
  };

  const getStepSubtitle = (step: number) => {
    switch (step) {
      case 1:
        return "Every transformation starts with a single step. Let's take yours together.";
      case 2:
        return "Your unique health story helps us personalize your experience.";
      case 3:
        return "Dream big! What does your healthiest self look like?";
      case 4:
        return "Great partnerships require transparency. Let's discuss investment.";
      case 5:
        return "You're moments away from joining a community that will change your life.";
      default:
        return "Welcome to your journey";
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center space-y-4">
              <div className="relative">
                <Heart className="h-16 w-16 text-primary mx-auto animate-pulse" />
                <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Your Health Story Begins Here
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Imagine waking up every morning feeling energized, confident,
                  and in control of your health. That's not just a dream—it's
                  your future. Let's start by getting to know the amazing person
                  who's about to embark on this transformation.
                </p>
              </div>
            </div>

            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-background">
              <CardContent className="p-4 sm:p-6 space-y-4 sm:space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName" className="text-sm font-medium">
                      What should we call you?
                      <span className="text-muted-foreground text-xs ml-1">
                        (optional)
                      </span>
                    </Label>
                    <Input
                      id="firstName"
                      {...form.register("firstName")}
                      placeholder="Your first name"
                      className={`mt-2 h-12 sm:h-10 text-base sm:text-sm ${
                        form.formState.errors.firstName
                          ? "border-destructive"
                          : ""
                      }`}
                    />
                    {form.formState.errors.firstName && (
                      <p className="text-sm text-destructive mt-1">
                        {form.formState.errors.firstName.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-sm font-medium">
                      Your family name
                      <span className="text-muted-foreground text-xs ml-1">
                        (optional)
                      </span>
                    </Label>
                    <Input
                      id="lastName"
                      {...form.register("lastName")}
                      placeholder="Last name"
                      className={`mt-2 h-12 sm:h-10 text-base sm:text-sm ${
                        form.formState.errors.lastName
                          ? "border-destructive"
                          : ""
                      }`}
                    />
                    {form.formState.errors.lastName && (
                      <p className="text-sm text-destructive mt-1">
                        {form.formState.errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="email" className="text-sm font-medium">
                    Where can we send your health transformation updates?
                    <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...form.register("email", {
                      onBlur: () => {
                        // Clear errors when user starts typing a valid email
                        const emailValue = form.getValues("email");
                        if (emailValue && emailValue.trim() !== "") {
                          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                          if (emailRegex.test(emailValue)) {
                            form.clearErrors("email");
                          }
                        }
                      },
                    })}
                    placeholder="<EMAIL>"
                    className={`mt-2 h-12 sm:h-10 text-base sm:text-sm ${
                      form.formState.errors.email ? "border-destructive" : ""
                    }`}
                    onChange={(e) => {
                      // Update form value
                      form.setValue("email", e.target.value);

                      // Clear errors if email becomes valid
                      const emailValue = e.target.value;
                      if (emailValue && emailValue.trim() !== "") {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (emailRegex.test(emailValue)) {
                          form.clearErrors("email");
                        }
                      }
                    }}
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-destructive mt-1">
                      {form.formState.errors.email.message}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-2">
                    💌 We'll send you exclusive health insights and early access
                    updates
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center space-y-4">
              <div className="relative">
                <Brain className="h-16 w-16 text-primary mx-auto" />
                <Star className="h-5 w-5 text-yellow-500 absolute -top-1 -right-1 animate-spin" />
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Understanding Your Journey
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Every health hero has a unique story. Your challenges, your
                  current path, your body's responses—they all matter. This
                  information helps us create a platform that truly serves YOU.
                  Remember, there's no judgment here, only support.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <Card className="border-primary/20">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-red-500" />
                    Your Health Story (Optional)
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Sharing helps us personalize your experience, but only share
                    what feels comfortable
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {HEALTH_CONDITIONS.map((condition) => (
                      <div
                        key={condition}
                        className="flex items-center space-x-3 p-3 sm:p-2 rounded-lg hover:bg-muted/50 transition-colors min-h-[44px] sm:min-h-auto"
                      >
                        <Checkbox
                          id={condition}
                          checked={form
                            .watch("healthConditions")
                            ?.includes(condition)}
                          onCheckedChange={(checked: boolean) => {
                            const current =
                              form.getValues("healthConditions") || [];
                            if (checked) {
                              form.setValue("healthConditions", [
                                ...current,
                                condition,
                              ]);
                            } else {
                              form.setValue(
                                "healthConditions",
                                current.filter((c) => c !== condition),
                              );
                            }
                          }}
                          className="h-4 w-4 sm:h-4 sm:w-4"
                        />
                        <Label
                          htmlFor={condition}
                          className="text-sm cursor-pointer flex-1 leading-relaxed"
                        >
                          {condition}
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-primary/20">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg">
                    What's Your Current Path?
                  </CardTitle>
                  <CardDescription className="text-sm">
                    We support all approaches—find what works best for your
                    unique body
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <Select
                    value={form.watch("currentDiet") || ""}
                    onValueChange={(value: string) =>
                      form.setValue("currentDiet", value)
                    }
                  >
                    <SelectTrigger className="h-12 sm:h-10 text-base sm:text-sm">
                      <SelectValue placeholder="Choose your current dietary approach" />
                    </SelectTrigger>
                    <SelectContent>
                      {CURRENT_DIETS.map((diet) => (
                        <SelectItem
                          key={diet}
                          value={diet}
                          className="py-3 sm:py-2"
                        >
                          {diet}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center space-y-4">
              <div className="relative">
                <TrendingUp className="h-16 w-16 text-primary mx-auto" />
                <Rocket className="h-6 w-6 text-orange-500 absolute -top-1 -right-1 animate-bounce" />
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Designing Your Success Path
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Close your eyes and imagine yourself 6 months from now. What
                  does success look like? More energy? Better sleep? Confidence
                  in your body? Let's map out your dreams and build the features
                  that will get you there.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <Card className="border-primary/20 bg-gradient-to-br from-green-50/50 to-background dark:from-green-950/20">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                    Your Health Dreams
                  </CardTitle>
                  <CardDescription className="text-sm">
                    What victories are you working towards? (Select all that
                    resonate)
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {DIETARY_GOALS.map((goal) => (
                      <div
                        key={goal}
                        className="flex items-center space-x-3 p-3 sm:p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-950/30 transition-colors min-h-[44px] sm:min-h-auto"
                      >
                        <Checkbox
                          id={goal}
                          checked={form.watch("dietaryGoals")?.includes(goal)}
                          onCheckedChange={(checked: boolean) => {
                            const current =
                              form.getValues("dietaryGoals") || [];
                            if (checked) {
                              form.setValue("dietaryGoals", [...current, goal]);
                            } else {
                              form.setValue(
                                "dietaryGoals",
                                current.filter((g) => g !== goal),
                              );
                            }
                          }}
                          className="h-4 w-4 sm:h-4 sm:w-4"
                        />
                        <Label
                          htmlFor={goal}
                          className="text-sm cursor-pointer flex-1 leading-relaxed"
                        >
                          {goal}
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-primary/20 bg-gradient-to-br from-blue-50/50 to-background dark:from-blue-950/20">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                    Platform Features You're Excited About
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Help us prioritize what matters most to you
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {INTERESTS.map((interest) => (
                      <div
                        key={interest}
                        className="flex items-center space-x-3 p-3 sm:p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-950/30 transition-colors min-h-[44px] sm:min-h-auto"
                      >
                        <Checkbox
                          id={interest}
                          checked={form.watch("interests")?.includes(interest)}
                          onCheckedChange={(checked: boolean) => {
                            const current = form.getValues("interests") || [];
                            if (checked) {
                              form.setValue("interests", [
                                ...current,
                                interest,
                              ]);
                            } else {
                              form.setValue(
                                "interests",
                                current.filter((i) => i !== interest),
                              );
                            }
                          }}
                          className="h-4 w-4 sm:h-4 sm:w-4"
                        />
                        <Label
                          htmlFor={interest}
                          className="text-sm cursor-pointer flex-1 leading-relaxed"
                        >
                          {interest}
                        </Label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center space-y-4">
              <div className="relative">
                <DollarSign className="h-16 w-16 text-primary mx-auto" />
                <Heart className="h-6 w-6 text-red-500 absolute -top-1 -right-1 animate-pulse" />
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Building Our Partnership
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Transparency builds trust. We're investing in AI technology,
                  third-party health integrations, and expert consultations to
                  give you the best possible experience. Understanding your
                  budget helps us create pricing that works for everyone.
                </p>
              </div>
            </div>

            <Card className="border-primary/20 bg-gradient-to-br from-emerald-50/50 to-background dark:from-emerald-950/20">
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                  <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600" />
                  Investment in Your Health
                </CardTitle>
                <CardDescription className="text-sm">
                  What monthly investment feels comfortable for a platform that
                  could transform your health?
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-0">
                <Select
                  value={form.watch("monthlyBudget") || ""}
                  onValueChange={(value: string) =>
                    form.setValue("monthlyBudget", value)
                  }
                >
                  <SelectTrigger className="h-12 sm:h-10 text-base sm:text-sm">
                    <SelectValue placeholder="Select your comfortable monthly budget" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free" className="py-3 sm:py-2">
                      I prefer a free version
                    </SelectItem>
                    <SelectItem value="5-15" className="py-3 sm:py-2">
                      $5 - $15/month
                    </SelectItem>
                    <SelectItem value="15-30" className="py-3 sm:py-2">
                      $15 - $30/month
                    </SelectItem>
                    <SelectItem value="30-50" className="py-3 sm:py-2">
                      $30 - $50/month
                    </SelectItem>
                    <SelectItem value="50-100" className="py-3 sm:py-2">
                      $50 - $100/month
                    </SelectItem>
                    <SelectItem value="100+" className="py-3 sm:py-2">
                      $100+/month
                    </SelectItem>
                  </SelectContent>
                </Select>

                <div className="bg-muted/30 p-4 rounded-lg space-y-2">
                  <h4 className="font-medium text-sm">
                    💡 What Your Investment Enables:
                  </h4>
                  <div className="grid grid-cols-1 gap-1 text-xs text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>Advanced AI health analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>Integration with wearables & lab results</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>Expert consultations & community moderation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>Continuous platform improvements</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        );

      case 5:
        return (
          <motion.div
            key="step5"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center space-y-4">
              <div className="relative">
                <Users className="h-16 w-16 text-primary mx-auto animate-pulse" />
                <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-1 -right-1 animate-spin" />
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Ready to Transform?
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  You're about to join a community of health pioneers who refuse
                  to settle for "okay." Together, we're building something
                  revolutionary. Your journey to optimal health starts the
                  moment you click that button below.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <Card className="border-primary/20">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg">
                    How did you discover us?
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Help us understand how health-conscious people like you find
                    solutions
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <Select
                    value={form.watch("referralSource") || ""}
                    onValueChange={(value: string) =>
                      form.setValue("referralSource", value)
                    }
                  >
                    <SelectTrigger className="h-12 sm:h-10 text-base sm:text-sm">
                      <SelectValue placeholder="How did you hear about us?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="social_media" className="py-3 sm:py-2">
                        Social Media (Instagram, Twitter, etc.)
                      </SelectItem>
                      <SelectItem
                        value="search_engine"
                        className="py-3 sm:py-2"
                      >
                        Google Search
                      </SelectItem>
                      <SelectItem
                        value="friend_referral"
                        className="py-3 sm:py-2"
                      >
                        Friend or Family Recommendation
                      </SelectItem>
                      <SelectItem
                        value="health_professional"
                        className="py-3 sm:py-2"
                      >
                        Healthcare Professional
                      </SelectItem>
                      <SelectItem value="podcast" className="py-3 sm:py-2">
                        Podcast or YouTube
                      </SelectItem>
                      <SelectItem value="blog_article" className="py-3 sm:py-2">
                        Blog or Article
                      </SelectItem>
                      <SelectItem value="other" className="py-3 sm:py-2">
                        Other
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {priorityScore > 3 && (
                <Alert className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/20">
                  <Sparkles className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800 dark:text-yellow-200">
                    <strong>🌟 VIP Early Access!</strong> Your health profile
                    indicates you'll benefit significantly from our platform.
                    You're getting priority access when we launch!
                  </AlertDescription>
                </Alert>
              )}

              <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-background">
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                    <Rocket className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                    Your Transformation Journey Starts Now
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 pt-0">
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div>
                        <div className="font-medium text-sm">
                          Instant Welcome Package
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Health optimization starter guide & community access
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-sm">
                          Weekly Health Insights
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Curated tips, research updates, and success stories
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-sm">
                          Founding Member Benefits
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Lifetime discounts, exclusive features, and direct
                          input on development
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-orange-600" />
                      <div>
                        <div className="font-medium text-sm">
                          Early Access (2024)
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Be among the first to experience the full platform
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  if (isSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          {trigger || (
            <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
              🚀 Start Your Transformation
            </Button>
          )}
        </DialogTrigger>
        <DialogContent
          className="sm:max-w-md w-full h-full sm:h-auto p-4 sm:p-6"
          onEscapeKeyDown={(e) => e.preventDefault()}
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          {showConfetti && <Confetti numberOfPieces={100} recycle={false} />}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="text-center space-y-4 sm:space-y-6 py-6 sm:py-8 flex flex-col justify-center h-full sm:h-auto"
          >
            <div className="relative">
              <CheckCircle className="h-16 w-16 sm:h-20 sm:w-20 text-green-500 mx-auto animate-pulse" />
              <Sparkles className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500 absolute -top-2 -right-2 animate-bounce" />
            </div>
            <div className="space-y-2">
              <h3 className="text-xl sm:text-2xl font-bold text-green-600">
                🎉 Welcome to Your New Beginning!
              </h3>
              <p className="text-sm sm:text-base text-muted-foreground px-2 sm:px-0">
                You've just taken the most important step in your health
                journey. Check your email for your welcome package and get ready
                to transform your life!
              </p>
            </div>
            <div className="flex flex-col gap-3 px-4 sm:px-0">
              <Button
                onClick={resetForm}
                className="bg-gradient-to-r from-green-600 to-green-700 h-12 sm:h-10 text-base sm:text-sm"
              >
                Invite a Friend to Join
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="h-12 sm:h-10 text-base sm:text-sm"
              >
                Explore the Platform
              </Button>
            </div>
          </motion.div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
            🚀 Start Your Transformation
          </Button>
        )}
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-2xl max-h-[100vh] sm:max-h-[90vh] w-full sm:w-auto h-full sm:h-auto overflow-y-auto p-4 sm:p-6"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="text-center space-y-3 mb-2 sm:mb-4">
          <DialogTitle className="text-xl sm:text-2xl px-2 sm:px-0">
            {getStepTitle(currentStep)}
          </DialogTitle>
          <DialogDescription className="text-sm sm:text-base leading-relaxed px-2 sm:px-0">
            {getStepSubtitle(currentStep)}
          </DialogDescription>

          <div className="space-y-2 px-2 sm:px-0">
            <div className="flex justify-between items-center text-xs sm:text-sm text-muted-foreground">
              <span>
                Step {currentStep} of {totalSteps}
              </span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2 sm:h-2" />

            {/* Required field indicator for current step */}
            {currentStep === 1 && (
              <p className="text-xs text-muted-foreground">
                <span className="text-destructive">*</span> Required field
              </p>
            )}
          </div>
        </DialogHeader>

        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4 sm:space-y-6 flex-1"
        >
          <AnimatePresence mode="wait">{renderStep()}</AnimatePresence>

          {/* Show validation errors for current step only */}
          {(() => {
            // Define which fields belong to each step
            const stepFields: Record<number, (keyof WaitlistFormData)[]> = {
              1: ["email", "firstName", "lastName"],
              2: ["healthConditions"],
              3: ["dietaryGoals", "currentDiet"],
              4: ["referralSource", "monthlyBudget"],
              5: ["interests"],
            };

            const currentStepFields = stepFields[currentStep] || [];
            const currentStepErrors = Object.entries(
              form.formState.errors,
            ).filter(([field]) =>
              currentStepFields.includes(field as keyof WaitlistFormData),
            );

            return currentStepErrors.length > 0 ? (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                <p className="text-sm text-destructive font-medium mb-2">
                  Please fix the following errors:
                </p>
                <ul className="text-sm text-destructive space-y-1">
                  {currentStepErrors.map(([field, error]) => {
                    const fieldName =
                      field === "email"
                        ? "Email"
                        : field === "firstName"
                          ? "First Name"
                          : field === "lastName"
                            ? "Last Name"
                            : field === "currentDiet"
                              ? "Current Diet"
                              : field === "referralSource"
                                ? "How did you hear about us"
                                : field === "monthlyBudget"
                                  ? "Monthly Budget"
                                  : field === "healthConditions"
                                    ? "Health Conditions"
                                    : field === "dietaryGoals"
                                      ? "Dietary Goals"
                                      : field === "interests"
                                        ? "Interests"
                                        : field.charAt(0).toUpperCase() +
                                          field.slice(1);

                    const errorMessage =
                      error?.message || `${fieldName} is required`;

                    return <li key={field}>• {errorMessage}</li>;
                  })}
                </ul>
              </div>
            ) : null;
          })()}

          <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0 pt-4 sm:pt-6 border-t mt-auto">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrev}
              disabled={currentStep === 1}
              className="flex items-center justify-center gap-2 h-12 sm:h-10 text-base sm:text-sm order-2 sm:order-1"
            >
              ← Previous
            </Button>

            {currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={handleNext}
                disabled={isLoading}
                className="flex items-center justify-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary h-12 sm:h-10 text-base sm:text-sm order-1 sm:order-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="hidden sm:inline">Validating...</span>
                    <span className="sm:hidden">Wait</span>
                  </>
                ) : (
                  <>Continue →</>
                )}
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center justify-center gap-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 h-12 sm:h-10 text-base sm:text-sm order-1 sm:order-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="hidden sm:inline">Joining...</span>
                    <span className="sm:hidden">Joining</span>
                  </>
                ) : (
                  <>
                    🎉{" "}
                    <span className="hidden sm:inline">
                      Join the Revolution
                    </span>
                    <span className="sm:hidden">Join Now</span>
                  </>
                )}
              </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
