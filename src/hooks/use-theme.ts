import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';

export const useThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = (e?: MouseEvent) => {
    if (!mounted) return;

    // Get click coordinates for animation origin
    const x = e?.clientX ?? window.innerWidth / 2;
    const y = e?.clientY ?? window.innerHeight / 2;

    // Set animation origin point
    document.documentElement.style.setProperty('--x', `${x}px`);
    document.documentElement.style.setProperty('--y', `${y}px`);

    // Start view transition
    if (document.startViewTransition) {
      document.startViewTransition(() => {
        setTheme(theme === 'light' ? 'dark' : 'light');
      });
    } else {
      setTheme(theme === 'light' ? 'dark' : 'light');
    }
  };

  return {
    theme,
    mounted,
    toggleTheme
  };
};
