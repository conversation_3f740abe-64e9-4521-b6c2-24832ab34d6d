import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import { Providers } from "@/components/layout/providers";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import {
  generateOrganizationSchema,
  generateWebsiteSchema,
  generateWebApplicationSchema,
} from "@/lib/structured-data";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0a0a0a" },
  ],
};

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || "https://theproperhumandiet.com",
  ),
  title: {
    default:
      "The Proper Human Diet - Transform Your Health with Carnivore Precision",
    template: "%s | The Proper Human Diet",
  },
  description:
    "The most comprehensive platform for tracking your carnivore journey. Monitor health metrics, manage autoimmune symptoms, and achieve lasting wellness with AI-powered insights and community support.",
  keywords: [
    "carnivore diet",
    "health tracking",
    "autoimmune disease",
    "weight loss",
    "inflammation reduction",
    "diet tracking app",
    "health metrics",
    "nutrition tracking",
    "wellness platform",
    "AI health insights",
  ],
  authors: [{ name: "The Proper Human Diet Team" }],
  creator: "The Proper Human Diet",
  publisher: "The Proper Human Diet",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: "/brand/logo.png", sizes: "16x16", type: "image/png" },
      { url: "/brand/logo.png", sizes: "32x32", type: "image/png" },
      { url: "/brand/logo.png", sizes: "96x96", type: "image/png" },
    ],
    apple: [{ url: "/brand/logo.png", sizes: "180x180", type: "image/png" }],
    other: [
      { url: "/brand/logo.png", sizes: "192x192", type: "image/png" },
      { url: "/brand/logo.png", sizes: "512x512", type: "image/png" },
    ],
  },
  manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title:
      "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description:
      "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    siteName: "The Proper Human Diet",
    images: [
      {
        url: "/brand/logo-with-domain-name.png",
        width: 512,
        height: 512,
        alt: "The Proper Human Diet Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title:
      "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description:
      "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    creator: "@ProperHumanDiet",
    images: ["/brand/logo-with-domain-name.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    other: {
      "facebook-domain-verification":
        process.env.FACEBOOK_DOMAIN_VERIFICATION || "",
    },
  },
  alternates: {
    canonical: "/",
  },
  category: "health",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = [
    generateOrganizationSchema(),
    generateWebsiteSchema(),
    generateWebApplicationSchema(),
  ];

  return (
    <html lang="en" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />

        {/* Structured data */}
        {structuredData.map((schema, index) => (
          <script
            key={index}
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
          />
        ))}
      </head>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen bg-background font-sans antialiased">
            <Navbar />
            <main>{children}</main>
            <Footer />
          </div>
        </Providers>
      </body>
    </html>
  );
}
