import { Metadata } from "next";
import { HeroSection } from "@/components/sections/hero-section";
import { FeaturesSection } from "@/components/sections/features-section";
import { HowItWorksSection } from "@/components/sections/how-it-works-section";
import { HealthBenefitsSection } from "@/components/sections/health-benefits-section";
import { CommunitySection } from "@/components/sections/community-section";
import { CtaSection } from "@/components/sections/cta-section";
import {
  generateHealthAndWellnessSchema,
  generateFAQSchema,
} from "@/lib/structured-data";

export const metadata: Metadata = {
  title: "Transform Your Health with Personalized Insights",
  description:
    "Join us in building the ultimate health optimization platform. Track 25+ health metrics, explore dietary approaches, and connect with a supportive community focused on wellness and nutrition.",
  openGraph: {
    title:
      "The Proper Human Diet - Transform Your Health with Personalized Insights",
    description:
      "Join us in building the ultimate health optimization platform for tracking, community, and personalized wellness insights.",
    images: [
      {
        url: "/images/hero-og.jpg",
        width: 1200,
        height: 630,
        alt: "Health optimization platform showing personalized tracking, community support, and wellness insights",
      },
    ],
  },
  twitter: {
    title: "Transform Your Health with Personalized Insights",
    description:
      "Join us in building the ultimate health optimization platform for tracking, community, and personalized wellness insights.",
    images: ["/images/hero-og.jpg"],
  },
  keywords: [
    "health optimization platform",
    "health tracking app",
    "wellness community",
    "nutrition tracking",
    "dietary approaches",
    "AI health insights",
    "health community",
    "wellness metrics dashboard",
    "personalized nutrition",
    "holistic health tracking",
  ],
};

// FAQ data for structured data
const faqs = [
  {
    question: "What is The Proper Human Diet platform and how can it help me?",
    answer:
      "We're building a comprehensive health optimization platform that helps you track 25+ health metrics, explore various dietary approaches, and connect with a supportive community. Our goal is to help you discover what works best for your unique health journey.",
  },
  {
    question: "What dietary approaches does the platform support?",
    answer:
      "Our platform will support various dietary approaches including ketogenic, carnivore, elimination diets, anti-inflammatory protocols, and more. We believe in personalized nutrition and helping you find what works best for your body and health goals.",
  },
  {
    question: "What health metrics can I track?",
    answer:
      "You'll be able to track weight, body composition, blood pressure, sleep quality, energy levels, mood, digestive health, stress levels, inflammation markers, and many more metrics to get a complete picture of your health journey.",
  },
  {
    question: "When will the platform be available?",
    answer:
      "We're currently in development with early access planned for Q2 2025. Join our waitlist to be among the first to access our platform and help shape its features through community feedback.",
  },
  {
    question: "How does the community aspect work?",
    answer:
      "Our platform will feature real-time chat, discussion forums, support groups, and knowledge sharing capabilities. We're building a space where people can connect, share experiences, and support each other's health journeys across various dietary approaches.",
  },
  {
    question: "What makes this platform different?",
    answer:
      "We're building this platform with and for the community. Your feedback shapes our features, and we focus on supporting diverse health approaches rather than promoting a single diet. It's about finding what works for you through data, community, and personalized insights.",
  },
];

export default function HomePage() {
  const structuredData = [
    generateHealthAndWellnessSchema(),
    generateFAQSchema(faqs),
  ];

  return (
    <>
      {structuredData.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        />
      ))}

      <HeroSection />
      <FeaturesSection />
      <HowItWorksSection />
      <HealthBenefitsSection />
      <CommunitySection />
      <CtaSection />
    </>
  );
}
