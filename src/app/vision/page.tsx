"use client";

import { useState, useEffect, useRef } from "react";
import {
  motion,
  useScroll,
  useTransform,
  useSpring,
  useMotionValue,
  useVelocity,
} from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Heart,
  Users,
  Zap,
  Target,
  Smartphone,
  Shield,
  Cloud,
  Calendar,
  Rocket,
  Star,
  Scale,
  AlertTriangle,
  MessageCircle,
  TrendingUp,
  Sparkles,
  ArrowRight,
  CheckCircle,
  BarChart3,
  LineChart,
  Activity,
} from "lucide-react";
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form";
// @ts-ignore - Lenis types not available
import Lenis from "lenis";

// TypeScript interfaces for mobile-optimized content
interface ProblemItem {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  stat: string;
  description: string;
  mobileDescription: string;
}

interface VisionFrame {
  id: string;
  title: string;
  subtitle?: string;
  content: React.ReactNode;
  background?: string;
}

const AnimatedCounter = ({
  value,
  duration = 2000,
}: {
  value: number;
  duration?: number;
}) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const updateCount = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * value));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(updateCount);
      }
    };

    animationFrame = requestAnimationFrame(updateCount);
    return () => cancelAnimationFrame(animationFrame);
  }, [value, duration]);

  return <span>{count.toLocaleString()}</span>;
};

// 3D Parallax Background Component
function ParallaxBackground() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll();
  const y1 = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);
  const y2 = useTransform(scrollYProgress, [0, 1], ["0%", "200%"]);
  const y3 = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const rotate = useTransform(scrollYProgress, [0, 1], ["0deg", "360deg"]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [1, 1.2, 0.8]);

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Layer 1 - Deepest background */}
      <motion.div
        style={{ y: y3 }}
        className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5"
      />

      {/* Layer 2 - Floating shapes */}
      <motion.div
        style={{ y: y2, rotate, scale }}
        className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl"
      />
      <motion.div
        style={{ y: y1, rotate: rotate }}
        className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-secondary/10 to-primary/10 rounded-full blur-3xl"
      />

      {/* Layer 3 - Animated particles */}
      <motion.div
        style={{ y: y1 }}
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120rem] h-[120rem] bg-gradient-to-r from-primary/2 to-secondary/2 rounded-full blur-3xl"
      />
    </div>
  );
}

// 3D Perspective Container
function Perspective3D({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const rotateX = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    ["15deg", "0deg", "-15deg"],
  );
  const rotateY = useTransform(scrollYProgress, [0, 1], ["-5deg", "5deg"]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);
  const z = useTransform(scrollYProgress, [0, 0.5, 1], [-200, 0, -200]);

  return (
    <motion.div
      ref={ref}
      style={{
        rotateX,
        rotateY,
        scale,
        z,
        transformStyle: "preserve-3d",
        perspective: "1000px",
      }}
      className={`${className}`}
    >
      {children}
    </motion.div>
  );
}

export default function VisionPage() {
  const [currentFrame, setCurrentFrame] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll();
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const smoothMouseX = useSpring(mouseX, { stiffness: 300, damping: 30 });
  const smoothMouseY = useSpring(mouseY, { stiffness: 300, damping: 30 });

  // Initialize Lenis for smooth scrolling
  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      lerp: 0.1,
      smoothWheel: true,
      smoothTouch: false,
      touchMultiplier: 2,
    });

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      lenis.destroy();
    };
  }, []);

  // Mouse tracking for 3D effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;

      mouseX.set((clientX - innerWidth / 2) / innerWidth);
      mouseY.set((clientY - innerHeight / 2) / innerHeight);
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [mouseX, mouseY]);

  const frames: VisionFrame[] = [
    {
      id: "opening",
      title: "The Future of Health is Personal",
      subtitle:
        "Every journey is unique. Every story matters. Every transformation inspires.",
      content: <OpeningFrame />,
      background: "from-primary/10 via-background to-secondary/10",
    },
    {
      id: "problem",
      title: "The Problem We're Solving",
      subtitle: "Current health tracking is broken. We're here to fix it.",
      content: <ProblemFrame />,
      background: "from-red-500/5 via-background to-orange-500/5",
    },
    {
      id: "solution",
      title: "Our Solution: The Ultimate Health Ecosystem",
      subtitle:
        "Four pillars that transform how you approach your health journey.",
      content: <SolutionFrame />,
      background: "from-blue-500/5 via-background to-cyan-500/5",
    },
    {
      id: "ai-intelligence",
      title: "AI-Powered Intelligence",
      subtitle:
        "Your personal health detective, working 24/7 to unlock insights.",
      content: <AIFrame />,
      background: "from-purple-500/5 via-background to-indigo-500/5",
    },
    {
      id: "community",
      title: "Community & Stories",
      subtitle:
        "Together we're stronger. Your story inspires others to transform.",
      content: <CommunityFrame />,
      background: "from-green-500/5 via-background to-emerald-500/5",
    },
    {
      id: "technology",
      title: "Built on Modern Technology",
      subtitle: "Secure, scalable, and designed for the future of health.",
      content: <TechnologyFrame />,
      background: "from-slate-500/5 via-background to-gray-500/5",
    },
    {
      id: "roadmap",
      title: "Our Journey Together",
      subtitle: "The roadmap to transforming health tracking forever.",
      content: <RoadmapFrame />,
      background: "from-yellow-500/5 via-background to-amber-500/5",
    },
    {
      id: "cta",
      title: "Ready to Transform Your Health?",
      subtitle: "Join the community that's reshaping the future of wellness.",
      content: <CTAFrame />,
      background: "from-primary/10 via-secondary/5 to-primary/10",
    },
  ];

  return (
    <div ref={containerRef} className="min-h-screen overflow-x-hidden">
      <ParallaxBackground />

      {/* Progress Indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left"
        style={{ scaleX: scrollYProgress }}
      />

      {/* 3D Hero Section */}
      <motion.section
        className="relative h-screen flex items-center justify-center overflow-hidden"
        style={{
          perspective: "1000px",
          rotateX: useTransform(scrollYProgress, [0, 0.3], ["0deg", "10deg"]),
          scale: useTransform(scrollYProgress, [0, 0.3], [1, 0.9]),
        }}
      >
        <motion.div
          className="container relative text-center space-y-8 z-10"
          style={{
            rotateX: useTransform(smoothMouseY, [-0.5, 0.5], ["5deg", "-5deg"]),
            rotateY: useTransform(smoothMouseX, [-0.5, 0.5], ["-5deg", "5deg"]),
            transformStyle: "preserve-3d",
          }}
        >
          <motion.div
            initial={{ opacity: 0, y: 100, z: -200 }}
            animate={{ opacity: 1, y: 0, z: 0 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            style={{ transform: "translateZ(50px)" }}
          >
            <Badge
              variant="outline"
              className="mb-4 bg-background/50 backdrop-blur border-primary/20"
            >
              <Sparkles className="h-3 w-3 mr-1" />✨ Vision 2025-2026
            </Badge>
            <motion.h1
              className="text-3xl sm:text-4xl lg:text-6xl xl:text-8xl font-bold tracking-tight mb-4 sm:mb-6"
              style={{
                transform: "translateZ(100px)",
                textShadow: "0 10px 30px rgba(0,0,0,0.3)",
              }}
            >
              <span className="hidden sm:inline">The Future of </span>
              <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                <span className="hidden sm:inline">Health is Personal</span>
                <span className="sm:hidden">Personal Health</span>
              </span>
            </motion.h1>
            <motion.p
              className="text-base sm:text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4 sm:px-0"
              style={{ transform: "translateZ(75px)" }}
            >
              <span className="hidden sm:inline">
                Share your transformation journey with a supportive community.
                <strong>Track everything that matters</strong>,{" "}
                <strong>learn from real experiences</strong>, and get{" "}
                <strong>AI-powered insights</strong> to guide your health
                journey.
              </span>
              <span className="sm:hidden">
                Join a supportive community. <strong>Track smart</strong>,{" "}
                <strong>learn together</strong>, get{" "}
                <strong>AI insights</strong>.
              </span>
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            style={{ transform: "translateZ(25px)" }}
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-primary via-primary to-primary/90 hover:from-primary/90 hover:via-primary hover:to-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105"
              onClick={() =>
                document
                  .getElementById("frames")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              <Rocket className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 animate-pulse" />
              <span className="hidden sm:inline">Explore Our Vision</span>
              <span className="sm:hidden">Our Vision</span>
              <ArrowRight className="ml-1 sm:ml-2 h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Enhanced 3D Presentation Frames */}
      <div id="frames" className="relative">
        {frames.map((frame, index) => (
          <Perspective3D key={frame.id}>
            <VisionFrameComponent
              frame={frame}
              index={index}
              isActive={currentFrame === index}
              onInView={() => setCurrentFrame(index)}
            />
          </Perspective3D>
        ))}
      </div>
    </div>
  );
}

// Enhanced Frame Components with 3D effects
function OpeningFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], ["-50%", "50%"]);
  const rotateY = useTransform(scrollYProgress, [0, 1], ["-15deg", "15deg"]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);

  return (
    <div ref={ref} className="max-w-6xl mx-auto">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        <motion.div
          style={{
            y,
            rotateY,
            scale,
            transformStyle: "preserve-3d",
          }}
          className="space-y-8"
        >
          <div className="space-y-6">
            {[
              {
                icon: Heart,
                title: "Personal",
                desc: "Every journey is unique",
                gradient: "from-primary to-primary/80",
              },
              {
                icon: Brain,
                title: "Intelligent",
                desc: "AI-powered insights",
                gradient: "from-blue-500 to-cyan-500",
              },
              {
                icon: Users,
                title: "Connected",
                desc: "Community-driven support",
                gradient: "from-green-500 to-emerald-500",
              },
            ].map((item, index) => (
              <motion.div
                key={item.title}
                className="flex items-center gap-4"
                initial={{ opacity: 0, x: -100, rotateY: -45 }}
                whileInView={{ opacity: 1, x: 0, rotateY: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                style={{ transformStyle: "preserve-3d" }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 5,
                  z: 50,
                  transition: { duration: 0.3 },
                }}
              >
                <motion.div
                  className={`p-3 bg-gradient-to-r ${item.gradient} rounded-full shadow-lg`}
                  style={{ transform: "translateZ(20px)" }}
                >
                  <item.icon className="h-8 w-8 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-2xl font-bold">{item.title}</h3>
                  <p className="text-muted-foreground">{item.desc}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          style={{
            y: useTransform(scrollYProgress, [0, 1], ["50%", "-50%"]),
            rotateY: useTransform(scrollYProgress, [0, 1], ["15deg", "-15deg"]),
            scale: useTransform(scrollYProgress, [0, 0.5, 1], [0.9, 1.1, 0.9]),
          }}
          className="relative"
        >
          <Card className="border-0 shadow-2xl bg-background/95 backdrop-blur-xl transform hover:scale-105 transition-all duration-500">
            <CardContent className="p-8">
              <motion.div
                className="text-center space-y-6"
                style={{ transformStyle: "preserve-3d" }}
              >
                <motion.div
                  className="relative"
                  whileHover={{ rotateY: 180 }}
                  transition={{ duration: 0.6 }}
                  style={{ transformStyle: "preserve-3d" }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-2xl" />
                  <motion.div
                    className="relative bg-gradient-to-r from-primary to-secondary p-4 rounded-full w-20 h-20 mx-auto flex items-center justify-center"
                    style={{ transform: "translateZ(30px)" }}
                  >
                    <Zap className="h-10 w-10 text-white animate-pulse" />
                  </motion.div>
                </motion.div>
                <h4 className="text-xl font-bold">The Vision</h4>
                <p className="text-muted-foreground leading-relaxed">
                  Imagine a world where health tracking doesn't feel like work,
                  where AI helps you understand your body's patterns, and where
                  your transformation story inspires others to begin their own
                  journey.
                </p>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

function ProblemFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const problems: ProblemItem[] = [
    {
      icon: Scale,
      title: "Generic Solutions",
      stat: "Most",
      description:
        "health apps use one-size-fits-all approaches that ignore individual needs",
      mobileDescription: "apps ignore individual needs",
    },
    {
      icon: AlertTriangle,
      title: "Disconnected Data",
      stat: "Many",
      description:
        "people struggle to connect their symptoms with their diet and lifestyle",
      mobileDescription: "can't connect symptoms to diet",
    },
    {
      icon: Users,
      title: "Isolated Journeys",
      stat: "Too Many",
      description: "people feel alone in their health transformation journey",
      mobileDescription: "feel alone in health journey",
    },
    {
      icon: Target,
      title: "No Clear Path",
      stat: "Countless",
      description:
        "people don't know what steps to take next for their health goals",
      mobileDescription: "don't know next steps",
    },
  ];

  return (
    <div ref={ref} className="max-w-6xl mx-auto">
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {problems.map((problem, index) => (
          <motion.div
            key={problem.title}
            initial={{ opacity: 0, y: 100, rotateX: -45 }}
            whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
            transition={{ duration: 0.8, delay: index * 0.1 }}
            style={{
              transformStyle: "preserve-3d",
              rotateY: useTransform(
                scrollYProgress,
                [0, 1],
                [`${index * 2}deg`, `${-index * 2}deg`],
              ),
              z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 50, 0]),
            }}
            whileHover={{
              scale: 1.05,
              rotateY: 10,
              z: 100,
              transition: { duration: 0.3 },
            }}
          >
            <Card className="h-full border-red-200/50 bg-gradient-to-br from-red-50/50 to-background hover:shadow-2xl transition-all duration-500 transform-gpu">
              <CardContent className="p-4 sm:p-6 text-center space-y-3 sm:space-y-4">
                <motion.div
                  className="relative"
                  style={{ transform: "translateZ(20px)" }}
                >
                  <div className="absolute inset-0 bg-red-500/10 rounded-full blur-xl" />
                  <div className="relative p-2 sm:p-3 bg-red-500/10 rounded-full w-fit mx-auto">
                    <problem.icon className="h-6 w-6 sm:h-8 sm:w-8 text-red-600" />
                  </div>
                </motion.div>
                <div className="space-y-2">
                  <motion.div
                    className="text-2xl sm:text-3xl font-bold text-red-600"
                    style={{ transform: "translateZ(15px)" }}
                  >
                    {problem.stat}
                  </motion.div>
                  <h4 className="font-semibold text-sm sm:text-base">
                    {problem.title}
                  </h4>
                  <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                    <span className="hidden sm:inline">
                      {problem.description}
                    </span>
                    <span className="sm:hidden">
                      {problem.mobileDescription}
                    </span>
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.8 }}
        whileInView={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.5 }}
        className="text-center mt-16"
        style={{
          rotateX: useTransform(scrollYProgress, [0, 1], ["5deg", "-5deg"]),
          transformStyle: "preserve-3d",
        }}
      >
        <div className="bg-gradient-to-r from-red-500/10 to-orange-500/10 rounded-2xl p-8 border border-red-200/50 backdrop-blur">
          <h3 className="text-2xl font-bold mb-4 text-red-800 dark:text-red-400">
            The Current State is Broken
          </h3>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            People are struggling with generic solutions that don't understand
            their unique needs, isolated journeys without support, and
            disconnected data that provides no actionable insights.
          </p>
        </div>
      </motion.div>
    </div>
  );
}

function SolutionFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const pillars = [
    {
      icon: Brain,
      title: "24/7 AI Coach",
      description:
        "Chat with AI that knows you better than your doctor. Available anytime, anywhere.",
      features: [
        "Instant health answers",
        "Personalized advice",
        "Pattern recognition",
        "Smart recommendations",
      ],
      gradient: "from-purple-500 to-indigo-500",
    },
    {
      icon: Activity,
      title: "Health Book Library",
      description:
        "Chat with health book summaries. Ask questions, get instant expert knowledge.",
      features: [
        "Popular health books",
        "Instant summaries",
        "Expert knowledge",
        "Ask any question",
      ],
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: Users,
      title: "YouTube Intelligence",
      description:
        "Read videos instead of watching. Get notes, insights, and top comments instantly.",
      features: [
        "Video summaries",
        "Key takeaways",
        "Time-saving",
        "Best insights",
      ],
      gradient: "from-green-500 to-emerald-500",
    },
    {
      icon: MessageCircle,
      title: "Smart Tracking",
      description:
        "Track everything with AI that connects the dots between food, symptoms, and mood.",
      features: [
        "Food tracking",
        "Symptom patterns",
        "Mood insights",
        "Auto-correlations",
      ],
      gradient: "from-orange-500 to-red-500",
    },
  ];

  return (
    <div ref={ref} className="max-w-7xl mx-auto">
      <div className="grid md:grid-cols-2 gap-8">
        {pillars.map((pillar, index) => (
          <motion.div
            key={pillar.title}
            initial={{ opacity: 0, y: 100, rotateY: -25 }}
            whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
            transition={{ duration: 0.8, delay: index * 0.2 }}
            style={{
              transformStyle: "preserve-3d",
              rotateX: useTransform(
                scrollYProgress,
                [0, 1],
                [`${index % 2 ? 5 : -5}deg`, `${index % 2 ? -5 : 5}deg`],
              ),
              z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 75, 0]),
            }}
            whileHover={{
              scale: 1.02,
              rotateY: 5,
              z: 100,
              transition: { duration: 0.4 },
            }}
            className="group"
          >
            <Card className="h-full border-0 shadow-lg bg-background/80 backdrop-blur hover:shadow-2xl transition-all duration-500 group-hover:border-primary/20 transform-gpu">
              <CardContent className="p-8">
                <div className="space-y-6">
                  <motion.div
                    className="flex items-center gap-4"
                    style={{ transform: "translateZ(30px)" }}
                  >
                    <motion.div
                      className={`p-3 bg-gradient-to-r ${pillar.gradient} rounded-xl shadow-lg`}
                      whileHover={{ rotateY: 180 }}
                      transition={{ duration: 0.6 }}
                      style={{ transformStyle: "preserve-3d" }}
                    >
                      <pillar.icon className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-2xl font-bold">{pillar.title}</h3>
                  </motion.div>

                  <motion.p
                    className="text-muted-foreground leading-relaxed"
                    style={{ transform: "translateZ(20px)" }}
                  >
                    {pillar.description}
                  </motion.p>

                  <div className="space-y-3">
                    {pillar.features.map((feature, featureIndex) => (
                      <motion.div
                        key={feature}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{
                          duration: 0.4,
                          delay: index * 0.2 + featureIndex * 0.1,
                        }}
                        className="flex items-center gap-3"
                        style={{ transform: "translateZ(10px)" }}
                      >
                        <CheckCircle className="h-5 w-5 text-green-500 shrink-0" />
                        <span className="text-sm font-medium">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        whileInView={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.8 }}
        className="text-center mt-16"
        style={{
          rotateX: useTransform(scrollYProgress, [0, 1], ["3deg", "-3deg"]),
          transformStyle: "preserve-3d",
        }}
      >
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8 border border-primary/20 backdrop-blur">
          <h3 className="text-2xl font-bold mb-4">Your AI Health Universe</h3>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Chat with AI 24/7, read health books instantly, turn videos into
            knowledge, and track everything that matters. The smartest health
            platform ever built.
          </p>
        </div>
      </motion.div>
    </div>
  );
}

function AIFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const aiFeatures = [
    {
      icon: Brain,
      title: "24/7 AI Health Coach",
      description:
        "Chat with AI that knows you better than your doctor. Available anytime.",
      example: "'Why am I tired?' → Get instant personalized answers",
    },
    {
      icon: BarChart3,
      title: "Health Book Library",
      description: "Chat with summaries of popular health books instantly.",
      example: "Ask 'What does Dr. Gundry say about lectins?'",
    },
    {
      icon: Target,
      title: "YouTube Intelligence",
      description:
        "Read videos instead of watching. Get notes and top insights.",
      example: "2-hour podcast → 5-minute read with key takeaways",
    },
    {
      icon: TrendingUp,
      title: "Smart Pattern Detection",
      description: "AI discovers what foods trigger your symptoms.",
      example: "Found: Dairy → triggers inflammation",
    },
  ];

  return (
    <div ref={ref} className="max-w-7xl mx-auto">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        <motion.div
          style={{
            rotateY: useTransform(scrollYProgress, [0, 1], ["-15deg", "15deg"]),
            z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 100, 0]),
            transformStyle: "preserve-3d",
          }}
          whileHover={{ scale: 1.05, rotateY: 5 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-purple-500/10 via-background to-indigo-500/10 backdrop-blur-xl">
            <CardContent className="p-8">
              <motion.div
                className="text-center space-y-6"
                style={{ transformStyle: "preserve-3d" }}
              >
                <motion.div
                  className="relative"
                  whileHover={{ rotateY: 360 }}
                  transition={{ duration: 1 }}
                  style={{ transformStyle: "preserve-3d" }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur-3xl" />
                  <motion.div
                    className="relative bg-gradient-to-r from-purple-500 to-indigo-500 p-6 rounded-full w-24 h-24 mx-auto flex items-center justify-center"
                    style={{ transform: "translateZ(50px)" }}
                  >
                    <Brain className="h-12 w-12 text-white animate-pulse" />
                  </motion.div>
                </motion.div>
                <h3 className="text-2xl font-bold">AI-Powered Support</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Share experiences, track your progress, and get AI support.
                  Your complete health transformation ecosystem.
                </p>
                <motion.div
                  className="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 rounded-lg p-4"
                  style={{ transform: "translateZ(25px)" }}
                >
                  <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                    Ask Your AI:
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    "Why am I always tired after lunch?" → "Your blood sugar
                    spikes. Try protein first."
                  </div>
                </motion.div>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="space-y-6">
          {aiFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, x: 100, rotateY: 25 }}
              whileInView={{ opacity: 1, x: 0, rotateY: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              style={{
                transformStyle: "preserve-3d",
                rotateX: useTransform(
                  scrollYProgress,
                  [0, 1],
                  [`${index * 2}deg`, `${-index * 2}deg`],
                ),
              }}
              whileHover={{
                scale: 1.02,
                rotateY: -5,
                z: 50,
                transition: { duration: 0.3 },
              }}
            >
              <Card className="border-purple-200/50 bg-gradient-to-r from-purple-50/50 to-background hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <motion.div
                      className={`p-2 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg shrink-0`}
                      style={{ transform: "translateZ(20px)" }}
                    >
                      <feature.icon className="h-5 w-5 text-white" />
                    </motion.div>
                    <div className="space-y-2">
                      <h4 className="font-semibold">{feature.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {feature.description}
                      </p>
                      <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                        Example: {feature.example}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

function CommunityFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const communityStats: {
    label: string;
    value: string | number;
    suffix: string;
  }[] = [
    { label: "Success Stories", value: "Coming", suffix: "" },
    { label: "Expert Insights", value: "Soon", suffix: "" },
    { label: "Community Members", value: "Growing", suffix: "" },
    { label: "Shared Experiences", value: "Building", suffix: "" },
  ];

  return (
    <div ref={ref} className="max-w-7xl mx-auto">
      <div className="grid lg:grid-cols-3 gap-8">
        <motion.div
          style={{
            rotateY: useTransform(scrollYProgress, [0, 1], ["-20deg", "20deg"]),
            z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 75, 0]),
            transformStyle: "preserve-3d",
          }}
          className="lg:col-span-1"
          whileHover={{ scale: 1.02, rotateY: 10 }}
        >
          <Card className="h-full border-0 shadow-2xl bg-gradient-to-br from-green-500/10 via-background to-emerald-500/10 backdrop-blur">
            <CardContent className="p-8 text-center space-y-6">
              <motion.div
                className="relative"
                whileHover={{ rotateY: 180 }}
                transition={{ duration: 0.8 }}
                style={{ transformStyle: "preserve-3d" }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-2xl" />
                <motion.div
                  className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-4 rounded-full w-20 h-20 mx-auto flex items-center justify-center"
                  style={{ transform: "translateZ(40px)" }}
                >
                  <Users className="h-10 w-10 text-white" />
                </motion.div>
              </motion.div>
              <h3 className="text-2xl font-bold">Community Power</h3>
              <p className="text-muted-foreground leading-relaxed">
                Connect with others who understand your journey, share
                experiences, and celebrate victories together.
              </p>
              <div className="grid grid-cols-2 gap-4 pt-4">
                {communityStats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, scale: 0.8, rotateX: -45 }}
                    whileInView={{ opacity: 1, scale: 1, rotateX: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="text-center"
                    style={{
                      transformStyle: "preserve-3d",
                      transform: "translateZ(20px)",
                    }}
                  >
                    <div className="text-2xl font-bold text-green-600">
                      {typeof stat.value === "number" ? (
                        <AnimatedCounter value={stat.value} />
                      ) : (
                        stat.value
                      )}
                      {stat.suffix}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="lg:col-span-2 space-y-6">
          {[
            {
              icon: Heart,
              title: "Share Your Story",
              description:
                "Document your transformation journey and inspire others with your progress.",
              badges: [
                "Progress Updates",
                "Milestone Celebrations",
                "Challenge Support",
              ],
              gradient: "from-green-500 to-emerald-500",
              borderColor:
                "border-green-200/50 bg-gradient-to-r from-green-50/50",
            },
            {
              icon: MessageCircle,
              title: "Learn from Others",
              description:
                "Access real experiences from people with similar health challenges and goals.",
              badges: ["Success Stories", "Expert Tips", "Research Insights"],
              gradient: "from-blue-500 to-cyan-500",
              borderColor:
                "border-blue-200/50 bg-gradient-to-r from-blue-50/50",
            },
            {
              icon: Star,
              title: "Build Connections",
              description:
                "Form lasting friendships with people who understand your health journey.",
              badges: [
                "Accountability Partners",
                "Support Groups",
                "Expert Access",
              ],
              gradient: "from-purple-500 to-indigo-500",
              borderColor:
                "border-purple-200/50 bg-gradient-to-r from-purple-50/50",
            },
          ].map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 50, rotateX: -25 }}
              whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
              style={{
                transformStyle: "preserve-3d",
                rotateY: useTransform(
                  scrollYProgress,
                  [0, 1],
                  [`${index * 3}deg`, `${-index * 3}deg`],
                ),
              }}
              whileHover={{ scale: 1.02, z: 50 }}
            >
              <Card
                className={`${item.borderColor} to-background hover:shadow-lg transition-all duration-300`}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <motion.div
                      className={`p-2 bg-gradient-to-r ${item.gradient} rounded-lg`}
                      style={{ transform: "translateZ(25px)" }}
                      whileHover={{ rotateY: 360 }}
                      transition={{ duration: 0.8 }}
                    >
                      <item.icon className="h-5 w-5 text-white" />
                    </motion.div>
                    <div>
                      <h4 className="font-semibold mb-2">{item.title}</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        {item.description}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {item.badges.map((badge, badgeIndex) => (
                          <motion.div
                            key={badge}
                            initial={{ opacity: 0, scale: 0 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.3 + badgeIndex * 0.1 }}
                          >
                            <Badge variant="secondary">{badge}</Badge>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

function TechnologyFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const techStack = [
    {
      icon: Smartphone,
      title: "Progressive Web App",
      description:
        "Native app experience across all devices with offline capabilities",
      features: [
        "Cross-platform",
        "Offline sync",
        "Push notifications",
        "App-like experience",
      ],
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description:
        "Bank-level encryption and privacy controls to protect your health data",
      features: [
        "End-to-end encryption",
        "HIPAA compliant",
        "Zero-knowledge architecture",
        "User-controlled data",
      ],
    },
    {
      icon: Cloud,
      title: "Scalable Infrastructure",
      description: "Modern cloud architecture built to grow with our community",
      features: [
        "99.9% uptime",
        "Global CDN",
        "Auto-scaling",
        "Real-time sync",
      ],
    },
  ];

  return (
    <div ref={ref} className="max-w-6xl mx-auto">
      <div className="grid md:grid-cols-3 gap-8">
        {techStack.map((tech, index) => (
          <motion.div
            key={tech.title}
            initial={{ opacity: 0, y: 100, rotateY: -30 }}
            whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
            transition={{ duration: 0.8, delay: index * 0.2 }}
            style={{
              transformStyle: "preserve-3d",
              rotateX: useTransform(
                scrollYProgress,
                [0, 1],
                [`${5 - index * 3}deg`, `${-5 + index * 3}deg`],
              ),
              z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 50, 0]),
            }}
            whileHover={{
              scale: 1.05,
              rotateY: 10,
              z: 100,
              transition: { duration: 0.4 },
            }}
          >
            <Card className="h-full border-0 shadow-lg bg-background/80 backdrop-blur hover:shadow-2xl transition-all duration-500 transform-gpu">
              <CardContent className="p-8 text-center space-y-6">
                <motion.div
                  className="relative"
                  whileHover={{ rotateY: 180 }}
                  transition={{ duration: 0.8 }}
                  style={{ transformStyle: "preserve-3d" }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-slate-500/10 to-gray-500/10 rounded-full blur-xl" />
                  <motion.div
                    className="relative p-4 bg-gradient-to-r from-slate-600 to-gray-600 rounded-full w-20 h-20 mx-auto flex items-center justify-center"
                    style={{ transform: "translateZ(30px)" }}
                  >
                    <tech.icon className="h-10 w-10 text-white" />
                  </motion.div>
                </motion.div>
                <div>
                  <h3 className="text-xl font-bold mb-3">{tech.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                    {tech.description}
                  </p>
                </div>
                <div className="space-y-2">
                  {tech.features.map((feature, featureIndex) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{
                        duration: 0.4,
                        delay: index * 0.2 + featureIndex * 0.1,
                      }}
                      className="flex items-center justify-center gap-2"
                      style={{ transform: "translateZ(15px)" }}
                    >
                      <CheckCircle className="h-4 w-4 text-green-500 shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

function RoadmapFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const roadmapPhases = [
    {
      phase: "Q1 2025",
      title: "Foundation Launch",
      status: "active",
      features: [
        "Core tracking features",
        "Basic AI insights",
        "Community platform",
        "Mobile-first design",
      ],
      milestone: "Beta Release",
    },
    {
      phase: "Q2 2025",
      title: "Intelligence Upgrade",
      status: "planned",
      features: [
        "Advanced AI analytics",
        "Pattern recognition",
        "Predictive insights",
        "Custom recommendations",
      ],
      milestone: "AI-Powered Insights",
    },
    {
      phase: "Q3 2025",
      title: "Community Expansion",
      status: "planned",
      features: [
        "Expert content",
        "Story sharing",
        "Group challenges",
        "Success celebrations",
      ],
      milestone: "Social Features",
    },
    {
      phase: "Q4 2025",
      title: "Enterprise Features",
      status: "planned",
      features: [
        "Provider integrations",
        "API access",
        "Advanced analytics",
        "Custom reporting",
      ],
      milestone: "Professional Tools",
    },
  ];

  return (
    <div ref={ref} className="max-w-6xl mx-auto">
      <div className="relative">
        <motion.div
          className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-primary/50 to-secondary/50 hidden md:block"
          style={{
            scaleY: useTransform(scrollYProgress, [0, 1], [0, 1]),
            transformOrigin: "top",
          }}
        />

        <div className="space-y-12">
          {roadmapPhases.map((phase, index) => (
            <motion.div
              key={phase.phase}
              initial={{
                opacity: 0,
                y: 100,
                rotateY: index % 2 === 0 ? -25 : 25,
              }}
              whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              style={{
                transformStyle: "preserve-3d",
                rotateX: useTransform(
                  scrollYProgress,
                  [0, 1],
                  [`${index % 2 ? 3 : -3}deg`, `${index % 2 ? -3 : 3}deg`],
                ),
                z: useTransform(scrollYProgress, [0, 0.5, 1], [0, 50, 0]),
              }}
              className={`relative md:w-1/2 ${index % 2 === 0 ? "md:mr-auto md:pr-8" : "md:ml-auto md:pl-8"}`}
              whileHover={{ scale: 1.02, z: 75 }}
            >
              <motion.div
                className="absolute top-8 hidden md:block"
                style={{ transform: "translateZ(30px)" }}
              >
                <motion.div
                  className={`absolute ${index % 2 === 0 ? "-right-6" : "-left-6"} w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg`}
                  whileHover={{ scale: 1.5 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>

              <Card
                className={`border-0 shadow-lg ${phase.status === "active" ? "bg-gradient-to-br from-primary/10 to-secondary/10 border-primary/20" : "bg-background/80 backdrop-blur"} hover:shadow-xl transition-all duration-300`}
              >
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge
                        variant={
                          phase.status === "active" ? "default" : "outline"
                        }
                      >
                        {phase.phase}
                      </Badge>
                      <div className="flex items-center gap-2">
                        {phase.status === "active" ? (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <Rocket className="h-4 w-4 text-primary" />
                          </motion.div>
                        ) : (
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className="text-sm text-muted-foreground">
                          {phase.milestone}
                        </span>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold">{phase.title}</h3>

                    <div className="space-y-2">
                      {phase.features.map((feature, featureIndex) => (
                        <motion.div
                          key={feature}
                          initial={{
                            opacity: 0,
                            x: index % 2 === 0 ? -20 : 20,
                          }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.4,
                            delay: index * 0.2 + featureIndex * 0.1,
                          }}
                          className="flex items-center gap-3"
                          style={{ transform: "translateZ(10px)" }}
                        >
                          <CheckCircle
                            className={`h-4 w-4 ${phase.status === "active" ? "text-primary" : "text-muted-foreground"} shrink-0`}
                          />
                          <span className="text-sm">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

function CTAFrame() {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  return (
    <div ref={ref} className="max-w-4xl mx-auto text-center space-y-12">
      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.8 }}
        whileInView={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8 }}
        className="space-y-6"
        style={{
          transformStyle: "preserve-3d",
          rotateX: useTransform(scrollYProgress, [0, 1], ["5deg", "-5deg"]),
        }}
      >
        <motion.div
          className="relative"
          whileHover={{ rotateY: 360 }}
          transition={{ duration: 1.5 }}
          style={{ transformStyle: "preserve-3d" }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-3xl" />
          <motion.div
            className="relative bg-gradient-to-r from-primary to-secondary p-6 rounded-full w-32 h-32 mx-auto flex items-center justify-center"
            style={{ transform: "translateZ(50px)" }}
          >
            <Heart className="h-16 w-16 text-white animate-pulse" />
          </motion.div>
        </motion.div>

        <h3 className="text-3xl font-bold">
          Your Health Community{" "}
          <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Awaits
          </span>
        </h3>

        <p className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto px-4 sm:px-0">
          <span className="hidden sm:inline">
            Share your transformation story, track your progress with smart
            tools, and get AI support when you need it. Join the most supportive
            health community ever built.
          </span>
          <span className="sm:hidden">
            Share your story, track smart, get AI support. Join the community.
          </span>
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="grid md:grid-cols-3 gap-6"
        style={{ transformStyle: "preserve-3d" }}
      >
        {[
          {
            icon: Brain,
            title: "24/7 AI Coach",
            desc: "Chat anytime, get instant answers",
            color: "text-primary",
            bg: "from-primary/5",
          },
          {
            icon: Heart,
            title: "Health Books",
            desc: "500+ books, instant summaries",
            color: "text-secondary",
            bg: "from-secondary/5",
          },
          {
            icon: Zap,
            title: "Video Intelligence",
            desc: "Read videos, save hours",
            color: "text-green-500",
            bg: "from-green-500/5",
          },
        ].map((item, index) => (
          <motion.div
            key={item.title}
            initial={{ opacity: 0, rotateY: -45, z: -100 }}
            whileInView={{ opacity: 1, rotateY: 0, z: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            whileHover={{
              scale: 1.05,
              rotateY: 10,
              z: 50,
              transition: { duration: 0.3 },
            }}
            style={{ transformStyle: "preserve-3d" }}
          >
            <Card
              className={`border-${item.color}/20 bg-gradient-to-br ${item.bg} to-background hover:shadow-lg transition-all duration-300`}
            >
              <CardContent className="p-6 text-center">
                <motion.div style={{ transform: "translateZ(20px)" }}>
                  <item.icon className={`h-8 w-8 ${item.color} mx-auto mb-3`} />
                </motion.div>
                <h4 className="font-semibold mb-2">{item.title}</h4>
                <p className="text-sm text-muted-foreground">{item.desc}</p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.8, rotateX: -30 }}
        whileInView={{ opacity: 1, scale: 1, rotateX: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/5 to-primary/10 rounded-2xl p-8 border border-primary/20 backdrop-blur"
        style={{ transformStyle: "preserve-3d" }}
      >
        <DetailedWaitlistForm
          trigger={
            <motion.div
              whileHover={{ scale: 1.05, z: 30 }}
              whileTap={{ scale: 0.95 }}
              style={{ transformStyle: "preserve-3d" }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-primary via-primary to-primary/90 hover:from-primary/90 hover:via-primary hover:to-primary text-base sm:text-lg px-6 sm:px-12 py-4 sm:py-6 shadow-2xl hover:shadow-3xl transition-all duration-300 transform-gpu"
              >
                <Star className="mr-1 sm:mr-3 h-4 w-4 sm:h-6 sm:w-6 animate-pulse" />
                <span className="hidden sm:inline">
                  Join the Waitlist - Health Pioneer
                </span>
                <span className="sm:hidden">Join Waitlist</span>
                <ArrowRight className="ml-1 sm:ml-3 h-4 w-4 sm:h-6 sm:w-6 transition-transform group-hover:translate-x-1" />
              </Button>
            </motion.div>
          }
        />
        <p className="text-xs sm:text-sm text-muted-foreground mt-3 sm:mt-4 px-4 sm:px-0">
          🚀 <strong>Early access only</strong> -
          <span className="hidden sm:inline">
            Your supportive health community is waiting
          </span>
          <span className="sm:hidden">Community awaits</span>
        </p>
      </motion.div>
    </div>
  );
}

function VisionFrameComponent({
  frame,
  index,
  isActive,
  onInView,
}: {
  frame: VisionFrame;
  index: number;
  isActive: boolean;
  onInView: () => void;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start center", "end center"],
  });

  return (
    <motion.section
      ref={ref}
      className={`min-h-screen flex items-center py-24 bg-gradient-to-br ${frame.background || "from-background to-background"} relative overflow-hidden`}
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, amount: 0.3 }}
      onViewportEnter={onInView}
      style={{
        perspective: "1000px",
        clipPath: useTransform(
          scrollYProgress,
          [0, 0.1, 0.9, 1],
          [
            "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
            "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
            "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
            "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
          ],
        ),
      }}
    >
      <motion.div
        className="container relative z-10"
        style={{
          transformStyle: "preserve-3d",
          rotateX: useTransform(
            scrollYProgress,
            [0, 0.5, 1],
            ["3deg", "0deg", "-3deg"],
          ),
          y: useTransform(scrollYProgress, [0, 1], ["0%", "-10%"]),
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 100, rotateX: -20 }}
          whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center mb-16"
          style={{ transformStyle: "preserve-3d" }}
        >
          <motion.div style={{ transform: "translateZ(25px)" }}>
            <Badge
              variant="outline"
              className="mb-4 bg-background/50 backdrop-blur border-primary/20"
            >
              Frame {index + 1} of 8
            </Badge>
          </motion.div>
          <motion.h2
            className="text-2xl sm:text-3xl lg:text-5xl xl:text-6xl font-bold tracking-tight mb-4 sm:mb-6 px-4 sm:px-0"
            style={{
              transform: "translateZ(50px)",
              textShadow: "0 5px 15px rgba(0,0,0,0.2)",
            }}
          >
            {frame.title}
          </motion.h2>
          {frame.subtitle && (
            <motion.p
              className="text-base sm:text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4 sm:px-0"
              style={{ transform: "translateZ(30px)" }}
            >
              {frame.subtitle}
            </motion.p>
          )}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          style={{
            transformStyle: "preserve-3d",
            transform: "translateZ(20px)",
          }}
        >
          {frame.content}
        </motion.div>
      </motion.div>
    </motion.section>
  );
}
