import { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Our Vision - The Future of Health Tracking",
  description: "Discover the future of personalized health tracking, AI insights, and community-driven wellness transformation.",
  openGraph: {
    title: "Our Vision - The Future of Health Tracking",
    description: "Discover the future of personalized health tracking, AI insights, and community-driven wellness transformation.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Our Vision - The Future of Health Tracking",
    description: "Discover the future of personalized health tracking, AI insights, and community-driven wellness transformation.",
  },
}

export default function VisionLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
} 