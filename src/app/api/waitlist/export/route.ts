import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    // Simple authentication check - in production, use proper auth
    const authHeader = request.headers.get("authorization");
    if (!authHeader || authHeader !== `Bearer ${process.env.EXPORT_API_KEY}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const waitlistEntries = await prisma.waitlistEntry.findMany({
      orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
    });

    // Convert to CSV format
    const csvHeaders = [
      "ID",
      "Email",
      "First Name",
      "Last Name",
      "Health Conditions",
      "Dietary Goals",
      "Current Diet",
      "Referral Source",
      "Interests",
      "Priority",
      "Status",
      "Created At",
      "Updated At",
    ].join(",");

    const csvRows = waitlistEntries.map((entry) =>
      [
        entry.id,
        entry.email,
        entry.firstName || "",
        entry.lastName || "",
        entry.healthConditions.join(";"),
        entry.dietaryGoals.join(";"),
        entry.currentDiet || "",
        entry.referralSource || "",
        entry.interests.join(";"),
        entry.priority,
        entry.status,
        entry.createdAt.toISOString(),
        entry.updatedAt.toISOString(),
      ]
        .map((field) => `"${field}"`)
        .join(","),
    );

    const csv = [csvHeaders, ...csvRows].join("\n");

    return new NextResponse(csv, {
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename="waitlist-export-${new Date().toISOString().split("T")[0]}.csv"`,
      },
    });
  } catch (error) {
    console.error("Export error:", error);
    return NextResponse.json(
      { error: "Failed to export waitlist data" },
      { status: 500 },
    );
  }
}
