import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { waitlistSchema } from "@/lib/validations";
import { sendWelcomeEmail, sendAdminNotification } from "@/lib/email";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Debug: Log the raw body data
    console.log("🔍 DEBUG: Raw request body:", JSON.stringify(body, null, 2));

    // Validate the request body
    const validatedData = waitlistSchema.parse(body);
    console.log("📝 New waitlist signup attempt for:", validatedData.email);
    console.log("🔍 Validated data:", JSON.stringify(validatedData, null, 2));

    // Check if email already exists
    const existingEntry = await prisma.waitlistEntry.findUnique({
      where: { email: validatedData.email },
    });

    if (existingEntry) {
      console.log("❌ Email already registered:", validatedData.email);
      return NextResponse.json(
        { error: "Email already registered" },
        { status: 400 },
      );
    }

    // Create waitlist entry
    const waitlistEntry = await prisma.waitlistEntry.create({
      data: {
        email: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        healthConditions: validatedData.healthConditions || [],
        dietaryGoals: validatedData.dietaryGoals || [],
        currentDiet: validatedData.currentDiet,
        referralSource: validatedData.referralSource,
        interests: validatedData.interests || [],
        priority:
          typeof body.priority === "number"
            ? body.priority
            : body.priority
              ? parseInt(body.priority)
              : 1,
        metadata: validatedData.monthlyBudget
          ? {
              monthlyBudget: validatedData.monthlyBudget,
              submissionTimestamp: new Date().toISOString(),
              userAgent: request.headers.get("user-agent"),
            }
          : {
              submissionTimestamp: new Date().toISOString(),
              userAgent: request.headers.get("user-agent"),
            },
      },
    });

    console.log("✅ Waitlist entry created successfully:", waitlistEntry.id);

    // Send welcome email
    if (process.env.WAITLIST_WELCOME_EMAIL_ENABLED === "true") {
      try {
        await sendWelcomeEmail(validatedData.email, validatedData.firstName);
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);
        // Don't fail the request if email fails
      }
    }

    // Send admin notification
    try {
      await sendAdminNotification(waitlistEntry);
    } catch (adminEmailError) {
      console.error("Failed to send admin notification:", adminEmailError);
      // Don't fail the request if admin email fails
    }

    return NextResponse.json({
      success: true,
      message: "Successfully joined waitlist",
      id: waitlistEntry.id,
    });
  } catch (error) {
    console.error("❌ Waitlist signup error:", error);

    if (error && (error as any).name === "ZodError") {
      console.error("❌ Validation error details:", (error as any).errors);
      return NextResponse.json(
        { error: "Invalid form data", details: (error as any).errors },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function GET() {
  try {
    const count = await prisma.waitlistEntry.count({
      where: { status: "ACTIVE" },
    });

    return NextResponse.json({ count });
  } catch (error) {
    console.error("Error fetching waitlist count:", error);
    return NextResponse.json(
      { error: "Failed to fetch waitlist count" },
      { status: 500 },
    );
  }
}
