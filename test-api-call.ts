import fetch from "node-fetch";

const testFormData = {
  email: "<EMAIL>",
  firstName: "<PERSON>",
  lastName: "Doe",
  healthConditions: ["Skin Conditions"],
  dietaryGoals: ["Autoimmune Management"],
  currentDiet: "Vegetarian/Vegan",
  referralSource: null,
  interests: ["Health Tracking"],
  monthlyBudget: "50-100",
  priority: 3,
};

async function testApiCall() {
  try {
    console.log("🚀 Testing API call with data:");
    console.log(JSON.stringify(testFormData, null, 2));

    const response = await fetch("http://localhost:3222/api/waitlist", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testFormData),
    });

    const result = await response.json();

    console.log("\n📊 API Response:");
    console.log("Status:", response.status);
    console.log("Result:", JSON.stringify(result, null, 2));

    if (!response.ok) {
      console.error("❌ API call failed");
    } else {
      console.log("✅ API call successful");
    }
  } catch (error) {
    console.error("❌ Error making API call:", error);
  }
}

testApiCall();
