#!/bin/bash

# Service Test Runner Script
# Runs all React service tests via Docker and generates comprehensive reports

set -e

echo "🚀 Starting React Services Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_FILES=()

# Function to run a specific test file
run_test_file() {
    local test_file=$1
    local test_name=$(basename "$test_file" .test.ts)
    
    echo -e "${BLUE}Running ${test_name}...${NC}"
    
    if docker exec react_dev_frontend npm run test:single -- "/app/src/services/__tests__/${test_file}" 2>/dev/null; then
        echo -e "${GREEN}✅ ${test_name} - PASSED${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "${RED}❌ ${test_name} - FAILED${NC}"
        ((FAILED_TESTS++))
    fi
    
    ((TOTAL_TESTS++))
    TEST_FILES+=("$test_file")
}

# Function to run all service tests
run_all_service_tests() {
    echo -e "${YELLOW}📋 Discovering test files...${NC}"
    
    # Get list of all test files
    test_files=$(docker exec react_dev_frontend find /app/src/services/__tests__ -name "*.test.ts" -type f | sed 's|/app/src/services/__tests__/||' | sort)
    
    if [ -z "$test_files" ]; then
        echo -e "${RED}No test files found!${NC}"
        return 1
    fi
    
    echo "Found $(echo "$test_files" | wc -l) test files:"
    echo "$test_files" | sed 's/^/  - /'
    echo ""
    
    # Run each test file
    for test_file in $test_files; do
        run_test_file "$test_file"
        sleep 1  # Brief pause between tests
    done
}

# Function to generate test summary
generate_summary() {
    echo ""
    echo "📊 TEST EXECUTION SUMMARY"
    echo "========================="
    echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED!${NC}"
        return 0
    else
        local pass_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        echo -e "Pass Rate: ${YELLOW}${pass_rate}%${NC}"
        echo -e "${YELLOW}⚠️  Some tests failed. Check individual results above.${NC}"
        return 1
    fi
}

# Function to check Docker container
check_docker() {
    echo -e "${BLUE}🐳 Checking Docker container...${NC}"
    
    if ! docker ps | grep -q react_dev_frontend; then
        echo -e "${RED}❌ React frontend container is not running!${NC}"
        echo "Please start the container with: make dev"
        exit 1
    fi
    
    echo -e "${GREEN}✅ React frontend container is running${NC}"
}

# Function to install any missing test dependencies
setup_test_environment() {
    echo -e "${BLUE}🔧 Setting up test environment...${NC}"
    
    # Check if vitest is available
    if ! docker exec react_dev_frontend npm list vitest >/dev/null 2>&1; then
        echo -e "${YELLOW}Installing missing test dependencies...${NC}"
        docker exec react_dev_frontend npm install --save-dev vitest @vitest/ui
    fi
    
    echo -e "${GREEN}✅ Test environment ready${NC}"
}

# Function to generate detailed test report
generate_detailed_report() {
    local report_file="test-results-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# React Services Test Report

**Generated:** $(date)
**Total Test Files:** $TOTAL_TESTS
**Passed:** $PASSED_TESTS
**Failed:** $FAILED_TESTS
**Pass Rate:** $((PASSED_TESTS * 100 / TOTAL_TESTS))%

## Test Coverage

The following service test files were executed:

EOF

    for test_file in "${TEST_FILES[@]}"; do
        echo "- \`$test_file\`" >> "$report_file"
    done

    cat >> "$report_file" << EOF

## Services Tested

- **adminService** - Admin panel functionality
- **analyticsService** - Analytics and metrics
- **axios-client** - HTTP client configuration
- **config** - Application configuration
- **forumService** - Forum management
- **index** - Service module exports
- **notificationService** - User notifications
- **searchUtils** - Search utilities
- **socialService** - Social features
- **topicService** - Topic management
- **websocketService** - Real-time communication

## Test Framework

- **Framework:** Vitest
- **Mocking:** Vitest vi mocks
- **Environment:** Docker container
- **Coverage:** Comprehensive unit tests with mocked dependencies

## Next Steps

1. Fix failing tests by aligning with actual service implementations
2. Add integration tests for critical user flows
3. Implement E2E tests for complete user journeys
4. Add performance benchmarks for service methods
5. Set up CI/CD pipeline for automated testing

EOF

    echo -e "${GREEN}📋 Detailed report saved to: $report_file${NC}"
}

# Main execution
main() {
    check_docker
    setup_test_environment
    run_all_service_tests
    generate_summary
    generate_detailed_report
    
    echo ""
    echo -e "${BLUE}🏁 Test execution completed!${NC}"
    
    if [ $FAILED_TESTS -gt 0 ]; then
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo "  --summary     Show only summary without running tests"
        echo "  --file FILE   Run specific test file"
        exit 0
        ;;
    --summary)
        echo "Test summary mode not implemented yet"
        exit 0
        ;;
    --file)
        if [ -z "$2" ]; then
            echo "Error: --file requires a filename"
            exit 1
        fi
        check_docker
        setup_test_environment
        run_test_file "$2"
        exit $?
        ;;
    "")
        main
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac 