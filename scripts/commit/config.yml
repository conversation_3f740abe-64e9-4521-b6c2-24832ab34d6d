# Fast Commit Configuration
# Optimized for speed and reliability - always completes within 10 seconds

run_validations_before_commit: true
validate_branch_naming: false  # Disabled for speed
validate_commit_message: false  # Disabled for speed
hooks_are_warnings_only: true  # Always treat failures as warnings

# Branch prefixes (for reference, but validation is disabled)
branch_prefixes:
  - "feature/"
  - "feat/"
  - "bugfix/"
  - "hotfix/"
  - "release/"
  - "docs/"
  - "chore/"
  - "refactor/"
  - "test/"

# Commit message pattern (for reference, but validation is disabled)
commit_message_pattern: "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\\(\\w+\\))?: .{1,100}"
require_issue_reference: false

# Fast hooks with strict timeouts
hooks:
  - name: "format_code"
    enabled: true
    timeout: 3  # 3 seconds max
  - name: "clean_comments"
    enabled: true
    timeout: 2  # 2 seconds max
  - name: "run_tests"
    enabled: false  # Disabled for speed
  - name: "validate_types"
    enabled: false  # Disabled for speed
  - name: "lint_code"
    enabled: false  # Disabled for speed

# File limits for performance
max_files_per_hook: 10
max_file_size_kb: 100 