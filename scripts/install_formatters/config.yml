# Install Formatters Configuration

# General settings
install_method: pip  # pip, npm, or system
use_virtual_environment: true
virtual_environment_path: env

# Python formatters
python_formatters:
  - name: black
    version: "23.1.0"
    enabled: true
  - name: isort
    version: "5.12.0"
    enabled: true
  - name: flake8
    version: "6.0.0"
    enabled: true
  - name: mypy
    version: "1.1.1"
    enabled: true
  - name: ruff
    version: "0.0.54"
    enabled: true

# JavaScript/TypeScript formatters
js_formatters:
  - name: prettier
    version: "2.8.4"
    enabled: true
  - name: eslint
    version: "8.36.0"
    enabled: true
  - name: typescript-eslint
    version: "5.54.1"
    enabled: true

# HTML/Template formatters
html_formatters:
  - name: djlint
    version: "1.19.16"
    enabled: true
  - name: prettier
    version: "2.8.4"
    enabled: true

# Shell formatters
shell_formatters:
  - name: shfmt
    version: "3.5.1"
    enabled: true
  - name: shellcheck
    version: "0.8.0"
    enabled: true

# YAML formatters
yaml_formatters:
  - name: yamllint
    version: "1.29.0"
    enabled: true
  - name: prettier
    version: "2.8.4"
    enabled: true 