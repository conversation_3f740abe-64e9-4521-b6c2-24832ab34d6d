"""
Formatter Installation Script

This script installs code formatters and linters for the project.
It handles Python, JavaScript/TypeScript, HTML, Shell, and YAML formatters.
"""

import os
import sys
import yaml
import argparse
import subprocess
import platform
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple


sys.path.append(str(Path(__file__).parent.parent))
try:
    from logger import get_logger
except ImportError:

    class FallbackLogger:
        def __init__(self, script_name, verbose=False):
            self.script_name = script_name
            self.verbose = verbose
            self.stats = {}

        def start_script(self):
            pass

        def end_script(self):
            return 0

        def info(self, message):
            if self.verbose:
                print(message)

        def debug(self, message):
            pass

        def warning(self, message):
            if self.verbose:
                print(f"WARNING: {message}")

        def error(self, message):
            print(f"ERROR: {message}")

        def critical(self, message):
            print(f"CRITICAL: {message}")

        def log_file_action(self, filepath, action, **stats):
            if self.verbose:
                print(f"{action.capitalize()}: {filepath}")
            for key, value in stats.items():
                if key not in self.stats:
                    self.stats[key] = 0
                self.stats[key] += value

        def print_summary(self):
            summary_lines = ["", "Summary:"]
            for key, value in self.stats.items():
                pretty_key = " ".join(word.capitalize() for word in key.split("_"))
                summary_lines.append(f"{pretty_key}: {value}")
            print("\n".join(summary_lines))
            return "\n".join(summary_lines)

    def get_logger(script_name, verbose=False):
        return FallbackLogger(script_name, verbose)


class FormatterInstaller:
    """Installer for code formatters and linters."""

    def __init__(self, config_path: Optional[str] = None, verbose: bool = False):
        """
        Initialize the FormatterInstaller with a configuration.

        Args:
            config_path: Path to the configuration file
            verbose: Whether to show verbose output
        """
        self.logger = get_logger("install_formatters", verbose)
        self.logger.start_script()

        self.config = self._load_config(config_path)
        self.errors = []
        self.warnings = []
        self.python_cmd = self._get_python_command()
        self.npm_cmd = "npm"

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from a YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dictionary containing configuration values
        """
        default_config = {
            "install_method": "pip",
            "use_virtual_environment": True,
            "virtual_environment_path": "env",
            "python_formatters": [
                {"name": "black", "version": "23.1.0", "enabled": True},
                {"name": "isort", "version": "5.12.0", "enabled": True},
                {"name": "flake8", "version": "6.0.0", "enabled": True},
                {"name": "mypy", "version": "1.1.1", "enabled": True},
                {"name": "ruff", "version": "0.0.54", "enabled": True},
            ],
            "js_formatters": [
                {"name": "prettier", "version": "2.8.4", "enabled": True},
                {"name": "eslint", "version": "8.36.0", "enabled": True},
                {"name": "typescript-eslint", "version": "5.54.1", "enabled": True},
            ],
            "html_formatters": [
                {"name": "djlint", "version": "1.19.16", "enabled": True},
                {"name": "prettier", "version": "2.8.4", "enabled": True},
            ],
            "shell_formatters": [
                {"name": "shfmt", "version": "3.5.1", "enabled": True},
                {"name": "shellcheck", "version": "0.8.0", "enabled": True},
            ],
            "yaml_formatters": [
                {"name": "yamllint", "version": "1.29.0", "enabled": True},
                {"name": "prettier", "version": "2.8.4", "enabled": True},
            ],
        }

        if config_path and os.path.isfile(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    user_config = yaml.safe_load(f)
                    if user_config:

                        for key, value in user_config.items():
                            if (
                                key in default_config
                                and isinstance(value, list)
                                and isinstance(default_config[key], list)
                            ):

                                default_config[key] = value
                            else:
                                default_config[key] = value
            except Exception as e:
                self.logger.error(f"Error loading config file: {e}")

        return default_config

    def _get_python_command(self) -> str:
        """
        Get the appropriate Python command to use.

        Returns:
            Python command to use
        """

        if self.config.get("use_virtual_environment", True):
            venv_path = self.config.get("virtual_environment_path", "env")

            if os.name == "nt":
                python_path = os.path.join(venv_path, "Scripts", "python.exe")
            else:
                python_path = os.path.join(venv_path, "bin", "python")

            if os.path.exists(python_path):
                return python_path

            self.logger.warning(
                f"Virtual environment Python not found at {python_path}, falling back to system Python"
            )

        return "python3" if os.name != "nt" else "python"

    def _run_command(self, cmd: List[str]) -> Tuple[bool, str, str]:
        """
        Run a command.

        Args:
            cmd: Command to run

        Returns:
            Tuple of (success, stdout, stderr)
        """
        self.logger.debug(f"Running command: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)

            stdout = result.stdout.strip()
            stderr = result.stderr.strip()

            if result.returncode != 0:
                self.logger.error(f"Command failed: {' '.join(cmd)}")
                if stderr:
                    self.logger.error(stderr)
                return False, stdout, stderr

            if stdout and self.logger.verbose:
                self.logger.debug(stdout)

            return True, stdout, stderr
        except Exception as e:
            self.logger.error(f"Failed to execute command: {e}")
            return False, "", str(e)

    def install_python_formatters(self) -> bool:
        """
        Install Python formatters.

        Returns:
            True if installation was successful, False otherwise
        """
        formatters = self.config.get("python_formatters", [])
        self.logger.info(
            f"Installing Python formatters: {', '.join(f['name'] for f in formatters if f.get('enabled', True))}"
        )

        success = True
        installed_count = 0

        for formatter in formatters:
            if not formatter.get("enabled", True):
                continue

            name = formatter.get("name", "")
            version = formatter.get("version", "")
            package = f"{name}=={version}" if version else name

            self.logger.info(f"Installing {package}")
            cmd = [self.python_cmd, "-m", "pip", "install", package]

            result, _, _ = self._run_command(cmd)
            success = success and result

            if result:
                installed_count += 1
                self.logger.info(f"Successfully installed {package}")
            else:
                self.errors.append(f"Failed to install {package}")

        self.logger.stats.update(
            {
                "python_formatters_installed": installed_count,
                "python_formatters_failed": len(
                    [f for f in formatters if f.get("enabled", True)]
                )
                - installed_count,
            }
        )

        return success

    def install_js_formatters(self) -> bool:
        """
        Install JavaScript/TypeScript formatters.

        Returns:
            True if installation was successful, False otherwise
        """
        formatters = self.config.get("js_formatters", [])
        self.logger.info(
            f"Installing JavaScript/TypeScript formatters: {', '.join(f['name'] for f in formatters if f.get('enabled', True))}"
        )

        success = True
        installed_count = 0

        for formatter in formatters:
            if not formatter.get("enabled", True):
                continue

            name = formatter.get("name", "")
            version = formatter.get("version", "")
            package = f"{name}@{version}" if version else name

            self.logger.info(f"Installing {package}")
            cmd = [self.npm_cmd, "install", "-g", package]

            result, _, _ = self._run_command(cmd)
            success = success and result

            if result:
                installed_count += 1
                self.logger.info(f"Successfully installed {package}")
            else:
                self.errors.append(f"Failed to install {package}")

        self.logger.stats.update(
            {
                "js_formatters_installed": installed_count,
                "js_formatters_failed": len(
                    [f for f in formatters if f.get("enabled", True)]
                )
                - installed_count,
            }
        )

        return success

    def install_html_formatters(self) -> bool:
        """
        Install HTML formatters.

        Returns:
            True if installation was successful, False otherwise
        """
        formatters = self.config.get("html_formatters", [])
        self.logger.info(
            f"Installing HTML formatters: {', '.join(f['name'] for f in formatters if f.get('enabled', True))}"
        )

        success = True
        installed_count = 0

        for formatter in formatters:
            if not formatter.get("enabled", True):
                continue

            name = formatter.get("name", "")
            version = formatter.get("version", "")

            if name in ["djlint", "html-linter"]:
                package = f"{name}=={version}" if version else name
                self.logger.info(f"Installing {package} with pip")
                cmd = [self.python_cmd, "-m", "pip", "install", package]
            else:
                package = f"{name}@{version}" if version else name
                self.logger.info(f"Installing {package} with npm")
                cmd = [self.npm_cmd, "install", "-g", package]

            result, _, _ = self._run_command(cmd)
            success = success and result

            if result:
                installed_count += 1
                self.logger.info(f"Successfully installed {package}")
            else:
                self.errors.append(f"Failed to install {package}")

        self.logger.stats.update(
            {
                "html_formatters_installed": installed_count,
                "html_formatters_failed": len(
                    [f for f in formatters if f.get("enabled", True)]
                )
                - installed_count,
            }
        )

        return success

    def install_shell_formatters(self) -> bool:
        """
        Install Shell formatters.

        Returns:
            True if installation was successful, False otherwise
        """
        formatters = self.config.get("shell_formatters", [])
        self.logger.info(
            f"Installing Shell formatters: {', '.join(f['name'] for f in formatters if f.get('enabled', True))}"
        )

        success = True
        installed_count = 0

        for formatter in formatters:
            if not formatter.get("enabled", True):
                continue

            name = formatter.get("name", "")
            version = formatter.get("version", "")

            if platform.system() == "Darwin":
                self.logger.info(f"Installing {name} with Homebrew")
                cmd = ["brew", "install", name]
            elif platform.system() == "Linux":

                self.logger.info(f"Installing {name} with apt-get")
                cmd = ["sudo", "apt-get", "install", "-y", name]
            else:
                self.logger.warning(f"Unsupported platform for {name} installation")
                continue

            result, _, _ = self._run_command(cmd)
            success = success and result

            if result:
                installed_count += 1
                self.logger.info(f"Successfully installed {name}")
            else:
                self.errors.append(f"Failed to install {name}")

        self.logger.stats.update(
            {
                "shell_formatters_installed": installed_count,
                "shell_formatters_failed": len(
                    [f for f in formatters if f.get("enabled", True)]
                )
                - installed_count,
            }
        )

        return success

    def install_yaml_formatters(self) -> bool:
        """
        Install YAML formatters.

        Returns:
            True if installation was successful, False otherwise
        """
        formatters = self.config.get("yaml_formatters", [])
        self.logger.info(
            f"Installing YAML formatters: {', '.join(f['name'] for f in formatters if f.get('enabled', True))}"
        )

        success = True
        installed_count = 0

        for formatter in formatters:
            if not formatter.get("enabled", True):
                continue

            name = formatter.get("name", "")
            version = formatter.get("version", "")

            if name in ["yamllint"]:
                package = f"{name}=={version}" if version else name
                self.logger.info(f"Installing {package} with pip")
                cmd = [self.python_cmd, "-m", "pip", "install", package]
            else:
                package = f"{name}@{version}" if version else name
                self.logger.info(f"Installing {package} with npm")
                cmd = [self.npm_cmd, "install", "-g", package]

            result, _, _ = self._run_command(cmd)
            success = success and result

            if result:
                installed_count += 1
                self.logger.info(f"Successfully installed {package}")
            else:
                self.errors.append(f"Failed to install {package}")

        self.logger.stats.update(
            {
                "yaml_formatters_installed": installed_count,
                "yaml_formatters_failed": len(
                    [f for f in formatters if f.get("enabled", True)]
                )
                - installed_count,
            }
        )

        return success

    def install_all(self) -> bool:
        """
        Install all formatters.

        Returns:
            True if all installations were successful, False otherwise
        """
        success = True

        success = self.install_python_formatters() and success

        success = self.install_js_formatters() and success

        success = self.install_html_formatters() and success

        success = self.install_shell_formatters() and success

        success = self.install_yaml_formatters() and success

        self.logger.stats.update(
            {
                "total_formatters_installed": self.logger.stats.get(
                    "python_formatters_installed", 0
                )
                + self.logger.stats.get("js_formatters_installed", 0)
                + self.logger.stats.get("html_formatters_installed", 0)
                + self.logger.stats.get("shell_formatters_installed", 0)
                + self.logger.stats.get("yaml_formatters_installed", 0),
                "total_formatters_failed": self.logger.stats.get(
                    "python_formatters_failed", 0
                )
                + self.logger.stats.get("js_formatters_failed", 0)
                + self.logger.stats.get("html_formatters_failed", 0)
                + self.logger.stats.get("shell_formatters_failed", 0)
                + self.logger.stats.get("yaml_formatters_failed", 0),
            }
        )

        return success


def main():
    """Parse arguments and run the main function."""
    parser = argparse.ArgumentParser(description="Formatter and linter installer")
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Show verbose output"
    )
    parser.add_argument(
        "--python-only", action="store_true", help="Install only Python formatters"
    )
    parser.add_argument(
        "--js-only",
        action="store_true",
        help="Install only JavaScript/TypeScript formatters",
    )
    parser.add_argument(
        "--html-only", action="store_true", help="Install only HTML formatters"
    )
    parser.add_argument(
        "--shell-only", action="store_true", help="Install only Shell formatters"
    )
    parser.add_argument(
        "--yaml-only", action="store_true", help="Install only YAML formatters"
    )

    args = parser.parse_args()

    if not args.config:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        default_config = os.path.join(script_dir, "config.yml")
        if os.path.exists(default_config):
            args.config = default_config

    installer = FormatterInstaller(args.config, args.verbose)
    success = False

    only_flags = [
        args.python_only,
        args.js_only,
        args.html_only,
        args.shell_only,
        args.yaml_only,
    ]

    if any(only_flags):

        success = True

        if args.python_only:
            success = installer.install_python_formatters() and success

        if args.js_only:
            success = installer.install_js_formatters() and success

        if args.html_only:
            success = installer.install_html_formatters() and success

        if args.shell_only:
            success = installer.install_shell_formatters() and success

        if args.yaml_only:
            success = installer.install_yaml_formatters() and success
    else:

        success = installer.install_all()

    installer.logger.print_summary()

    execution_time = installer.logger.end_script()
    installer.logger.debug(f"Total execution time: {execution_time:.2f} seconds")

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
