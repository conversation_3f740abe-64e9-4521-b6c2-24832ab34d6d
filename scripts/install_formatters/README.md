# Formatter Installation Utility

A comprehensive tool that automates the installation and configuration of code formatting tools for multiple programming languages. This utility ensures consistent development environments across the project.

## Features

- **Multi-Language Support**: Installs formatters for Python, JavaScript, TypeScript, HTML, YAML, and shell scripts
- **Intelligent Installation**: Detects existing installations to avoid duplicates
- **Cross-Platform**: Works on Linux, macOS, and Windows environments
- **Virtual Environment Integration**: Can install Python formatters in virtual environments
- **Configuration Generation**: Creates default configuration files for all formatters
- **Dependency Management**: Handles version conflicts and dependencies
- **IDE Integration**: Sets up formatters to work with common IDEs

## Supported Formatters

| Language | Formatters |
|----------|------------|
| Python | Black, isort, ruff, autopep8 |
| JavaScript/TypeScript | Prettier, ESLint |
| HTML | Prettier, djLint |
| YAML | yamllint, prettier |
| Shell | shfmt, shellcheck |

## Installation

No additional installation required beyond Python, pip, and npm.

## Usage

### Basic Usage

```bash
# Install all formatters
python install_formatters/install_formatters.py

# Install specific formatter groups
python install_formatters/install_formatters.py --python --javascript

# Install with custom configurations
python install_formatters/install_formatters.py --config-dir ./my-configs
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--all` | Install all formatters (default) |
| `--python` | Install Python formatters only |
| `--javascript` | Install JavaScript/TypeScript formatters only |
| `--html` | Install HTML formatters only |
| `--yaml` | Install YAML formatters only |
| `--shell` | Install shell script formatters only |
| `--venv` | Install Python formatters in virtual environment |
| `--config-dir` | Directory containing custom configuration files |
| `--verbose`, `-v` | Show detailed output |
| `--skip-existing` | Skip installation if formatter already exists |
| `--config`, `-c` | Path to custom configuration file |

## Configuration

The formatter installation is configured through a YAML file:

```yaml
# General settings
general:
  skip_existing: true
  create_configs: true
  config_dir: "../config/formatters"

# Python formatters
python:
  install: true
  packages:
    - name: "black"
      version: "23.3.0"
      config_file: "pyproject.toml"
    - name: "isort"
      version: "5.12.0"
      config_file: "pyproject.toml"
    - name: "ruff"
      version: "0.0.262"
      config_file: "ruff.toml"
    - name: "autopep8"
      version: "2.0.2"
      config_file: ".pep8"

# JavaScript/TypeScript formatters
javascript:
  install: true
  packages:
    - name: "prettier"
      version: "2.8.8"
      config_file: ".prettierrc"
    - name: "eslint"
      version: "8.38.0"
      config_file: ".eslintrc.js"
      dependencies:
        - "@typescript-eslint/eslint-plugin"
        - "@typescript-eslint/parser"

# HTML formatters
html:
  install: true
  packages:
    - name: "djlint"
      version: "1.19.16"
      config_file: ".djlintrc"
      pip: true

# YAML formatters
yaml:
  install: true
  packages:
    - name: "yamllint"
      version: "1.32.0"
      config_file: ".yamllint"
      pip: true

# Shell formatters
shell:
  install: true
  packages:
    - name: "shfmt"
      installer: "go"
      version: "latest"
    - name: "shellcheck"
      installer: "system"
```

### Configuration Sections

| Section | Description |
|---------|-------------|
| `general` | Overall settings for the installation process |
| `python`, `javascript`, etc. | Language-specific formatter configurations |
| `packages` | Individual formatter packages with version and configuration |

## Default Configurations

The utility includes sensible default configurations for all formatters:

### Black (Python)

```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
```

### Prettier (JavaScript/TypeScript)

```json
// .prettierrc
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 100,
  "trailingComma": "es5"
}
```

## Examples

### Install Python Formatters in Virtual Environment

```bash
python install_formatters/install_formatters.py --python --venv
```

### Install All Formatters with Custom Configurations

```bash
python install_formatters/install_formatters.py --all --config-dir ./my-configs
```

### Install Specific Formatters

```bash
python install_formatters/install_formatters.py --javascript --html
```

## Integration with Other Scripts

This formatter installation utility integrates with:

1. Virtual environment setup script for Python formatters
2. Code formatting script to ensure the right formatters are available
3. Docker setup for consistent formatting in containerized environments

## Troubleshooting

If you encounter issues during installation:

1. Check the log files in `scripts/logs/` directory
2. Verify you have the required base tools (pip, npm, etc.)
3. Check your network connection for downloading packages
4. Ensure you have appropriate permissions to install packages
5. Run with `--verbose` flag to see detailed installation logs 