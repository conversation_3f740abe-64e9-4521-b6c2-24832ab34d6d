preserved_comment_patterns:
  # File header comments
  - "#!/"
  - "#! /"
  - "-*- coding:"
  - "-*- mode:"
  - "# encoding:"
  - "# vim:"
  - "# editor:"
  
  # Task markers
  - "TODO:"
  - "FIXME:"
  - "NOTE:"
  - "WARNING:"
  - "HACK:"
  - "XXX:"
  - "BUG:"
  - "SECURITY:"
  
  # Documentation comments
  - "@param"
  - "@return"
  - "@throws"
  - "@type"
  - "@deprecated"
  - "@since"
  - "@see"
  - "@link"
  - "@example"
  
  # Linting directives
  - "type: ignore"
  - "noqa"
  - "fmt: "
  - "pylint:"
  - "mypy:"
  - "ruff:"
  - "isort:"
  - "flake8:"
  - "pyright:"
  - "eslint-disable"
  - "tslint:disable"
  - "prettier-ignore"
  
  # Django-specific comments and configs
  - "# Django"
  - "# DRF"
  - "# settings:"
  - "# middleware:"
  - "# apps:"
  - "# urls:"
  - "# models:"
  - "# views:"
  - "# serializers:"
  - "# auth:"
  - "# permissions:"
  - "# signals:"
  - "# admin:"
  - "# celery:"
  - "# migrations:"
  - "# docker:"
  - "# deployment:"
  - "# SECURITY:"
  - "# SECRET_KEY:"
  - "# DEBUG:"
  - "# DATABASES:"
  
  # YAML-specific important comments
  - "# version:"
  - "# environment:"
  - "# volumes:"
  - "# services:"
  - "# networks:"
  - "# depends_on:"
  - "# build:"
  - "# args:"
  - "# env_file:"
  
  # Code that is commented out but might be needed later
  - "Commented code:"
  - "For future implementation:"
  - "Alternative implementation:"
  - "DEBUG:"

# Directories to exclude from processing
exclude_dirs:
  - "venv"
  - "node_modules"
  - ".git"
  - "__pycache__"
  - "migrations"
  - "dist"
  - "build"
  - ".cache"

# File patterns to exclude
exclude_files:
  - "*.min.js"
  - "*.min.css"
  - "*.map"
  - "*.pyc"
  - "*.lock"
  - "package-lock.json"
  - "yarn.lock"
  - "pnpm-lock.yaml"

# File extensions to include
include_extensions:
  - ".py"
  - ".js"
  - ".jsx"
  - ".ts"
  - ".tsx"
  - ".sh"
  - ".yaml"
  - ".yml"
  - ".html"
  - ".htm"
  - ".txt"

# Django template settings
django_template_dirs:
  - "templates"
  - "core/templates"
  - "api/core/templates"

# By default, preserve all comments in Django templates to avoid breaking template logic
preserve_all_django_template_comments: true

# Yaml file settings
yaml_settings:
  # Always preserve comments in these critical Django YAML files
  critical_files:
    - "docker-compose*.yml"
    - "docker-compose*.yaml"
    - ".gitlab-ci.yml"
    - ".github/workflows/*.yml"
    - "helm/*.yaml"
    - "kubernetes/*.yaml"
    - "requirements*.yml"
    - "*settings*.yml"
    - "*settings*.yaml"
    - "*config*.yml"
    - "*config*.yaml"
  
  # Preserve all comments near these YAML keys (line above or same line)
  critical_keys:
    - "DEBUG:"
    - "SECRET_KEY:"
    - "ALLOWED_HOSTS:"
    - "DATABASES:"
    - "MIDDLEWARE:"
    - "INSTALLED_APPS:"
    - "TEMPLATES:"
    - "AUTH_PASSWORD_VALIDATORS:"
    - "services:"
    - "volumes:"
    - "environment:"
    - "build:"
    - "image:"

# Code detection settings
code_detection_threshold: 0.7  
preserve_commented_code: true 