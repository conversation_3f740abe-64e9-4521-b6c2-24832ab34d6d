# Comment Cleaner

A robust Python script that safely removes unnecessary comments from your codebase while preserving important ones such as license headers, documentation markers, and linter directives.

## Features

- **Smart Comment Detection**: Distinguishes between meaningful comments and disposable ones
- **Comment Code Detection**: Preserves commented-out code blocks to avoid removing potential backup code
- **Multi-language Support**: Works with Python, JavaScript, TypeScript, JSX/TSX, HTML, and Django templates
- **Configurable Patterns**: Extensive configuration to control which comments should be kept
- **Recursive Processing**: Process entire directory trees with a single command
- **Detailed Logging**: Comprehensive logs with configurable verbosity levels

## Installation

No installation required beyond standard Python libraries. The script uses:
- PyYAML for configuration
- Standard library modules for file processing

## Usage

### Basic Usage

```bash
# Process current directory
python clean_comments.py

# Process specific directory
python clean_comments.py --directory path/to/dir

# Process directory recursively
python clean_comments.py --directory path/to/dir --recursive

# Dry run (show what would be cleaned without making changes)
python clean_comments.py --dry-run

# Show detailed output
python clean_comments.py --verbose
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--directory`, `-d` | Target directory to process (default: current directory) |
| `--recursive`, `-r` | Process directories recursively |
| `--pattern`, `-p` | File pattern to match (default: all supported file types) |
| `--config`, `-c` | Path to configuration file (default: config.yml in script directory) |
| `--verbose`, `-v` | Show detailed output during processing |
| `--dry-run`, `-n` | Run without making changes to files |
| `--preserve-commented-code` | Preserve commented code blocks (enabled by default) |

## Configuration

The script uses a YAML configuration file (`config.yml`) that allows you to customize:

```yaml
# Example of preserved comment patterns
preserved_comment_patterns:
  # File header comments
  - "#!/"
  - "-*- coding:"
  
  # Task markers
  - "TODO:"
  - "FIXME:"
  - "NOTE:"

  # Documentation comments
  - "@param"
  - "@return"
  
  # Linting directives
  - "type: ignore"
  - "noqa"
  - "eslint-disable"
```

### Key Configuration Options

| Option | Description |
|--------|-------------|
| `preserved_comment_patterns` | List of patterns that should not be removed |
| `file_extensions` | Dictionary mapping of language to file extensions |
| `commented_code_detection` | Settings for detecting and preserving commented code |
| `django_template_settings` | Special settings for handling Django templates |
| `yaml_settings` | Special handling for YAML files |

## Examples

### Clean Python Files While Preserving TODOs

```bash
python clean_comments.py --directory ./api --pattern "*.py" --recursive
```

### Clean JavaScript Files with Dry Run

```bash
python clean_comments.py --directory ./ui/src --pattern "*.js" --dry-run --verbose
```

### Clean All Files Using Custom Configuration

```bash
python clean_comments.py --config /path/to/my/config.yml --recursive
```

## Integration with Other Scripts

The comment cleaner works as part of the larger script infrastructure:

1. Can be run as a pre-commit hook via the commit script
2. Is integrated into the main `run_all_checks.py` script
3. Logs all activities to the central logging system

## Troubleshooting

If you encounter any issues:

1. Use the `--verbose` flag to get detailed logs
2. Check the log files in the `scripts/logs` directory
3. Ensure your config file is properly formatted YAML
4. Try `--dry-run` first to see what changes would be made 