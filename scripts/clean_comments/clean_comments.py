"""
Comment Cleaning Script

This script removes unnecessary or outdated comments from code files.
It can detect and clean various types of comments across different programming languages.
"""

import os
import re
import sys
import time
import argparse
import subprocess
import yaml
from pathlib import Path
from typing import List, Dict, Any, Set, Tuple
import tempfile


sys.path.append(str(Path(__file__).parent.parent))
try:
    from logger import get_logger
except ImportError:

    class FallbackLogger:
        def __init__(self, script_name, verbose=False):
            self.script_name = script_name
            self.verbose = verbose
            self.stats = {}

        def start_script(self):
            pass

        def end_script(self):
            return 0

        def info(self, message):
            if self.verbose:
                print(message)

        def debug(self, message):
            pass

        def warning(self, message):
            if self.verbose:
                print(f"WARNING: {message}")

        def error(self, message):
            print(f"ERROR: {message}")

        def critical(self, message):
            print(f"CRITICAL: {message}")

        def log_file_action(self, filepath, action, **stats):
            if self.verbose:
                print(f"{action.capitalize()}: {filepath}")
            for key, value in stats.items():
                if key not in self.stats:
                    self.stats[key] = 0
                self.stats[key] += value

        def print_summary(self):
            summary_lines = ["", "Summary:"]
            for key, value in self.stats.items():
                pretty_key = " ".join(word.capitalize() for word in key.split("_"))
                summary_lines.append(f"{pretty_key}: {value}")
            print("\n".join(summary_lines))
            return "\n".join(summary_lines)

    def get_logger(script_name, verbose=False):
        return FallbackLogger(script_name, verbose)


class CommentCleaner:
    """Cleans unnecessary comments from code files."""

    DEFAULT_COMMENT_PATTERNS = {
        ".py": {
            "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|#!/|#!|encoding:|coding:|vim:|editor:)).*$",
            "multi_line": r'"""(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?.*?"""',
            "preserve_docstrings": True,
        },
        ".js": {
            "single_line": r"//(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|eslint-disable|prettier-ignore)).*$",
            "multi_line": r"/\*(?!\*|(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
            "preserve_jsdoc": True,
        },
        ".ts": {
            "single_line": r"//(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|eslint-disable|prettier-ignore)).*$",
            "multi_line": r"/\*(?!\*|(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
            "preserve_jsdoc": True,
        },
        ".tsx": {
            "single_line": r"//(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|eslint-disable|prettier-ignore)).*$",
            "multi_line": r"/\*(?!\*|(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
            "preserve_jsdoc": True,
        },
        ".html": {
            "multi_line": r"<!--(?!\s*(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?-->",
        },
        ".css": {
            "multi_line": r"/\*(?!(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
        },
        ".scss": {
            "single_line": r"//(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$",
            "multi_line": r"/\*(?!(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
        },
        ".sh": {
            "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|#!/|#!|env|command|script|description|argument|param|example)).*$",
            "preserve_shebang": True,
        },
        ".yaml": {
            "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$",
        },
        ".yml": {
            "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$",
        },
        ".txt": {
            "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$",
        },
    }

    def __init__(self, verbose: bool = False, force_safelist: bool = False):
        """
        Initialize the CommentCleaner.

        Args:
            verbose: Whether to show verbose output
            force_safelist: If True, safelisted files will still be processed
        """
        self.logger = get_logger("clean_comments", verbose)
        self.logger.start_script()
        self.cleaned_files = 0
        self.cleaned_comments = 0
        self.modified_files = []
        self.config_file = os.path.join(Path(__file__).parent, "config.yml")
        self.force_safelist = force_safelist

        self.safelist_patterns = [
            r".*Makefile$",
            r".*\.mk$",
            r".*\.github/workflows/.*\.yml$",
            r".*\.gitlab-ci\.yml$",
            r".*docker-compose.*\.yml$",
            r".*env\.sh$",
            r".*\.env$",
            r".*entrypoint.*\.sh$",
        ]

        self.COMMENT_PATTERNS = self._load_comment_patterns()

    def _load_comment_patterns(self) -> Dict[str, Dict[str, str]]:
        """Load comment patterns from config file and merge with defaults."""
        patterns = self.DEFAULT_COMMENT_PATTERNS.copy()

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    config = yaml.safe_load(f)

                if (
                    config
                    and "include_extensions" in config
                    and isinstance(config["include_extensions"], list)
                ):
                    for ext in config["include_extensions"]:
                        if not ext.startswith("."):
                            ext = f".{ext}"

                        if ext not in patterns:
                            if ext in [".yaml", ".yml", ".txt", ".md", ".rst"]:
                                patterns[ext] = {
                                    "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$"
                                }
                            elif ext == ".sh":
                                patterns[ext] = {
                                    "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|#!/|#!)).*$"
                                }
                            elif ext in [".jsx"]:
                                patterns[ext] = patterns[".js"].copy()
                            elif ext in [".htm"]:
                                patterns[ext] = patterns[".html"].copy()
                            else:
                                self.logger.warning(
                                    f"No default pattern for extension {ext}, using generic single-line comment pattern"
                                )
                                patterns[ext] = {
                                    "single_line": r"#(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP)).*$"
                                }

                self.logger.info(f"Loaded config from {self.config_file}")
            except Exception as e:
                self.logger.error(f"Error loading config file: {e}")

        return patterns

    def get_staged_files(self) -> List[str]:
        """
        Get the list of staged files for the commit.

        Returns:
            List of staged file paths
        """
        try:
            result = subprocess.run(
                ["git", "diff", "--cached", "--name-only"],
                capture_output=True,
                text=True,
                check=True,
            )
            return result.stdout.strip().split("\n")
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to get staged files: {e}")
            return []

    def should_process_file(self, filepath: str) -> bool:
        """
        Check if the file should be processed.

        Args:
            filepath: Path to the file

        Returns:
            True if the file should be processed, False otherwise
        """
        if not os.path.exists(filepath):
            return False

        if not self.force_safelist:
            for pattern in self.safelist_patterns:
                if re.match(pattern, filepath):
                    self.logger.info(f"Skipping safelisted file: {filepath}")
                    return False

        ext = os.path.splitext(filepath)[1]
        return ext in self.COMMENT_PATTERNS

    def clean_file(self, filepath: str) -> int:
        """
        Clean comments from a file.

        Args:
            filepath: Path to the file

        Returns:
            Number of comments cleaned
        """
        self.logger.info(f"Cleaning comments from {filepath}")

        if not self.should_process_file(filepath):
            self.logger.debug(f"Skipping unsupported file: {filepath}")
            return 0

        ext = os.path.splitext(filepath)[1]
        patterns = self.COMMENT_PATTERNS.get(ext, {})
        self.logger.debug(f"Using patterns for {ext}: {patterns}")

        
        if not patterns:
            self.logger.debug(f"No patterns found for extension {ext}")
            return 0

        is_shell_script = ext == ".sh"
        if is_shell_script:
            backup_path = f"{filepath}.bak"
            try:
                import shutil
                shutil.copy2(filepath, backup_path)
                self.logger.debug(f"Created backup of shell script at {backup_path}")
            except Exception as e:
                self.logger.warning(f"Failed to create backup of shell script: {e}")

        file_size = os.path.getsize(filepath)
        
        
        if file_size > 256 * 1024:  
            self.logger.warning(
                f"Skipping large file {filepath} ({file_size/1024:.1f} KB)"
            )
            return 0

        try:
            with open(filepath, "r", encoding="utf-8") as file:
                content = file.read()

            
            if not content.strip():
                return 0

            
            lines = content.splitlines()
            if any(len(line) > 2000 for line in lines):
                self.logger.warning(f"Skipping file with very long lines: {filepath}")
                return 0

            original_content = content
            comments_cleaned = 0

            start_time = time.time()
            max_processing_time = 3  

            
            has_potential_comments = False
            if ext == ".py" and "#" in content:
                has_potential_comments = True
            elif ext in [".js", ".jsx", ".ts", ".tsx"] and (
                "//" in content or "/*" in content
            ):
                has_potential_comments = True
            elif ext in [".html", ".htm"] and "<!--" in content:
                has_potential_comments = True
            elif ext in [".sh", ".yaml", ".yml", ".txt"] and "#" in content:
                has_potential_comments = True

            if not has_potential_comments:
                self.logger.debug(f"No comment patterns found in {filepath}")
                return 0

            if ext == ".py":
                self.logger.debug("Processing Python file with tokenizer")
                import tokenize
                import io
                from token import STRING, COMMENT

                string_positions = []
                comment_positions = []

                try:
                    tokenize_start = time.time()
                    tokenize_timeout = 2  

                    tokens = tokenize.tokenize(
                        io.BytesIO(content.encode("utf-8")).readline
                    )
                    for token in tokens:
                        
                        if time.time() - tokenize_start > tokenize_timeout:
                            self.logger.warning(
                                f"Tokenization of {filepath} is taking too long, falling back to regex"
                            )
                            raise TimeoutError("Tokenization timeout")

                        token_type = token.type
                        start_line, start_col = token.start
                        end_line, end_col = token.end

                        if token_type == STRING:
                            string_positions.append(
                                (start_line, start_col, end_line, end_col)
                            )

                        if token_type == COMMENT:
                            comment_text = token.string
                            if "single_line" in patterns:
                                pattern = patterns["single_line"]
                                if re.match(pattern, comment_text):
                                    comment_positions.append(
                                        (start_line, start_col, end_line, end_col)
                                    )
                                    self.logger.debug(
                                        f"Found comment to clean: {comment_text}"
                                    )

                    if comment_positions:
                        lines = content.splitlines(True)
                        for line_num, start_col, _, end_col in sorted(
                            comment_positions, reverse=True
                        ):
                            if 0 <= line_num - 1 < len(lines):
                                line = lines[line_num - 1]

                                if 0 <= start_col < len(line) and 0 <= end_col <= len(
                                    line
                                ):
                                    lines[line_num - 1] = (
                                        line[:start_col] + line[end_col:]
                                    )
                                    comments_cleaned += 1

                        content = "".join(lines)
                except (
                    tokenize.TokenError,
                    IndentationError,
                    SyntaxError,
                    TimeoutError,
                ) as e:
                    self.logger.warning(
                        f"Tokenization failed for {filepath}, falling back to regex with safeguards: {e}"
                    )
                    content, py_comments = self._fallback_clean_python_comments(
                        content, patterns
                    )
                    comments_cleaned += py_comments

            elif is_shell_script:
                self.logger.debug(
                    f"Processing shell script with extra safety: {filepath}"
                )

                lines = content.splitlines(True)
                result_lines = []
                for line_num, line in enumerate(lines):
                    
                    if (
                        line_num % 50 == 0  
                        and time.time() - start_time > max_processing_time
                    ):
                        self.logger.warning(
                            f"Processing {filepath} is taking too long, stopping early"
                        )
                        break

                    if line_num == 0 and line.startswith("#!"):
                        result_lines.append(line)
                        continue

                    line_without_whitespace = line.strip()
                    if not line_without_whitespace.startswith("#"):
                        result_lines.append(line)
                        continue

                    comment_text = line_without_whitespace
                    pattern = patterns.get("single_line", "")
                    if not re.match(pattern, comment_text):
                        result_lines.append(line)
                        continue

                    comments_cleaned += 1

                    if line.endswith("\n"):
                        result_lines.append("\n")

                content = "".join(result_lines)

            elif ext in [".js", ".jsx", ".ts", ".tsx"]:
                self.logger.debug(f"Processing JavaScript/TypeScript file: {filepath}")
                content, js_comments = self._clean_js_comments(content, patterns)
                comments_cleaned += js_comments

                if time.time() - start_time > max_processing_time:
                    self.logger.warning(
                        f"Processing {filepath} is taking too long, skipping further cleaning"
                    )
                    return comments_cleaned

            elif ext in [".html", ".htm"]:
                self.logger.debug("Processing HTML file")
                content, html_comments = self._clean_html_comments(content, patterns)
                comments_cleaned += html_comments

                if time.time() - start_time > max_processing_time:
                    self.logger.warning(
                        f"Processing {filepath} is taking too long, skipping further cleaning"
                    )
                    return comments_cleaned

            else:
                self.logger.debug(f"Processing generic file with extension {ext}")
                content, generic_comments = self._generic_clean_comments(
                    content, patterns
                )
                comments_cleaned += generic_comments

                if time.time() - start_time > max_processing_time:
                    self.logger.warning(
                        f"Processing {filepath} is taking too long, skipping further cleaning"
                    )
                    return comments_cleaned

            if content != original_content and comments_cleaned > 0:
                self.logger.debug(
                    f"Writing changes to {filepath} (cleaned {comments_cleaned} comments)"
                )

                if is_shell_script:
                    
                    if not content.startswith("#!/"):
                        self.logger.warning(
                            f"Shell script {filepath} is missing shebang after cleaning, restoring backup"
                        )

                        backup_path = f"{filepath}.bak"
                        if os.path.exists(backup_path):
                            import shutil
                            shutil.copy2(backup_path, filepath)
                            os.remove(backup_path)
                            return 0

                    try:
                        with tempfile.NamedTemporaryFile(
                            mode="w+", delete=False
                        ) as tmp:
                            tmp.write(content)
                            tmp_path = tmp.name

                        result = subprocess.run(
                            ["bash", "-n", tmp_path], 
                            capture_output=True, 
                            text=True,
                            timeout=5  
                        )

                        if result.returncode != 0:
                            self.logger.warning(
                                f"Shell script {filepath} has syntax errors after cleaning, restoring backup"
                            )
                            self.logger.debug(f"Syntax check output: {result.stderr}")

                            backup_path = f"{filepath}.bak"
                            if os.path.exists(backup_path):
                                import shutil
                                shutil.copy2(backup_path, filepath)
                                os.remove(backup_path)
                                return 0

                        os.unlink(tmp_path)
                    except Exception as e:
                        self.logger.warning(
                            f"Failed to validate shell script syntax: {e}"
                        )

                with open(filepath, "w", encoding="utf-8") as file:
                    file.write(content)
                self.logger.log_file_action(
                    filepath, "cleaned", comments=comments_cleaned
                )
                self.cleaned_files += 1
                self.cleaned_comments += comments_cleaned
                self.modified_files.append(filepath)

                if is_shell_script:
                    backup_path = f"{filepath}.bak"
                    if os.path.exists(backup_path):
                        try:
                            os.remove(backup_path)
                        except Exception as e:
                            self.logger.debug(
                                f"Failed to remove backup {backup_path}: {e}"
                            )

            return comments_cleaned

        except Exception as e:
            self.logger.error(f"Error cleaning comments from {filepath}: {str(e)}")
            import traceback
            self.logger.debug(f"Traceback: {traceback.format_exc()}")

            if is_shell_script:
                backup_path = f"{filepath}.bak"
                if os.path.exists(backup_path):
                    try:
                        import shutil
                        shutil.copy2(backup_path, filepath)
                        os.remove(backup_path)
                        self.logger.info(
                            f"Restored shell script from backup after error"
                        )
                    except Exception as e2:
                        self.logger.error(
                            f"Failed to restore shell script from backup: {e2}"
                        )

            return 0

    def _fallback_clean_python_comments(self, content, patterns):
        """Safe fallback for cleaning Python comments when tokenizing fails."""
        lines = content.splitlines(True)
        result_lines = []
        in_string = False
        string_delimiter = None
        comments_cleaned = 0

        start_time = time.time()
        max_processing_time = 3  

        for line_num, line in enumerate(lines):
            
            if line_num % 50 == 0 and time.time() - start_time > max_processing_time:
                self.logger.warning(
                    "Fallback Python comment cleaner taking too long, stopping early"
                )
                
                result_lines.extend(lines[line_num:])
                break

            if not in_string:
                for i, char in enumerate(line):
                    if char in "\"'" and (i == 0 or line[i - 1] != "\\"):
                        if i + 2 < len(line) and line[i : i + 3] in ['"""', "'''"]:
                            in_string = True
                            string_delimiter = line[i : i + 3]
                            break

                        elif not in_string:
                            rest = line[i + 1 :]
                            if (
                                char in rest
                                and rest.index(char) + i + 1 < len(line)
                                and line[rest.index(char) + i] != "\\"
                            ):
                                pass
                            else:
                                in_string = True
                                string_delimiter = char
                                break

                if not in_string:
                    comment_pos = -1
                    for i, char in enumerate(line):
                        if char == "#" and (i == 0 or line[i - 1] != "\\"):
                            in_str = False
                            str_char = None
                            for j in range(i):
                                if line[j] in "\"'" and (j == 0 or line[j - 1] != "\\"):
                                    if not in_str:
                                        in_str = True
                                        str_char = line[j]
                                    elif line[j] == str_char and line[j - 1] != "\\":
                                        in_str = False
                            if not in_str:
                                comment_pos = i
                                break

                if comment_pos >= 0:
                    comment_text = line[comment_pos:].strip()
                    pattern = patterns.get("single_line", "")
                    if re.match(pattern, comment_text):
                        comments_cleaned += 1
                        line = (
                            line[:comment_pos] + "\n"
                            if line.endswith("\n")
                            else line[:comment_pos]
                        )

            else:
                if string_delimiter in line:
                    delimiter_pos = line.find(string_delimiter)
                    if delimiter_pos > 0 and line[delimiter_pos - 1] != "\\":
                        in_string = False

            result_lines.append(line)

        return "".join(result_lines), comments_cleaned

    def _clean_js_comments(self, content, patterns):
        """Clean JavaScript/TypeScript comments with awareness of regex literals and template strings."""
        lines = content.splitlines(True)
        result_lines = []
        in_block_comment = False
        in_string = False
        in_template = False
        in_regex = False
        string_char = None
        comments_cleaned = 0

        for line in lines:
            i = 0
            current_line = ""

            while i < len(line):

                if in_block_comment:
                    if i + 1 < len(line) and line[i : i + 2] == "*/":
                        in_block_comment = False
                        i += 2
                        continue
                    i += 1
                    continue

                if in_string:
                    if line[i] == string_char and (i == 0 or line[i - 1] != "\\"):
                        in_string = False
                    current_line += line[i]
                    i += 1
                    continue

                if in_template:
                    if line[i] == "`" and (i == 0 or line[i - 1] != "\\"):
                        in_template = False
                    current_line += line[i]
                    i += 1
                    continue

                if in_regex:
                    if line[i] == "/" and (i == 0 or line[i - 1] != "\\"):
                        in_regex = False
                    current_line += line[i]
                    i += 1
                    continue

                if i + 1 < len(line):

                    if line[i : i + 2] == "/*":

                        if (
                            i + 2 < len(line)
                            and line[i + 2] == "*"
                            and patterns.get("preserve_jsdoc", False)
                        ):

                            current_line += line[i]
                            i += 1
                            continue
                        else:

                            if "multi_line" in patterns:

                                comment_start = i
                                rest_of_content = line[i:]

                                comment_end = rest_of_content.find("*/")
                                if comment_end != -1:
                                    comment_text = rest_of_content[: comment_end + 2]
                                    if re.match(patterns["multi_line"], comment_text):
                                        comments_cleaned += 1
                                        i += comment_end + 2
                                        continue

                            in_block_comment = True
                            comments_cleaned += 1
                            i += 2
                            continue

                    if line[i : i + 2] == "//" and "single_line" in patterns:
                        pattern = patterns["single_line"]
                        comment_text = line[i:]
                        if re.match(pattern, comment_text):
                            comments_cleaned += 1
                            break
                        else:
                            current_line += comment_text
                            break

                if line[i] in "\"'":
                    in_string = True
                    string_char = line[i]
                    current_line += line[i]
                    i += 1
                    continue

                if line[i] == "`":
                    in_template = True
                    current_line += line[i]
                    i += 1
                    continue

                if line[i] == "/" and i + 1 < len(line) and line[i + 1] not in "/*":
                    valid_regex_prefix = True
                    for j in range(i - 1, -1, -1):
                        if line[j].isspace():
                            continue
                        valid_regex_prefix = line[j] in ",({[=:!&|?;"
                        break

                    if valid_regex_prefix:
                        in_regex = True
                        current_line += line[i]
                        i += 1
                continue

                current_line += line[i]
                i += 1

            if current_line or not line.strip():
                result_lines.append(
                    current_line if current_line.endswith("\n") else current_line + "\n"
                )

        return "".join(result_lines), comments_cleaned

    def _clean_html_comments(self, content, patterns):
        """Clean HTML comments while preserving JavaScript and CSS in script and style tags."""
        import re

        script_blocks = {}
        style_blocks = {}
        comments_cleaned = 0

        script_pattern = r"(<script\b[^>]*>)(.*?)(</script>)"
        for i, match in enumerate(re.finditer(script_pattern, content, re.DOTALL)):
            placeholder = f"__SCRIPT_PLACEHOLDER_{i}__"
            script_blocks[placeholder] = match.group(2)
            content = content.replace(match.group(2), placeholder)

        style_pattern = r"(<style\b[^>]*>)(.*?)(</style>)"
        for i, match in enumerate(re.finditer(style_pattern, content, re.DOTALL)):
            placeholder = f"__STYLE_PLACEHOLDER_{i}__"
            style_blocks[placeholder] = match.group(2)
            content = content.replace(match.group(2), placeholder)

        if "multi_line" in patterns:
            pattern = patterns["multi_line"]

            in_tag = False
            in_attr_value = False
            attr_quote = None
            result = []
            i = 0

            while i < len(content):
                if (
                    not in_tag
                    and not in_attr_value
                    and i + 3 < len(content)
                    and content[i : i + 4] == "<!--"
                ):
                    comment_start = i
                    comment_end = content.find("-->", i + 4)
                    if comment_end != -1:
                        comment = content[comment_start : comment_end + 3]
                        if re.match(pattern, comment):
                            comments_cleaned += 1
                            i = comment_end + 3
                            continue

                if content[i] == "<" and not in_attr_value:
                    in_tag = True
                elif content[i] == ">" and not in_attr_value:
                    in_tag = False
                elif (
                    in_tag
                    and content[i] in "\"'"
                    and (i == 0 or content[i - 1] != "\\")
                ):
                    if not in_attr_value:
                        in_attr_value = True
                        attr_quote = content[i]
                    elif content[i] == attr_quote:
                        in_attr_value = False

                result.append(content[i])
                i += 1

            content = "".join(result)

        for placeholder, script in script_blocks.items():
            cleaned_script, script_comments = self._clean_js_comments(
                script,
                {
                    "single_line": r"//(?!\s*(TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP|eslint-disable|prettier-ignore)).*$",
                    "multi_line": r"/\*(?!(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/",
                },
            )
            comments_cleaned += script_comments
            content = content.replace(placeholder, cleaned_script)

        for placeholder, style in style_blocks.items():
            cleaned_style, style_comments = self._generic_clean_comments(
                style,
                {
                    "multi_line": r"/\*(?!(?:TODO|FIXME|XXX|NOTE|HACK|BUG|DEPRECATED|REFACTOR|OPTIMIZE|REVIEW|WIP):?).*?\*/"
                },
            )
            comments_cleaned += style_comments
            content = content.replace(placeholder, cleaned_style)

        return content, comments_cleaned

    def _generic_clean_comments(self, content, patterns):
        """Generic comment cleaner with basic safeguards for other languages."""
        comments_cleaned = 0
        file_ext = None

        for ext in self.COMMENT_PATTERNS.keys():
            if patterns == self.COMMENT_PATTERNS.get(ext):
                file_ext = ext
                break

        is_shell_script = file_ext == ".sh"
        preserve_shebangs = is_shell_script

        if "single_line" in patterns:
            lines = content.splitlines(True)
            result_lines = []

            for line_num, line in enumerate(lines):
                i = 0
                in_string = False
                string_char = None
                comment_pos = -1

                if line_num == 0 and preserve_shebangs and line.startswith("#!"):
                    result_lines.append(line)
                    continue

                while i < len(line):
                    if not in_string:
                        if i + 1 < len(line) and line[i : i + 2] in ["//", "--"]:
                            comment_pos = i
                            break
                        elif line[i] == "#":
                            comment_pos = i
                            break

                        if line[i] in "\"'`":
                            in_string = True
                            string_char = line[i]

                    elif line[i] == string_char and (i == 0 or line[i - 1] != "\\"):
                        in_string = False

                    i += 1

                if comment_pos >= 0 and not in_string:
                    comment_text = line[comment_pos:]
                    pattern = patterns["single_line"]
                    if re.match(pattern, comment_text):

                        if is_shell_script and line.strip() == comment_text.strip():

                            comments_cleaned += 1
                            line = "\n" if line.endswith("\n") else ""
                        elif (
                            is_shell_script
                            and comment_pos > 0
                            and not line[:comment_pos].strip()
                        ):

                            comments_cleaned += 1
                            line = "\n" if line.endswith("\n") else ""
                        else:

                            comments_cleaned += 1
                            line = (
                                line[:comment_pos] + "\n"
                                if line.endswith("\n")
                                else line[:comment_pos]
                            )

                result_lines.append(line)

            content = "".join(result_lines)

        if "multi_line" in patterns and not patterns.get("preserve_docstrings", False):
            pattern = patterns["multi_line"]

            in_comment = False
            comment_start = 0
            in_string = False
            string_char = None
            result = []
            i = 0

            while i < len(content):
                if not in_comment and not in_string:
                    if i + 1 < len(content) and (
                        (content[i : i + 2] == "/*")
                        or (
                            content[i : i + 3] == '"""'
                            and patterns.get("preserve_docstrings", False) == False
                        )
                    ):
                        in_comment = True
                        comment_start = i
                        i += 2
                        continue

                    if content[i] in "\"'`":
                        if i + 2 < len(content) and content[i : i + 3] in [
                            '"""',
                            "'''",
                        ]:
                            in_string = True
                            string_char = content[i : i + 3]
                            result.append(content[i : i + 3])
                            i += 3
                            continue
                        else:
                            in_string = True
                            string_char = content[i]
                            result.append(content[i])
                            i += 1
                    continue

                elif in_comment:
                    if (i + 1 < len(content) and content[i : i + 2] == "*/") or (
                        i + 2 < len(content) and content[i : i + 3] == '"""'
                    ):
                        in_comment = False
                        comment_text = content[comment_start : i + 2]
                        if re.match(pattern, comment_text):
                            comments_cleaned += 1
                            i += 2
                        else:
                            result.append(comment_text)
                            i += 2
                            continue

                elif in_string:
                    if string_char in ['"""', "'''"]:
                        if i + 2 < len(content) and content[i : i + 3] == string_char:
                            in_string = False
                            result.append(content[i : i + 3])
                            i += 3
                            continue
                    elif content[i] == string_char and (
                        i == 0 or content[i - 1] != "\\"
                    ):
                        in_string = False

                if not in_comment:
                    result.append(content[i])
                i += 1

            content = "".join(result)

        return content, comments_cleaned

    def clean_comments(self, files: List[str] = None):
        """
        Clean comments from the specified files or all staged files.

        Args:
            files: List of files to clean, or None to clean all staged files

        Returns:
            Number of comments cleaned
        """
        if files is None:
            files = self.get_staged_files()

        if not files:
            self.logger.info("No files to clean")
            return 0

        supported_files = []
        for filepath in files:
            if os.path.exists(filepath) and self.should_process_file(filepath):

                try:
                    ext = os.path.splitext(filepath)[1]
                    with open(filepath, "rb") as f:

                        sample = f.read(8192).decode("utf-8", errors="ignore")

                        has_comments = False
                        if ext == ".py" and "#" in sample:
                            has_comments = True
                        elif ext in [
                            ".js",
                            ".jsx",
                            ".ts",
                            ".tsx",
                            ".css",
                            ".scss",
                        ] and ("//" in sample or "/*" in sample):
                            has_comments = True
                        elif ext in [".html", ".htm"] and "<!--" in sample:
                            has_comments = True
                        elif ext in [".sh", ".yaml", ".yml", ".txt"] and "#" in sample:
                            has_comments = True

                        if has_comments:
                            supported_files.append(filepath)
                except Exception as e:
                    self.logger.debug(f"Error pre-checking {filepath}: {e}")

                    supported_files.append(filepath)

        self.logger.info(
            f"Found {len(supported_files)} files that might have comments to clean (out of {len(files)} total files)"
        )

        for filepath in supported_files:
            self.clean_file(filepath)

        if self.modified_files:
            try:
                self.logger.info(
                    f"Re-staging {len(self.modified_files)} modified files"
                )
                subprocess.run(
                    ["git", "add"] + self.modified_files,
                    check=True,
                    capture_output=True,
                )
            except subprocess.SubprocessError as e:
                self.logger.warning(f"Failed to re-stage files: {e}")

        self.logger.info(
            f"Cleaned {self.cleaned_comments} comments from {self.cleaned_files} files"
        )

        return self.cleaned_comments

    def print_summary(self):
        """Print summary of cleaning operations."""
        self.logger.stats.update(
            {
                "files_cleaned": self.cleaned_files,
                "comments_removed": self.cleaned_comments,
            }
        )

        self.logger.print_summary()
        execution_time = self.logger.end_script()
        self.logger.debug(f"Total execution time: {execution_time:.2f} seconds")
        return self.cleaned_comments


def main():
    """Parse arguments and run the main function."""
    parser = argparse.ArgumentParser(
        description="Clean unnecessary comments from code files"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Show verbose output"
    )
    parser.add_argument("--files", nargs="*", help="Specific files to clean")
    parser.add_argument(
        "--directory", "-d", help="Directory to recursively clean comments from"
    )
    parser.add_argument(
        "--force",
        "-f",
        action="store_true",
        help="Force removal of all comments (except docstrings)",
    )
    parser.add_argument(
        "--force-safelist",
        "-S",
        action="store_true",
        help="Process files that are normally in the safelist",
    )

    args = parser.parse_args()

    cleaner = CommentCleaner(args.verbose, args.force_safelist)

    if args.force:
        cleaner.logger.info(
            "Force mode enabled - will remove all comments except docstrings"
        )

        for ext in cleaner.COMMENT_PATTERNS:
            cleaner.COMMENT_PATTERNS[ext]["single_line"] = (
                "//.*$"
                if ext.startswith(".js") or ext == ".tsx" or ext == ".ts"
                else "#.*$"
            )
            if "multi_line" in cleaner.COMMENT_PATTERNS[ext]:
                cleaner.COMMENT_PATTERNS[ext]["multi_line"] = (
                    r"/\*.*?\*/" if ext != ".py" else r'""".*?"""'
                )

    result = 0
    if args.directory:
        import glob

        supported_extensions = list(cleaner.COMMENT_PATTERNS.keys())
        cleaner.logger.info(f"Supported extensions: {supported_extensions}")
        files_to_clean = []

        for ext in supported_extensions:
            pattern = f"{args.directory}/**/*{ext}"
            found_files = glob.glob(pattern, recursive=True)
            cleaner.logger.info(f"Found {len(found_files)} files with extension {ext}")
            files_to_clean.extend(found_files)

        if files_to_clean:
            result = cleaner.clean_comments(files_to_clean)
        else:
            cleaner.logger.info(
                f"No supported files found in directory: {args.directory}"
            )
    elif args.files:

        valid_files = []
        for file_path in args.files:
            if os.path.isfile(file_path):
                valid_files.append(file_path)
            else:
                cleaner.logger.warning(f"File not found: {file_path}")

        if valid_files:
            result = cleaner.clean_comments(valid_files)
        else:
            cleaner.logger.info("No valid files provided to clean")
    else:

        result = cleaner.clean_comments()

    cleaner.print_summary()

    return result


if __name__ == "__main__":
    sys.exit(main())
