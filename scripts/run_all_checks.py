"""
Main script to run all available code quality checks and formatters.

This script orchestrates various code validation and formatting utilities,
allowing them to be run individually or as a comprehensive suite.
"""

import argparse
import os
import subprocess
import sys
import time
from datetime import datetime


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from scripts.logger import Logger


log = Logger("run_all_checks")


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
STRUCTURE_VALIDATION = os.path.join(
    SCRIPT_DIR, "structure_validation", "validate_structure.py"
)
PROD_CONFIG_CHECK = os.path.join(
    SCRIPT_DIR, "prod_config_check", "check_prod_config.py"
)
CLEAN_COMMENTS = os.path.join(SCRIPT_DIR, "clean_comments", "clean_comments.py")
FORMAT_CODE = os.path.join(SCRIPT_DIR, "format_code", "code_formatter.sh")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run code quality checks and formatters"
    )

    group = parser.add_mutually_exclusive_group()
    group.add_argument(
        "--all", action="store_true", help="Run all checks and formatters"
    )
    group.add_argument(
        "--validate-only", action="store_true", help="Run only validation checks"
    )
    group.add_argument(
        "--format-only", action="store_true", help="Run only code formatters"
    )

    parser.add_argument(
        "--structure", action="store_true", help="Run structure validation"
    )
    parser.add_argument(
        "--prod-config", action="store_true", help="Check production configuration"
    )
    parser.add_argument(
        "--clean-comments", action="store_true", help="Clean unnecessary comments"
    )
    parser.add_argument("--format", action="store_true", help="Format code")

    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--dry-run",
        "-n",
        action="store_true",
        help="Show what would be done without making changes",
    )
    parser.add_argument(
        "--summary", "-s", action="store_true", help="Show summary of results"
    )

    return parser.parse_args()


def run_command(command, description, verbose=False):
    """Run a shell command and log the result."""
    log.info(f"Running {description}...")

    start_time = time.time()

    try:

        if verbose:
            if isinstance(command, list):
                command.append("--verbose")
            else:
                command += " --verbose"

        result = subprocess.run(
            command,
            shell=isinstance(command, str),
            check=True,
            capture_output=True,
            text=True,
        )

        elapsed_time = time.time() - start_time
        log.info(f"✅ {description} completed successfully in {elapsed_time:.2f}s")

        if verbose:
            log.info(result.stdout)

        return True, result.stdout

    except subprocess.CalledProcessError as e:
        elapsed_time = time.time() - start_time
        log.error(f"❌ {description} failed after {elapsed_time:.2f}s")
        log.error(f"Exit code: {e.returncode}")

        if e.stdout:
            log.debug(e.stdout)

        if e.stderr:
            log.error(e.stderr)

        return False, e.stderr

    except Exception as e:
        log.error(f"❌ Error running {description}: {str(e)}")
        return False, str(e)


def main():
    """Main function to run all checks based on provided arguments."""
    args = parse_arguments()

    log.info(
        f"Starting code quality checks at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

    run_all = args.all or (
        not any(
            [
                args.validate_only,
                args.format_only,
                args.structure,
                args.prod_config,
                args.clean_comments,
                args.format,
            ]
        )
    )

    run_validate = run_all or args.validate_only or args.structure
    run_prod_config = run_all or args.validate_only or args.prod_config
    run_clean_comments = run_all or args.format_only or args.clean_comments
    run_format = run_all or args.format_only or args.format

    results = {}
    success_count = 0
    total_count = 0

    if run_validate:
        total_count += 1
        command = [sys.executable, STRUCTURE_VALIDATION]
        if args.summary:
            command.append("--summary")
        if args.dry_run:
            command.append("--dry-run")

        success, output = run_command(command, "Structure validation", args.verbose)
        results["Structure validation"] = "Passed" if success else "Failed"
        if success:
            success_count += 1

    if run_prod_config:
        total_count += 1
        command = [sys.executable, PROD_CONFIG_CHECK]
        if args.summary:
            command.append("--summary")
        if args.dry_run:
            command.append("--dry-run")

        success, output = run_command(command, "Production config check", args.verbose)
        results["Production config"] = "Passed" if success else "Failed"
        if success:
            success_count += 1

    if run_clean_comments:
        total_count += 1
        command = [sys.executable, CLEAN_COMMENTS]
        if args.dry_run:
            command.append("--dry-run")

        success, output = run_command(command, "Comment cleaning", args.verbose)
        results["Comment cleaning"] = "Passed" if success else "Failed"
        if success:
            success_count += 1

    if run_format:
        total_count += 1
        command = FORMAT_CODE
        if args.dry_run:
            command += " --dry-run"

        success, output = run_command(command, "Code formatting", args.verbose)
        results["Code formatting"] = "Passed" if success else "Failed"
        if success:
            success_count += 1

    log.info("\n----- Summary -----")
    for check, result in results.items():
        status_symbol = "✅" if result == "Passed" else "❌"
        log.info(f"{status_symbol} {check}: {result}")

    log.info(f"\nPassed {success_count} out of {total_count} checks")

    if total_count > 0 and success_count == total_count:
        log.info("All checks completed successfully!")
        return 0
    else:
        log.error("One or more checks failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
