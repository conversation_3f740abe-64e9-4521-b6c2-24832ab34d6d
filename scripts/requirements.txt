# Core dependencies for all scripts
pyyaml>=6.0          # For configuration files
requests>=2.25.0     # For API communication
polib>=1.1.0         # For translation files

# Code quality tools
black>=23.3.0        # Code formatter
isort>=5.12.0        # Import sorting
flake8>=6.0.0        # Linter
mypy>=1.3.0          # Type checking

# Testing tools
pytest>=7.3.1        # Test runner
pytest-cov>=4.1.0    # Coverage reports

# Git tools
gitpython>=3.1.30    # For Git operations in scripts

# System utilities
psutil>=5.9.5        # For system monitoring

# Optional: Django integration 
django>=4.2.0        # Optional - If scripts need Django project access

# Installation
# Create and activate a virtual environment:
# python -m venv venv
# source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate     # Windows
#
# Install dependencies:
# pip install -r requirements.txt
#
# For development only:
# pip install -r requirements.txt[dev] 