
django_apps:
  paths:
    - "api/apps"
    - "api/core"
  required_files:
    - "__init__.py"
    - "models.py"
    - "views.py"
    - "apps.py"
  optional_files:
    - "urls.py"
    - "admin.py"
    - "forms.py"
    - "serializers.py"
    - "tests.py"
    - "signals.py"
    - "managers.py"
    - "middleware.py"
    - "utils.py"
  excluded:
    - "__pycache__"
    - "migrations"
    - "tests"

react_components:
  paths:
    - "ui/src/components"
    - "ui/src/features/*/components"
  required_files:
    - "index.tsx"
  optional_files:
    - "styles.ts"
    - "types.ts"
    - "utils.ts"
    - "tests.tsx"
  naming_convention: "PascalCase"
  excluded:
    - "node_modules"
    - "dist"
    - "__tests__"

react_features:
  paths:
    - "ui/src/features"
  required_subdirs:
    - "components"
    - "hooks"
  optional_subdirs:
    - "api"
    - "store"
    - "utils"
    - "types"
    - "pages"
  required_files:
    - "index.ts"
  excluded:
    - "node_modules"
    - "dist"
    - "__tests__"

root_structure:
  required_dirs:
    - "api"
    - "ui"
    - "scripts"
    - "docs"
  optional_dirs:
    - "docker"
    - "tools"
    - "ci"
    - "logs"
  required_files:
    - "README.md"
    - "docker-compose.yml"
    - ".gitignore"
  optional_files:
    - "Makefile"
    - ".env.example"
    - "CHANGELOG.md"
    - "LICENSE" 