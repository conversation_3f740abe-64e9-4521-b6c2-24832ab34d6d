
"""
Git Hooks Setup Script

This script sets up git hooks for automatic code formatting and comment cleaning
during the commit process.
"""

import os
import sys
import stat
import shutil
import argparse
from pathlib import Path


def find_git_directory():
    """Find the .git directory in the current repository."""
    current = Path.cwd()
    
    
    while current != current.parent:
        git_dir = current / ".git"
        if git_dir.exists():
            return git_dir
        current = current.parent
    
    return None


def create_pre_commit_hook(git_hooks_dir: Path, script_dir: Path):
    """Create the pre-commit hook file."""
    hook_path = git_hooks_dir / "pre-commit"
    pre_commit_script = script_dir / "git_hooks" / "pre_commit_hook.py"
    
    if not pre_commit_script.exists():
        print(f"❌ Pre-commit script not found: {pre_commit_script}")
        return False
    
    
    hook_content = f"""#!/bin/bash
# Auto-generated pre-commit hook
# Runs code formatting and comment cleaning

cd "$(git rev-parse --show-toplevel)"
python3 "{pre_commit_script.absolute()}"
"""
    
    
    with open(hook_path, 'w') as f:
        f.write(hook_content)
    
    
    hook_path.chmod(hook_path.stat().st_mode | stat.S_IEXEC)
    
    print(f"✅ Created pre-commit hook: {hook_path}")
    return True


def create_commit_msg_hook(git_hooks_dir: Path, script_dir: Path):
    """Create the commit-msg hook for additional validation."""
    hook_path = git_hooks_dir / "commit-msg"
    
    
    hook_content = """#!/bin/bash
# Auto-generated commit-msg hook
# Basic commit message validation

commit_regex='^.{10,}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ Commit message too short (minimum 10 characters)"
    echo "Current message: $(cat "$1")"
    exit 1
fi

echo "✅ Commit message validated"
"""
    
    
    with open(hook_path, 'w') as f:
        f.write(hook_content)
    
    
    hook_path.chmod(hook_path.stat().st_mode | stat.S_IEXEC)
    
    print(f"✅ Created commit-msg hook: {hook_path}")
    return True


def backup_existing_hooks(git_hooks_dir: Path):
    """Backup any existing git hooks."""
    hooks_to_backup = ["pre-commit", "commit-msg", "pre-push"]
    backed_up = []
    
    for hook_name in hooks_to_backup:
        hook_path = git_hooks_dir / hook_name
        if hook_path.exists():
            backup_path = git_hooks_dir / f"{hook_name}.backup"
            shutil.copy2(hook_path, backup_path)
            backed_up.append(hook_name)
            print(f"📋 Backed up existing hook: {hook_name} -> {hook_name}.backup")
    
    return backed_up


def check_dependencies():
    """Check if required dependencies are available."""
    dependencies = {
        "python3": "Python 3 interpreter",
        "git": "Git version control",
        "black": "Python formatter (optional but recommended)",
        "isort": "Python import sorter (optional but recommended)",
        "npx": "Node.js package runner (for prettier)"
    }
    
    missing = []
    available = []
    
    for cmd, description in dependencies.items():
        try:
            import subprocess
            result = subprocess.run(
                [cmd, "--version"], 
                capture_output=True, 
                timeout=3
            )
            if result.returncode == 0:
                available.append(f"✅ {cmd} - {description}")
            else:
                missing.append(f"❌ {cmd} - {description}")
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
            missing.append(f"❌ {cmd} - {description}")
    
    print("\n📋 Dependency Check:")
    for item in available:
        print(f"  {item}")
    
    if missing:
        print("\n⚠️  Missing Dependencies (some features may not work):")
        for item in missing:
            print(f"  {item}")
        print("\nInstall missing dependencies:")
        print("  npm install -g prettier  # For JS/TS/CSS/HTML formatting")
        print("  pip install black isort  # For Python formatting")
        print("  brew install shfmt       # For shell script formatting (macOS)")
    
    return len(missing) == 0


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup git hooks for code formatting and comment cleaning")
    parser.add_argument("--force", "-f", action="store_true", help="Overwrite existing hooks without backup")
    parser.add_argument("--check-deps", "-c", action="store_true", help="Only check dependencies")
    parser.add_argument("--uninstall", "-u", action="store_true", help="Uninstall git hooks")
    
    args = parser.parse_args()
    
    if args.check_deps:
        check_dependencies()
        return 0
    
    
    git_dir = find_git_directory()
    if not git_dir:
        print("❌ Not in a git repository")
        return 1
    
    git_hooks_dir = git_dir / "hooks"
    git_hooks_dir.mkdir(exist_ok=True)
    
    
    script_dir = Path(__file__).parent
    
    print(f"🔧 Setting up git hooks in: {git_hooks_dir}")
    
    if args.uninstall:
        
        hooks_to_remove = ["pre-commit", "commit-msg"]
        for hook_name in hooks_to_remove:
            hook_path = git_hooks_dir / hook_name
            if hook_path.exists():
                hook_path.unlink()
                print(f"🗑️  Removed hook: {hook_name}")
        
        print("✅ Git hooks uninstalled")
        return 0
    
    
    if not check_dependencies():
        print("\n⚠️  Some dependencies are missing. Hooks will still be installed but may not work fully.")
        response = input("Continue anyway? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            return 1
    
    
    if not args.force:
        backup_existing_hooks(git_hooks_dir)
    
    
    success = True
    
    success &= create_pre_commit_hook(git_hooks_dir, script_dir)
    success &= create_commit_msg_hook(git_hooks_dir, script_dir)
    
    if success:
        print("\n🎉 Git hooks setup completed successfully!")
        print("\nNext steps:")
        print("1. Make a test commit to verify the hooks work")
        print("2. Configure formatter settings in scripts/format_code/config.yml")
        print("3. Configure comment cleaner in scripts/clean_comments/config.yml")
        print("\nThe hooks will now automatically:")
        print("- Format code files (Python, JS/TS, CSS, etc.)")
        print("- Clean unnecessary comments")
        print("- Re-stage modified files")
        return 0
    else:
        print("\n❌ Some hooks failed to install")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 