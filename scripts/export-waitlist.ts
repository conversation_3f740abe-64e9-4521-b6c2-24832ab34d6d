import { PrismaClient } from "@prisma/client";
import * as fs from "fs";
import * as path from "path";

interface ExportStatistics {
  statusBreakdown: Record<string, number>;
  priorityBreakdown: Record<number, number>;
  notificationTypeBreakdown: Record<string, number>;
  averagePriority: number;
  recentEntriesCount: number;
}

interface ExportData {
  exportedAt: string;
  totalEntries: number;
  totalNotifications: number;
  waitlistEntries: any[];
  notifications: any[];
  statistics: ExportStatistics;
}

const prisma = new PrismaClient();

/**
 * Validates environment and prerequisites
 */
function validateEnvironment(): void {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable is required");
  }
}

/**
 * Creates export directory with proper error handling
 */
function ensureExportDirectory(exportDir: string): void {
  try {
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
      console.log(`📁 Created export directory: ${exportDir}`);
    }
  } catch (error) {
    throw new Error(
      `Failed to create export directory: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

/**
 * Generates comprehensive statistics from waitlist data
 */
function generateStatistics(
  waitlistEntries: any[],
  notifications: any[],
): ExportStatistics {
  const statusBreakdown = waitlistEntries.reduce(
    (acc, entry) => {
      acc[entry.status] = (acc[entry.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  const priorityBreakdown = waitlistEntries.reduce(
    (acc, entry) => {
      acc[entry.priority] = (acc[entry.priority] || 0) + 1;
      return acc;
    },
    {} as Record<number, number>,
  );

  const notificationTypeBreakdown = notifications.reduce(
    (acc, notif) => {
      acc[notif.type] = (acc[notif.type] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  const averagePriority =
    waitlistEntries.length > 0
      ? waitlistEntries.reduce((sum, entry) => sum + entry.priority, 0) /
        waitlistEntries.length
      : 0;

  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const recentEntriesCount = waitlistEntries.filter(
    (entry) => new Date(entry.createdAt) > sevenDaysAgo,
  ).length;

  return {
    statusBreakdown,
    priorityBreakdown,
    notificationTypeBreakdown,
    averagePriority: Math.round(averagePriority * 100) / 100,
    recentEntriesCount,
  };
}

/**
 * Safely writes data to file with proper error handling
 */
function writeExportFile(filename: string, data: ExportData): void {
  try {
    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filename, jsonData, { encoding: "utf8", mode: 0o644 });
  } catch (error) {
    throw new Error(
      `Failed to write export file: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

/**
 * Main export function with comprehensive error handling
 */
async function exportWaitlist(): Promise<void> {
  const startTime = Date.now();

  try {
    console.log(`🚀 Starting waitlist export at ${new Date().toISOString()}`);

    // Validate environment
    validateEnvironment();

    // Fetch data with error handling
    console.log("📊 Fetching waitlist entries...");
    const waitlistEntries = await prisma.waitlistEntry.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log("📧 Fetching notifications...");
    const notifications = await prisma.waitlistNotification.findMany({
      orderBy: {
        sentAt: "desc",
      },
    });

    console.log(
      `✅ Data fetched successfully: ${waitlistEntries.length} entries, ${notifications.length} notifications`,
    );

    // Generate comprehensive statistics
    const statistics = generateStatistics(waitlistEntries, notifications);

    // Create export data structure
    const exportData: ExportData = {
      exportedAt: new Date().toISOString(),
      totalEntries: waitlistEntries.length,
      totalNotifications: notifications.length,
      waitlistEntries,
      notifications,
      statistics,
    };

    // Ensure export directory exists
    const exportDir = path.join(process.cwd(), "exports");
    ensureExportDirectory(exportDir);

    // Generate secure filename with timestamp
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .split(".")[0];
    const filename = path.join(exportDir, `waitlist-export-${timestamp}.json`);

    // Write to file
    writeExportFile(filename, exportData);

    const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);

    // Success logging
    console.log(`✅ Successfully exported waitlist data to ${filename}`);
    console.log(`⏱️  Export completed in ${executionTime}s`);
    console.log(`📊 Export Statistics:`);
    console.log(`   • Total entries: ${exportData.totalEntries}`);
    console.log(`   • Total notifications: ${exportData.totalNotifications}`);
    console.log(
      `   • Recent entries (7 days): ${statistics.recentEntriesCount}`,
    );
    console.log(`   • Average priority: ${statistics.averagePriority}`);
    console.log(`   • Status breakdown:`, statistics.statusBreakdown);
    console.log(`   • Priority breakdown:`, statistics.priorityBreakdown);
    console.log(
      `   • Notification types:`,
      statistics.notificationTypeBreakdown,
    );
  } catch (error) {
    const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);
    console.error(`❌ Error exporting waitlist (after ${executionTime}s):`);

    if (error instanceof Error) {
      console.error(`   Message: ${error.message}`);
      console.error(`   Stack: ${error.stack}`);
    } else {
      console.error(`   Unknown error:`, error);
    }

    process.exit(1);
  } finally {
    console.log("🔌 Disconnecting from database...");
    await prisma.$disconnect();
  }
}

// Handle process termination gracefully
process.on("SIGINT", async () => {
  console.log("\n⚠️  Process interrupted, cleaning up...");
  await prisma.$disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n⚠️  Process terminated, cleaning up...");
  await prisma.$disconnect();
  process.exit(0);
});

// Run the export
if (require.main === module) {
  exportWaitlist();
}
