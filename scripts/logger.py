"""
Centralized logging module for all scripts.

This module provides a consistent logging interface with the following features:
- Console output with colored formatting
- File logging with timestamped filenames
- Configurable verbosity levels
- Statistics tracking
- Summary reporting

All scripts should use this logger to ensure consistent log formatting and centralized access to logs.
"""

import os
import sys
import time
import logging
from datetime import datetime
from pathlib import Path


SCRIPT_DIR = Path(__file__).parent
LOG_DIR = SCRIPT_DIR / "logs"


if not LOG_DIR.exists():
    LOG_DIR.mkdir(parents=True, exist_ok=True)


COLORS = {
    "RESET": "\033[0m",
    "BOLD": "\033[1m",
    "RED": "\033[31m",
    "GREEN": "\033[32m",
    "YELLOW": "\033[33m",
    "BLUE": "\033[34m",
    "MAGENTA": "\033[35m",
    "CYAN": "\033[36m",
    "GRAY": "\033[37m",
}


class Logger:
    """Custom logger for script operations with console and file output."""

    def __init__(self, script_name, verbose=False):
        """
        Initialize the logger.

        Args:
            script_name: Name of the script using this logger (used for log file naming)
            verbose: Whether to show DEBUG level messages
        """
        self.script_name = script_name
        self.verbose = verbose
        self.start_time = time.time()
        self.stats = {}

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{script_name}_{timestamp}.log"
        self.log_file = LOG_DIR / log_filename

        self.console = logging.getLogger(f"{script_name}_console")
        self.console.setLevel(logging.DEBUG if verbose else logging.INFO)

        if not self.console.handlers:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(ColoredFormatter())
            self.console.addHandler(console_handler)

        self.file_logger = logging.getLogger(f"{script_name}_file")
        self.file_logger.setLevel(logging.DEBUG)

        if not self.file_logger.handlers:
            file_handler = logging.FileHandler(self.log_file)
            formatter = logging.Formatter(
                "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
            )
            file_handler.setFormatter(formatter)
            self.file_logger.addHandler(file_handler)

        self.info(
            f"Started {script_name} at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        self.debug(f"Log file: {self.log_file}")

    def start_script(self):
        """Start script logging - already handled by __init__, included for compatibility."""

        pass

    def debug(self, message):
        """Log a debug message."""
        self.console.debug(message)
        self.file_logger.debug(message)

    def info(self, message):
        """Log an info message."""
        self.console.info(message)
        self.file_logger.info(message)

    def warning(self, message):
        """Log a warning message."""
        self.console.warning(message)
        self.file_logger.warning(message)

    def error(self, message):
        """Log an error message."""
        self.console.error(message)
        self.file_logger.error(message)

    def critical(self, message):
        """Log a critical message."""
        self.console.critical(message)
        self.file_logger.critical(message)

    def log_file_action(self, filepath, action, **stats):
        """
        Log a file-related action and collect statistics.

        Args:
            filepath: Path to the file
            action: Action being performed (process, modify, etc.)
            stats: Optional statistics to collect (count_*: incremented, other keys: accumulated)
        """
        self.debug(f"{action.capitalize()}: {filepath}")

        for key, value in stats.items():
            if key not in self.stats:
                self.stats[key] = 0
            self.stats[key] += value

    def print_summary(self):
        """Print a summary of collected statistics."""
        if not self.stats:
            self.info("No statistics collected.")
            return ""

        summary_lines = ["", f"{self.script_name} Summary:"]
        summary_lines.append("=" * 40)

        for key, value in sorted(self.stats.items()):
            pretty_key = " ".join(word.capitalize() for word in key.split("_"))
            summary_lines.append(f"{pretty_key}: {value}")

        summary_lines.append("=" * 40)
        summary = "\n".join(summary_lines)

        self.info(summary)
        return summary

    def end_script(self):
        """
        Log script completion and return execution time.

        Returns:
            float: Execution time in seconds
        """
        execution_time = time.time() - self.start_time
        self.info(f"Completed {self.script_name} in {execution_time:.2f} seconds")
        return execution_time


class ColoredFormatter(logging.Formatter):
    """Formatter that adds colors to log level names in terminal output."""

    LEVEL_COLORS = {
        "DEBUG": COLORS["GRAY"],
        "INFO": COLORS["RESET"],
        "WARNING": COLORS["YELLOW"],
        "ERROR": COLORS["RED"],
        "CRITICAL": COLORS["RED"] + COLORS["BOLD"],
    }

    def format(self, record):
        """Format the log record with colored level name."""
        levelname = record.levelname
        if levelname in self.LEVEL_COLORS:
            record.levelname = (
                f"{self.LEVEL_COLORS[levelname]}{levelname}{COLORS['RESET']}"
            )

        return f"{record.levelname}: {record.getMessage()}"


def get_logger(script_name, verbose=False):
    """
    Get a logger instance for the given script name.

    Args:
        script_name: Name of the script
        verbose: Whether to show DEBUG level messages

    Returns:
        Logger: A logger instance
    """
    return Logger(script_name, verbose)


if __name__ == "__main__":

    logger = get_logger("logger_demo", verbose=True)

    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")

    logger.log_file_action("example.py", "process", files_processed=1)
    logger.log_file_action(
        "example2.py", "modify", files_processed=1, lines_modified=10
    )

    logger.print_summary()
    logger.end_script()
