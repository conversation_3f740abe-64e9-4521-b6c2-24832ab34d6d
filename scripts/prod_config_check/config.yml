
# Security-related checks
# Security-related checks
security_checks:
  debug_must_be_false: true
  secret_key_must_be_set: true
  allowed_hosts_must_be_set: true
  csrf_cookie_secure: true
  secure_ssl_redirect: true
  session_cookie_secure: true
  secure_hsts_seconds: true
  secure_hsts_include_subdomains: true
  secure_content_type_nosniff: true
  secure_browser_xss_filter: true
  x_frame_options: true

performance_checks:
  cache_configured: true
  database_conn_max_age: true
  static_root_set: true
  media_root_set: true
  whitenoise_middleware_used: true
  compression_enabled: true

middleware_checks:
  security_middleware: true
  cache_middleware: true
  cors_middleware: true
  gzip_middleware: true

minimum_values:
  database_conn_max_age: 60
  secure_hsts_seconds: 31536000

insecure_settings:
  - "RECAPTCHA_PRIVATE_KEY"
  - "EMAIL_HOST_PASSWORD"
  - "MAILGUN_API_KEY"
  - "AWS_SECRET_ACCESS_KEY"
  - "AWS_ACCESS_KEY_ID"
  - "STRIPE_API_KEY"
  - "GOOGLE_API_KEY"
  - "DATABASE_URL"
  - "REDIS_URL"
  - "DJANGO_SECRET_KEY"

excluded_checks:

settings_file: "api/config/settings/prod.py" 