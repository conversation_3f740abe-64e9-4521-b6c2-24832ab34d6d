# Production Configuration Checker

A comprehensive validation tool for Django settings to ensure your application is production-ready. This utility analyzes settings for security, performance, and best practices to prevent common deployment issues.

## Features

- **Security Checks**: Verifies critical security settings are properly configured
- **Performance Checks**: Ensures performance-related settings are optimized
- **Middleware Checks**: Validates essential middleware components are present and properly ordered
- **Secret Detection**: Identifies potentially hardcoded secrets that should be environment variables
- **Detailed Reporting**: Provides clear error and warning messages with recommendations
- **Configurable Rules**: All validation rules can be customized via YAML configuration

## Installation

No additional installation required beyond Python and PyYAML.

## Usage

### Basic Usage

```bash
# Run with default configuration
python check_prod_config.py

# Run with verbose output
python check_prod_config.py --verbose

# Run with custom configuration
python check_prod_config.py --config /path/to/config.yml

# Show summary only
python check_prod_config.py --summary
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--config`, `-c` | Path to configuration file (default: config.yml in script directory) |
| `--verbose`, `-v` | Show detailed output during validation |
| `--summary`, `-s` | Show summary of check results |

## Configuration

The checker is configured through a YAML file that defines which settings to check and their expected values:

```yaml
# Security-related checks
security_checks:
  debug_must_be_false: true
  secret_key_must_be_set: true
  allowed_hosts_must_be_set: true
  csrf_cookie_secure: true
  secure_ssl_redirect: true
  session_cookie_secure: true
  secure_hsts_seconds: true
  secure_hsts_include_subdomains: true
  secure_content_type_nosniff: true

# Performance-related checks
performance_checks:
  cache_configured: true
  database_conn_max_age: true
  static_root_set: true
  media_root_set: true
  whitenoise_middleware_used: true

# Middleware checks
middleware_checks:
  security_middleware: true
  cache_middleware: true
  cors_middleware: true

# Minimum values for numeric settings
minimum_values:
  database_conn_max_age: 60
  secure_hsts_seconds: 31536000

# Settings that should not be hardcoded
insecure_settings:
  - "RECAPTCHA_PRIVATE_KEY"
  - "EMAIL_HOST_PASSWORD"
  - "MAILGUN_API_KEY"

# Path to the production settings file
settings_file: "api/config/settings/prod.py"

# Checks to exclude
excluded_checks: []
```

### Configuration Sections

| Section | Description |
|---------|-------------|
| `security_checks` | Security-related Django settings to validate |
| `performance_checks` | Performance optimization settings to check |
| `middleware_checks` | Required middleware components to verify |
| `minimum_values` | Minimum numeric values for certain settings |
| `insecure_settings` | Settings that should be loaded from environment variables |
| `settings_file` | Path to the production settings file to analyze |
| `excluded_checks` | List of specific checks to skip |

## Validation Rules

### Security Settings

- **DEBUG**: Must be set to False in production
- **SECRET_KEY**: Must be set and not use default/sample values
- **ALLOWED_HOSTS**: Must not be empty and should not contain wildcards
- **CSRF_COOKIE_SECURE**: Should be True to ensure CSRF cookies are sent over HTTPS
- **SECURE_SSL_REDIRECT**: Should be True to redirect HTTP to HTTPS
- **SESSION_COOKIE_SECURE**: Should be True to ensure session cookies are sent over HTTPS
- **SECURE_HSTS_SECONDS**: Should be set to at least 31536000 (1 year)
- **SECURE_HSTS_INCLUDE_SUBDOMAINS**: Should be True to protect subdomains
- **SECURE_CONTENT_TYPE_NOSNIFF**: Should be True to prevent MIME type sniffing

### Performance Settings

- **CACHES**: Should not use local memory cache in production
- **DATABASE CONN_MAX_AGE**: Should be set to a reasonable value (default minimum: 60)
- **STATIC_ROOT**: Must be set for collecting static files
- **MEDIA_ROOT**: Must be set for file uploads
- **WHITENOISE**: Middleware should be used for efficient static file serving

### Middleware Checks

- **SecurityMiddleware**: Must be included
- **CacheMiddleware**: Should be included for performance
- **CORSMiddleware**: Should be included for API access control

## Examples

### Focus on Security Checks Only

To validate only security settings:

```yaml
security_checks:
  debug_must_be_false: true
  secret_key_must_be_set: true
  allowed_hosts_must_be_set: true
  csrf_cookie_secure: true
  secure_ssl_redirect: true
  session_cookie_secure: true

performance_checks: {}
middleware_checks: {}
insecure_settings: []
```

### Custom Settings File Location

To check a different settings file:

```yaml
settings_file: "myproject/settings/production.py"
```

## Integration with DevOps Pipeline

This tool is designed to be integrated into CI/CD and DevOps workflows:

1. Run as a pre-deployment check to catch configuration issues
2. Returns non-zero exit code on validation failure (blocks deployment)
3. Generates detailed logs for audit trails
4. Integrates with the central logging system

## Troubleshooting

### Common Issues

1. **Settings Module Not Found**: Verify the path to your Django settings file is correct
2. **Import Errors**: Ensure all dependencies required by your settings are available
3. **Environment Variables**: Some checks may fail if environment variables referenced in settings are not available
4. **Permissions**: Ensure the script has read access to the settings file

### Resolving Validation Failures

For each error or warning reported:

1. Check the failed setting in your Django settings file
2. Refer to Django's documentation for best practices
3. Make the recommended changes
4. Re-run the checker to confirm the issue is resolved 