

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List


def distribute_translations():
    """
    Distribute all translations to component/feature/page/layout-specific locales folders.
    Following Senior <PERSON><PERSON><PERSON> best practices for modular, reusable components.
    Every folder that represents pages, layouts, components, or features gets its own locales.
    """

    print("🌍 Starting comprehensive translation distribution...")

    
    ui_src = Path("ui/src")
    centralized_locales = ui_src / "locales"
    languages = ["ar", "de", "en", "es", "fr", "it"]

    
    translation_mappings = {
        
        "features/admin/components": ["admin", "moderation", "roles"],
        "features/admin/pages": ["admin", "moderation", "roles"],
        
        "features/analytics/components": ["admin"],
        "features/analytics/pages": ["admin"],
        
        "features/carnivore/achievements/components": ["achievements"],
        "features/carnivore/achievements/pages": ["achievements"],
        "features/carnivore/achievements/components/analytics": ["achievements"],
        "features/carnivore/achievements/components/challenges": ["achievements"],
        "features/carnivore/achievements/components/dashboard": ["achievements"],
        "features/carnivore/achievements/components/forms": ["achievements"],
        "features/carnivore/achievements/components/milestones": ["achievements"],
        "features/carnivore/achievements/components/notifications": ["achievements"],
        "features/carnivore/achievements/components/streaks": ["achievements"],
        
        "features/calculators/bmi/components": ["bmi", "calculators"],
        "features/calculators/bmi/pages": ["bmi", "calculators"],
        "features/calculators/body_fat/components": [
            "body_fat_calculator",
            "calculators",
        ],
        "features/calculators/body_fat/pages": ["body_fat_calculator", "calculators"],
        "features/calculators/calorie/components": [
            "calorie_calculator",
            "calculators",
        ],
        "features/calculators/calorie/pages": ["calorie_calculator", "calculators"],
        "features/calculators/heart_rate_zones/components": [
            "heart_rate_zones",
            "calculators",
        ],
        "features/calculators/heart_rate_zones/pages": [
            "heart_rate_zones",
            "calculators",
        ],
        "features/calculators/macro/components": ["macro_calculator", "calculators"],
        "features/calculators/macro/pages": ["macro_calculator", "calculators"],
        "features/calculators/tdee/components": ["tdee", "calculators"],
        "features/calculators/tdee/pages": ["tdee", "calculators"],
        "features/calculators/water_intake/components": [
            "water_intake_calculator",
            "calculators",
        ],
        "features/calculators/water_intake/pages": [
            "water_intake_calculator",
            "calculators",
        ],
        "features/calculators/pages": ["calculators"],
        
        "features/common/pages": ["common"],
        
        "features/content-management/components": ["common"],
        "features/content-management/pages": ["common"],
        
        "features/documentation/components": ["common"],
        "features/documentation/pages": ["common"],
        "features/documentation/admin": ["admin"],
        "features/documentation/admin/components": ["admin"],
        "features/documentation/examples": ["common"],
        "features/documentation/integration": ["common"],
        "features/documentation/layouts": ["common"],
        "features/documentation/tests": ["common"],
        
        "features/forum/components": ["forum", "editor"],
        "features/forum/pages": ["forum", "editor"],
        "features/forum/tests": ["forum"],
        
        "features/moderation/components": ["moderation", "admin"],
        "features/moderation/pages": ["moderation", "admin"],
        
        "features/notifications": ["common"],
        
        "features/users/components": ["users", "profile"],
        "features/users/pages": ["users", "profile"],
        
        "shared/components/ui": ["ui"],
        "shared/components/primitives": ["ui"],
        "shared/components/layout": ["ui"],
        "shared/layouts": ["ui"],
        
        "components/ui": ["ui"],
        "components/layout": ["ui"],
        "components/primitives": ["ui"],
        "components/user": ["users", "profile"],
        "components/utils": ["ui"],
        
        "layouts": ["ui"],
        
        "guards": ["ui"],
        
        "core": ["system"],
    }

    
    distribute_feature_translations(
        ui_src, centralized_locales, languages, translation_mappings
    )

    
    create_missing_locales_folders(ui_src, languages)

    
    update_i18n_config(ui_src, languages)

    
    create_translation_utilities(ui_src)

    print("✅ Translation distribution completed successfully!")


def distribute_feature_translations(
    ui_src, centralized_locales, languages, translation_mappings
):
    """Distribute translations to specific component/feature folders."""
    print("📦 Distributing translations to component/feature folders...")

    for folder_path, translation_keys in translation_mappings.items():
        target_dir = ui_src / folder_path
        if target_dir.exists():
            locales_dir = target_dir / "locales"
            locales_dir.mkdir(exist_ok=True)

            for lang in languages:
                lang_dir = locales_dir / lang
                lang_dir.mkdir(exist_ok=True)

                
                for key in translation_keys:
                    source_file = centralized_locales / lang / f"{key}.json"
                    if source_file.exists():
                        destination = lang_dir / f"{key}.json"
                        if not destination.exists():
                            shutil.copy2(source_file, destination)
                            print(
                                f"  📄 Copied {key}.json ({lang}) to {folder_path}/locales/"
                            )


def create_missing_locales_folders(ui_src, languages):
    """Create locales folders for any missing component/page/layout directories."""
    print("🏗️ Creating locales folders for all components/pages/layouts...")

    
    target_patterns = [
        "components/*",
        "features/*/components",
        "features/*/components/*",
        "features/*/pages",
        "features/*/layouts",
        "features/*/admin",
        "features/*/admin/components",
        "features/*/examples",
        "features/*/integration",
        "features/*/tests",
        "shared/components/*",
        "shared/layouts",
        "layouts",
        "guards",
        "pages",
        "core",
    ]

    for pattern in target_patterns:
        for directory in ui_src.glob(pattern):
            if (
                directory.is_dir()
                and not directory.name.startswith(".")
                and not directory.name.startswith("__")
            ):
                locales_dir = directory / "locales"
                if not locales_dir.exists():
                    locales_dir.mkdir(exist_ok=True)

                    for lang in languages:
                        lang_dir = locales_dir / lang
                        lang_dir.mkdir(exist_ok=True)

                        
                        default_file = lang_dir / "index.json"
                        if not default_file.exists():
                            create_default_translation_file(
                                default_file, directory.name, lang
                            )

                    print(f"  📁 Created locales for: {directory.relative_to(ui_src)}")


def create_default_translation_file(file_path, component_name, language):
    """Create a default translation file for a component."""
    default_translations = {
        "component_name": component_name,
        "common": {
            "loading": (
                "Loading..."
                if language == "en"
                else (
                    "جاري التحميل..."
                    if language == "ar"
                    else (
                        "Chargement..."
                        if language == "fr"
                        else (
                            "Cargando..."
                            if language == "es"
                            else "Caricamento..." if language == "it" else "Laden..."
                        )
                    )
                )
            ),
            "error": (
                "Error"
                if language == "en"
                else (
                    "خطأ"
                    if language == "ar"
                    else (
                        "Erreur"
                        if language == "fr"
                        else (
                            "Error"
                            if language == "es"
                            else "Errore" if language == "it" else "Fehler"
                        )
                    )
                )
            ),
            "success": (
                "Success"
                if language == "en"
                else (
                    "نجح"
                    if language == "ar"
                    else (
                        "Succès"
                        if language == "fr"
                        else (
                            "Éxito"
                            if language == "es"
                            else "Successo" if language == "it" else "Erfolg"
                        )
                    )
                )
            ),
        },
    }

    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(default_translations, f, indent=2, ensure_ascii=False)


def update_i18n_config(ui_src, languages):
    """Update i18n configuration to support distributed translations."""
    print("⚙️ Updating i18n configuration for distributed translations...")

    i18n_config = ui_src / "locales" / "i18n.ts"

    config_content = f"""import i18n from 'i18next';
import {{ initReactI18next }} from 'react-i18next';
import Backend from 'i18next-fs-backend';

// Dynamic import function for distributed translations
const loadTranslations = async (lng: string, ns: string, componentPath?: string) => {{
  try {{
    // Try component-specific translations first
    if (componentPath) {{
      const componentTranslation = await import(`../{{componentPath}}/locales/${{lng}}/${{ns}}.json`);
      return componentTranslation.default || componentTranslation;
    }}
    
    // Fallback to centralized translations (for backward compatibility)
    const centralizedTranslation = await import(`./${{lng}}/${{ns}}.json`);
    return centralizedTranslation.default || centralizedTranslation;
  }} catch (error) {{
    console.warn(`Failed to load translation: ${{lng}}/${{ns}} from ${{componentPath || 'centralized'}}`);
    return {{}};
  }}
}};

// Custom backend for distributed translations
const distributedBackend = {{
  type: 'backend' as const,
  init: () => {{}},
  read: async (language: string, namespace: string, callback: any) => {{
    try {{
      const translations = await loadTranslations(language, namespace);
      callback(null, translations);
    }} catch (error) {{
      callback(error, null);
    }}
  }}
}};

i18n
  .use(distributedBackend)
  .use(initReactI18next)
  .init({{
    lng: 'en',
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {{
      escapeValue: false,
    }},
    
    // Support for distributed namespaces
    ns: ['common', 'ui', 'auth', 'admin', 'forum', 'social', 'calculators', 'achievements'],
    defaultNS: 'common',
    
    // Distributed translation loading
    backend: {{
      loadPath: (lng: string[], ns: string[]) => {{
        // This will be handled by our custom backend
        return `${{lng[0]}}/${{ns[0]}}`;
      }}
    }},
    
    // React specific options
    react: {{
      useSuspense: false,
    }},
  }});

// Helper function for components to load their specific translations
export const useComponentTranslation = (componentPath: string, namespaces: string[] = ['index']) => {{
  const loadComponentTranslations = async () => {{
    const translations: Record<string, any> = {{}};
    
    for (const ns of namespaces) {{
      for (const lng of {languages}) {{
        try {{
          const translation = await loadTranslations(lng, ns, componentPath);
          if (!translations[lng]) translations[lng] = {{}};
          translations[lng][ns] = translation;
        }} catch (error) {{
          console.warn(`Failed to load ${{componentPath}}/${{lng}}/${{ns}}.json`);
        }}
      }}
    }}
    
    return translations;
  }};
  
  return {{ loadComponentTranslations }};
}};

export default i18n;
"""

    with open(i18n_config, "w") as f:
        f.write(config_content)


def create_translation_utilities(ui_src):
    """Create utility functions for working with distributed translations."""
    print("🛠️ Creating translation utilities...")

    utils_dir = ui_src / "utils"
    utils_dir.mkdir(exist_ok=True)

    translation_utils = utils_dir / "translationUtils.ts"

    utils_content = """import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

/**
 * Hook for loading component-specific translations
 * Following Senior Django Developer best practices for modular components
 */
export const useComponentTranslations = (
  componentPath: string,
  namespaces: string[] = ['index'],
  fallbackNamespace = 'common'
) => {
  const { t, i18n } = useTranslation();
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        setError(null);
        
        // Load all required namespaces for this component
        for (const ns of namespaces) {
          if (!i18n.hasResourceBundle(i18n.language, ns)) {
            try {
              // Dynamic import of component-specific translations
              const translations = await import(`../${componentPath}/locales/${i18n.language}/${ns}.json`);
              i18n.addResourceBundle(i18n.language, ns, translations.default || translations);
            } catch (importError) {
              console.warn(`Failed to load ${componentPath}/locales/${i18n.language}/${ns}.json, using fallback`);
              
              // Try fallback namespace
              if (ns !== fallbackNamespace) {
                try {
                  const fallback = await import(`../locales/${i18n.language}/${fallbackNamespace}.json`);
                  i18n.addResourceBundle(i18n.language, ns, fallback.default || fallback);
                } catch (fallbackError) {
                  console.warn(`Fallback translation also failed for ${ns}`);
                }
              }
            }
          }
        }
        
        setIsLoaded(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load translations');
        setIsLoaded(true);
      }
    };

    loadTranslations();
  }, [componentPath, namespaces, fallbackNamespace, i18n]);

  /**
   * Translation function with component-specific namespace support
   */
  const ct = (key: string, options?: any) => {
    // Try each namespace in order
    for (const ns of namespaces) {
      const translatedValue = t(`${ns}:${key}`, { ...options, defaultValue: undefined });
      if (translatedValue !== key && translatedValue !== `${ns}:${key}`) {
        return translatedValue;
      }
    }
    
    // Fallback to default translation
    return t(key, options);
  };

  return {
    t: ct,
    isLoaded,
    error,
    ready: isLoaded && !error
  };
};

/**
 * Higher-order component for automatic translation loading
 */
export const withTranslations = (
  componentPath: string,
  namespaces: string[] = ['index']
) => {
  return function <P extends object>(Component: React.ComponentType<P>) {
    return function TranslatedComponent(props: P) {
      const { ready } = useComponentTranslations(componentPath, namespaces);
      
      if (!ready) {
        return <div>Loading translations...</div>; // You can customize this
      }
      
      return <Component {...props} />;
    };
  };
};

/**
 * Utility to get translation keys for a component
 */
export const getTranslationKeys = (componentPath: string, language = 'en') => {
  // This would typically be used in development/tooling
  return import(`../${componentPath}/locales/${language}/index.json`)
    .then(module => Object.keys(module.default || module))
    .catch(() => []);
};

/**
 * Validate that all required translation keys exist for a component
 */
export const validateTranslations = async (
  componentPath: string,
  requiredKeys: string[],
  languages = ['ar', 'de', 'en', 'es', 'fr', 'it']
) => {
  const missingTranslations: Record<string, string[]> = {};
  
  for (const lang of languages) {
    try {
      const translations = await import(`../${componentPath}/locales/${lang}/index.json`);
      const availableKeys = Object.keys(translations.default || translations);
      const missing = requiredKeys.filter(key => !availableKeys.includes(key));
      
      if (missing.length > 0) {
        missingTranslations[lang] = missing;
      }
    } catch (error) {
      missingTranslations[lang] = requiredKeys; // All keys missing if file doesn't exist
    }
  }
  
  return missingTranslations;
};
"""

    with open(translation_utils, "w") as f:
        f.write(utils_content)

    print("  🛠️ Created translation utilities")


if __name__ == "__main__":
    distribute_translations()
    print("🎉 Distributed translation system completed successfully!")
