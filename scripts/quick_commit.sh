#!/bin/bash

# Quick Commit Script
# Wrapper for the optimized commit script
# Ensures commits complete within 10 seconds regardless of any issues

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMMIT_SCRIPT="$SCRIPT_DIR/commit/commit.py"

# Default message if none provided
DEFAULT_MESSAGE="chore: quick commit $(date +%Y%m%d_%H%M%S)"
COMMIT_MESSAGE="${1:-$DEFAULT_MESSAGE}"

echo "🚀 Starting quick commit..."

# Try the optimized commit script first
if python3 "$COMMIT_SCRIPT" --message "$COMMIT_MESSAGE" --stage-all --verbose 2>/dev/null; then
    echo "✅ Commit completed successfully!"
    exit 0
fi

echo "⚠️  Primary commit failed, attempting fallback..."

# Fallback: Simple git commands
if git add -A 2>/dev/null && git commit -m "$COMMIT_MESSAGE" 2>/dev/null; then
    echo "✅ Fallback commit successful!"
    exit 0
fi

# Last resort: Just try to commit what's staged
if git commit -m "$COMMIT_MESSAGE" 2>/dev/null; then
    echo "✅ Staged commit successful!"
    exit 0
fi

echo "❌ All commit attempts failed"
echo "🔧 Manual intervention required - check git status"
git status --short 2>/dev/null || true
exit 1 