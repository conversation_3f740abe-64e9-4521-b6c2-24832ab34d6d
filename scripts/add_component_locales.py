

import json
import os
from pathlib import Path
from typing import Dict, List, Set


def add_component_locales():
    """
    Add locales folder with 6 languages to every component for complete translatable reusability.
    Following Senior <PERSON><PERSON><PERSON> best practices for modular, reusable components.
    """

    print("🌍 Starting component locales addition...")

    ui_components = Path("ui/src/components")
    languages = ["ar", "de", "en", "es", "fr", "it"]

    
    component_dirs = find_all_component_directories(ui_components)

    
    for component_dir in component_dirs:
        add_locales_to_component(component_dir, languages)

    print("✅ Component locales addition completed successfully!")


def find_all_component_directories(base_path: Path) -> List[Path]:
    """Find all component directories that need locales."""
    component_dirs = []

    for item in base_path.rglob("*"):
        if item.is_dir():
            
            tsx_files = list(item.glob("*.tsx"))
            index_files = list(item.glob("index.ts"))

            
            if item.name == "locales":
                continue

            
            if tsx_files or index_files:
                component_dirs.append(item)

    return component_dirs


def add_locales_to_component(component_dir: Path, languages: List[str]):
    """Add locales directory with all languages to a component."""
    locales_dir = component_dir / "locales"

    
    if locales_dir.exists():
        print(
            f"  ✓ Locales already exists: {component_dir.relative_to(Path('ui/src/components'))}"
        )
        return

    
    locales_dir.mkdir(exist_ok=True)

    
    for lang in languages:
        lang_dir = locales_dir / lang
        lang_dir.mkdir(exist_ok=True)

        
        index_file = lang_dir / "index.json"
        create_component_translation_file(index_file, component_dir.name, lang)

    print(f"  📁 Added locales: {component_dir.relative_to(Path('ui/src/components'))}")


def create_component_translation_file(
    file_path: Path, component_name: str, language: str
):
    """Create a translation file for a specific component."""

    
    translations = generate_component_translations(component_name, language)

    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(translations, f, indent=2, ensure_ascii=False)


def generate_component_translations(component_name: str, language: str) -> Dict:
    """Generate appropriate translations based on component name and context."""

    
    base_translations = {
        "component_name": component_name,
        "common": get_common_translations(language),
    }

    
    if "button" in component_name.lower():
        base_translations["button"] = get_button_translations(language)
    elif "form" in component_name.lower():
        base_translations["form"] = get_form_translations(language)
    elif "modal" in component_name.lower() or "dialog" in component_name.lower():
        base_translations["modal"] = get_modal_translations(language)
    elif "navigation" in component_name.lower() or "nav" in component_name.lower():
        base_translations["navigation"] = get_navigation_translations(language)
    elif "search" in component_name.lower():
        base_translations["search"] = get_search_translations(language)
    elif "notification" in component_name.lower():
        base_translations["notification"] = get_notification_translations(language)
    elif "user" in component_name.lower() or "profile" in component_name.lower():
        base_translations["user"] = get_user_translations(language)
    elif (
        "forum" in component_name.lower()
        or "topic" in component_name.lower()
        or "post" in component_name.lower()
    ):
        base_translations["forum"] = get_forum_translations(language)
    elif "achievement" in component_name.lower() or "badge" in component_name.lower():
        base_translations["achievement"] = get_achievement_translations(language)
    elif "calendar" in component_name.lower() or "date" in component_name.lower():
        base_translations["calendar"] = get_calendar_translations(language)
    elif "chart" in component_name.lower() or "graph" in component_name.lower():
        base_translations["chart"] = get_chart_translations(language)
    elif "table" in component_name.lower():
        base_translations["table"] = get_table_translations(language)
    elif "mobile" in component_name.lower():
        base_translations["mobile"] = get_mobile_translations(language)
    elif "progress" in component_name.lower():
        base_translations["progress"] = get_progress_translations(language)
    elif "social" in component_name.lower():
        base_translations["social"] = get_social_translations(language)

    return base_translations


def get_common_translations(language: str) -> Dict:
    """Common translations for all components."""
    translations = {
        "en": {
            "loading": "Loading...",
            "error": "Error",
            "success": "Success",
            "cancel": "Cancel",
            "confirm": "Confirm",
            "close": "Close",
            "save": "Save",
            "edit": "Edit",
            "delete": "Delete",
            "yes": "Yes",
            "no": "No",
        },
        "ar": {
            "loading": "جاري التحميل...",
            "error": "خطأ",
            "success": "نجح",
            "cancel": "إلغاء",
            "confirm": "تأكيد",
            "close": "إغلاق",
            "save": "حفظ",
            "edit": "تحرير",
            "delete": "حذف",
            "yes": "نعم",
            "no": "لا",
        },
        "de": {
            "loading": "Laden...",
            "error": "Fehler",
            "success": "Erfolg",
            "cancel": "Abbrechen",
            "confirm": "Bestätigen",
            "close": "Schließen",
            "save": "Speichern",
            "edit": "Bearbeiten",
            "delete": "Löschen",
            "yes": "Ja",
            "no": "Nein",
        },
        "es": {
            "loading": "Cargando...",
            "error": "Error",
            "success": "Éxito",
            "cancel": "Cancelar",
            "confirm": "Confirmar",
            "close": "Cerrar",
            "save": "Guardar",
            "edit": "Editar",
            "delete": "Eliminar",
            "yes": "Sí",
            "no": "No",
        },
        "fr": {
            "loading": "Chargement...",
            "error": "Erreur",
            "success": "Succès",
            "cancel": "Annuler",
            "confirm": "Confirmer",
            "close": "Fermer",
            "save": "Enregistrer",
            "edit": "Modifier",
            "delete": "Supprimer",
            "yes": "Oui",
            "no": "Non",
        },
        "it": {
            "loading": "Caricamento...",
            "error": "Errore",
            "success": "Successo",
            "cancel": "Annulla",
            "confirm": "Conferma",
            "close": "Chiudi",
            "save": "Salva",
            "edit": "Modifica",
            "delete": "Elimina",
            "yes": "Sì",
            "no": "No",
        },
    }
    return translations.get(language, translations["en"])


def get_button_translations(language: str) -> Dict:
    """Button-specific translations."""
    translations = {
        "en": {"click": "Click", "submit": "Submit", "reset": "Reset"},
        "ar": {"click": "انقر", "submit": "إرسال", "reset": "إعادة تعيين"},
        "de": {"click": "Klicken", "submit": "Absenden", "reset": "Zurücksetzen"},
        "es": {"click": "Hacer clic", "submit": "Enviar", "reset": "Reiniciar"},
        "fr": {"click": "Cliquer", "submit": "Soumettre", "reset": "Réinitialiser"},
        "it": {"click": "Clicca", "submit": "Invia", "reset": "Reimposta"},
    }
    return translations.get(language, translations["en"])


def get_form_translations(language: str) -> Dict:
    """Form-specific translations."""
    translations = {
        "en": {
            "required": "Required",
            "optional": "Optional",
            "invalid": "Invalid input",
        },
        "ar": {"required": "مطلوب", "optional": "اختياري", "invalid": "إدخال غير صحيح"},
        "de": {
            "required": "Erforderlich",
            "optional": "Optional",
            "invalid": "Ungültige Eingabe",
        },
        "es": {
            "required": "Requerido",
            "optional": "Opcional",
            "invalid": "Entrada inválida",
        },
        "fr": {
            "required": "Requis",
            "optional": "Optionnel",
            "invalid": "Saisie invalide",
        },
        "it": {
            "required": "Richiesto",
            "optional": "Opzionale",
            "invalid": "Input non valido",
        },
    }
    return translations.get(language, translations["en"])


def get_modal_translations(language: str) -> Dict:
    """Modal/Dialog-specific translations."""
    translations = {
        "en": {"title": "Modal", "close": "Close modal"},
        "ar": {"title": "نافذة منبثقة", "close": "إغلاق النافذة"},
        "de": {"title": "Modal", "close": "Modal schließen"},
        "es": {"title": "Modal", "close": "Cerrar modal"},
        "fr": {"title": "Modal", "close": "Fermer modal"},
        "it": {"title": "Modale", "close": "Chiudi modale"},
    }
    return translations.get(language, translations["en"])


def get_navigation_translations(language: str) -> Dict:
    """Navigation-specific translations."""
    translations = {
        "en": {"home": "Home", "back": "Back", "next": "Next", "menu": "Menu"},
        "ar": {"home": "الرئيسية", "back": "رجوع", "next": "التالي", "menu": "القائمة"},
        "de": {
            "home": "Startseite",
            "back": "Zurück",
            "next": "Weiter",
            "menu": "Menü",
        },
        "es": {"home": "Inicio", "back": "Atrás", "next": "Siguiente", "menu": "Menú"},
        "fr": {"home": "Accueil", "back": "Retour", "next": "Suivant", "menu": "Menu"},
        "it": {"home": "Home", "back": "Indietro", "next": "Avanti", "menu": "Menu"},
    }
    return translations.get(language, translations["en"])


def get_search_translations(language: str) -> Dict:
    """Search-specific translations."""
    translations = {
        "en": {
            "placeholder": "Search...",
            "no_results": "No results found",
            "results": "Results",
        },
        "ar": {
            "placeholder": "البحث...",
            "no_results": "لم يتم العثور على نتائج",
            "results": "النتائج",
        },
        "de": {
            "placeholder": "Suchen...",
            "no_results": "Keine Ergebnisse gefunden",
            "results": "Ergebnisse",
        },
        "es": {
            "placeholder": "Buscar...",
            "no_results": "No se encontraron resultados",
            "results": "Resultados",
        },
        "fr": {
            "placeholder": "Rechercher...",
            "no_results": "Aucun résultat trouvé",
            "results": "Résultats",
        },
        "it": {
            "placeholder": "Cerca...",
            "no_results": "Nessun risultato trovato",
            "results": "Risultati",
        },
    }
    return translations.get(language, translations["en"])


def get_notification_translations(language: str) -> Dict:
    """Notification-specific translations."""
    translations = {
        "en": {
            "new": "New notification",
            "mark_read": "Mark as read",
            "clear_all": "Clear all",
        },
        "ar": {
            "new": "إشعار جديد",
            "mark_read": "وضع علامة كمقروء",
            "clear_all": "مسح الكل",
        },
        "de": {
            "new": "Neue Benachrichtigung",
            "mark_read": "Als gelesen markieren",
            "clear_all": "Alle löschen",
        },
        "es": {
            "new": "Nueva notificación",
            "mark_read": "Marcar como leído",
            "clear_all": "Limpiar todo",
        },
        "fr": {
            "new": "Nouvelle notification",
            "mark_read": "Marquer comme lu",
            "clear_all": "Tout effacer",
        },
        "it": {
            "new": "Nuova notifica",
            "mark_read": "Segna come letto",
            "clear_all": "Cancella tutto",
        },
    }
    return translations.get(language, translations["en"])


def get_user_translations(language: str) -> Dict:
    """User/Profile-specific translations."""
    translations = {
        "en": {
            "profile": "Profile",
            "username": "Username",
            "online": "Online",
            "offline": "Offline",
        },
        "ar": {
            "profile": "الملف الشخصي",
            "username": "اسم المستخدم",
            "online": "متصل",
            "offline": "غير متصل",
        },
        "de": {
            "profile": "Profil",
            "username": "Benutzername",
            "online": "Online",
            "offline": "Offline",
        },
        "es": {
            "profile": "Perfil",
            "username": "Nombre de usuario",
            "online": "En línea",
            "offline": "Desconectado",
        },
        "fr": {
            "profile": "Profil",
            "username": "Nom d'utilisateur",
            "online": "En ligne",
            "offline": "Hors ligne",
        },
        "it": {
            "profile": "Profilo",
            "username": "Nome utente",
            "online": "Online",
            "offline": "Offline",
        },
    }
    return translations.get(language, translations["en"])


def get_forum_translations(language: str) -> Dict:
    """Forum-specific translations."""
    translations = {
        "en": {"post": "Post", "reply": "Reply", "thread": "Thread", "topic": "Topic"},
        "ar": {"post": "منشور", "reply": "رد", "thread": "موضوع", "topic": "موضوع"},
        "de": {
            "post": "Beitrag",
            "reply": "Antwort",
            "thread": "Thread",
            "topic": "Thema",
        },
        "es": {
            "post": "Publicación",
            "reply": "Respuesta",
            "thread": "Hilo",
            "topic": "Tema",
        },
        "fr": {
            "post": "Publication",
            "reply": "Réponse",
            "thread": "Fil",
            "topic": "Sujet",
        },
        "it": {
            "post": "Post",
            "reply": "Risposta",
            "thread": "Thread",
            "topic": "Argomento",
        },
    }
    return translations.get(language, translations["en"])


def get_achievement_translations(language: str) -> Dict:
    """Achievement-specific translations."""
    translations = {
        "en": {
            "earned": "Earned",
            "progress": "Progress",
            "unlock": "Unlock",
            "badge": "Badge",
        },
        "ar": {
            "earned": "حصل عليه",
            "progress": "التقدم",
            "unlock": "فتح",
            "badge": "شارة",
        },
        "de": {
            "earned": "Verdient",
            "progress": "Fortschritt",
            "unlock": "Freischalten",
            "badge": "Abzeichen",
        },
        "es": {
            "earned": "Ganado",
            "progress": "Progreso",
            "unlock": "Desbloquear",
            "badge": "Insignia",
        },
        "fr": {
            "earned": "Gagné",
            "progress": "Progrès",
            "unlock": "Débloquer",
            "badge": "Badge",
        },
        "it": {
            "earned": "Guadagnato",
            "progress": "Progresso",
            "unlock": "Sblocca",
            "badge": "Badge",
        },
    }
    return translations.get(language, translations["en"])


def get_calendar_translations(language: str) -> Dict:
    """Calendar-specific translations."""
    translations = {
        "en": {"today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday"},
        "ar": {"today": "اليوم", "tomorrow": "غدا", "yesterday": "أمس"},
        "de": {"today": "Heute", "tomorrow": "Morgen", "yesterday": "Gestern"},
        "es": {"today": "Hoy", "tomorrow": "Mañana", "yesterday": "Ayer"},
        "fr": {"today": "Aujourd'hui", "tomorrow": "Demain", "yesterday": "Hier"},
        "it": {"today": "Oggi", "tomorrow": "Domani", "yesterday": "Ieri"},
    }
    return translations.get(language, translations["en"])


def get_chart_translations(language: str) -> Dict:
    """Chart-specific translations."""
    translations = {
        "en": {"data": "Data", "chart": "Chart", "export": "Export"},
        "ar": {"data": "البيانات", "chart": "الرسم البياني", "export": "تصدير"},
        "de": {"data": "Daten", "chart": "Diagramm", "export": "Exportieren"},
        "es": {"data": "Datos", "chart": "Gráfico", "export": "Exportar"},
        "fr": {"data": "Données", "chart": "Graphique", "export": "Exporter"},
        "it": {"data": "Dati", "chart": "Grafico", "export": "Esporta"},
    }
    return translations.get(language, translations["en"])


def get_table_translations(language: str) -> Dict:
    """Table-specific translations."""
    translations = {
        "en": {
            "rows": "Rows",
            "columns": "Columns",
            "sort": "Sort",
            "filter": "Filter",
        },
        "ar": {
            "rows": "الصفوف",
            "columns": "الأعمدة",
            "sort": "ترتيب",
            "filter": "تصفية",
        },
        "de": {
            "rows": "Zeilen",
            "columns": "Spalten",
            "sort": "Sortieren",
            "filter": "Filter",
        },
        "es": {
            "rows": "Filas",
            "columns": "Columnas",
            "sort": "Ordenar",
            "filter": "Filtro",
        },
        "fr": {
            "rows": "Lignes",
            "columns": "Colonnes",
            "sort": "Trier",
            "filter": "Filtre",
        },
        "it": {
            "rows": "Righe",
            "columns": "Colonne",
            "sort": "Ordina",
            "filter": "Filtro",
        },
    }
    return translations.get(language, translations["en"])


def get_mobile_translations(language: str) -> Dict:
    """Mobile-specific translations."""
    translations = {
        "en": {"swipe": "Swipe", "tap": "Tap", "pinch": "Pinch to zoom"},
        "ar": {"swipe": "اسحب", "tap": "اضغط", "pinch": "اقرص للتكبير"},
        "de": {"swipe": "Wischen", "tap": "Tippen", "pinch": "Zum Zoomen kneifen"},
        "es": {"swipe": "Deslizar", "tap": "Tocar", "pinch": "Pellizcar para zoom"},
        "fr": {"swipe": "Glisser", "tap": "Appuyer", "pinch": "Pincer pour zoomer"},
        "it": {"swipe": "Scorri", "tap": "Tocca", "pinch": "Pizzica per zoom"},
    }
    return translations.get(language, translations["en"])


def get_progress_translations(language: str) -> Dict:
    """Progress-specific translations."""
    translations = {
        "en": {
            "complete": "Complete",
            "incomplete": "Incomplete",
            "percent": "Percent",
        },
        "ar": {"complete": "مكتمل", "incomplete": "غير مكتمل", "percent": "نسبة مئوية"},
        "de": {
            "complete": "Vollständig",
            "incomplete": "Unvollständig",
            "percent": "Prozent",
        },
        "es": {
            "complete": "Completo",
            "incomplete": "Incompleto",
            "percent": "Porcentaje",
        },
        "fr": {
            "complete": "Complet",
            "incomplete": "Incomplet",
            "percent": "Pourcentage",
        },
        "it": {
            "complete": "Completo",
            "incomplete": "Incompleto",
            "percent": "Percentuale",
        },
    }
    return translations.get(language, translations["en"])


def get_social_translations(language: str) -> Dict:
    """Social-specific translations."""
    translations = {
        "en": {
            "like": "Like",
            "share": "Share",
            "follow": "Follow",
            "unfollow": "Unfollow",
        },
        "ar": {
            "like": "إعجاب",
            "share": "مشاركة",
            "follow": "متابعة",
            "unfollow": "إلغاء المتابعة",
        },
        "de": {
            "like": "Gefällt mir",
            "share": "Teilen",
            "follow": "Folgen",
            "unfollow": "Entfolgen",
        },
        "es": {
            "like": "Me gusta",
            "share": "Compartir",
            "follow": "Seguir",
            "unfollow": "Dejar de seguir",
        },
        "fr": {
            "like": "J'aime",
            "share": "Partager",
            "follow": "Suivre",
            "unfollow": "Ne plus suivre",
        },
        "it": {
            "like": "Mi piace",
            "share": "Condividi",
            "follow": "Segui",
            "unfollow": "Smetti di seguire",
        },
    }
    return translations.get(language, translations["en"])


if __name__ == "__main__":
    add_component_locales()
    print("🎉 All components now have complete locales for reusability!")
