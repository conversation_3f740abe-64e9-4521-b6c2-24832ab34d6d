#!/bin/bash


set -e

SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
TEST_DIR="$SCRIPT_DIR/test_files"

echo "Creating test directory: $TEST_DIR"
mkdir -p "$TEST_DIR"

create_test_files() {
  cat > "$TEST_DIR/test_python.py" << 'EOF'
import sys, os

def test_function( x,    y ):
    for i in range(10):
      print(i)    
    return {'key1':x,'key2':y,
    'key3':x+y}

class TestClass:
  def __init__(self,  name):
    self.name=name
  
  def test_method(self,  param1,param2):
      return param1+param2
EOF

  cat > "$TEST_DIR/test_js.js" << 'EOF'
// Test JavaScript file with formatting issues
function testFunction(x,y){
    const obj = {a:1,b:2,c:3};
    if(x>y){
        return obj.a
    }else{
        return obj.b
    }
}

const arrow = (x,y)=>{
    return x+y
}

// Test array with inconsistent spacing
const arr = [1, 2,3,  4,5];
EOF

  cat > "$TEST_DIR/test_json.json" << 'EOF'
{
"name": "test",
  "version":"1.0.0",
 "description": "Test JSON file",
  "dependencies": {
    "react": "^17.0.2",
  "react-dom":"^17.0.2"
  }
}
EOF

  cat > "$TEST_DIR/test_html.html" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Test HTML</title>
</head>
<body>
    <div class="container">
        <h1>Test Heading</h1>
        <p>This is a paragraph with <strong>bold text</strong> and <em>emphasized text</em>.</p>
        <ul>
            <li>Item 1</li>
        <li>Item 2</li>
      <li>Item 3</li>
        </ul>
    </div>
</body>
</html>
EOF

  cat > "$TEST_DIR/test_css.css" << 'EOF'
.container {
  margin:0;
padding: 20px;
    color: 
}

h1 {
font-size: 24px;
  color:
}

p {
  line-height:1.5;
font-size: 16px;
  margin-bottom:10px;
}
EOF

  cat > "$TEST_DIR/test_yaml.yml" << 'EOF'
---
name: test
version: 1.0.0
dependencies:
  - name: dep1
    version: 1.0.0
  - name: dep2
    version: 2.0.0
settings:
  enabled: true
  options:
    - option1
    - option2
  nested:
    key1: value1
    key2: value2
EOF

  cat > "$TEST_DIR/test_shell.sh" << 'EOF'
#!/bin/bash
function test_function(){
local var1=$1
local var2=$2
if [ $var1 -gt $var2 ]; then
echo "Var1 is greater"
else
echo "Var2 is greater or equal"
fi
}

for i in $(seq 1 10); do
  test_function $i 5
done
EOF

  cat > "$TEST_DIR/test_markdown.md" << 'EOF'

This is a paragraph with *italic* and **bold** text.


- Item 1
- Item 2
- Item 3


```python
def hello():
    print("Hello world")
```


| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Value 1  | Value 2  | Value 3  |
| Value 4  | Value 5  | Value 6  |
EOF

  chmod +x "$TEST_DIR/test_shell.sh"

  echo "Created test files in $TEST_DIR:"
  ls -la "$TEST_DIR"
}

create_test_config() {
  cat > "$TEST_DIR/test_config.yml" << 'EOF'

format_python: true
format_js: true
format_jsx: false
format_tsx: false
format_html: true
format_css: true 
format_django_templates: false
format_shell: true
format_yaml: true
format_json: true
format_markdown: true

python_dirs: "./test_files"
js_dirs: "./test_files"
html_dirs: "./test_files"
css_dirs: "./test_files"
shell_dirs: "./test_files"
yaml_dirs: "./test_files"
markdown_dirs: "./test_files"

exclude_patterns: "node_modules/**,env/**,venv/**"

prettier_config: ".prettierrc"
black_config: "pyproject.toml"
isort_config: "pyproject.toml"
EOF

  echo "Created test configuration: $TEST_DIR/test_config.yml"
}

run_formatter() {
  echo "Running formatter on test files..."
  
  cd "$SCRIPT_DIR"
  
  ./code_formatter.sh --config "$TEST_DIR/test_config.yml" --verbose
  
  echo "Formatter run completed"
}

check_results() {
  echo "Checking if files were formatted correctly..."
  
  
  for file in "$TEST_DIR"/*; do
    if [[ -f "$file" ]]; then
      echo "Formatting changes in $(basename "$file"):"
      echo "--------------------------------------------"
      cat "$file"
      echo "--------------------------------------------"
      echo ""
    fi
  done
  
  echo "Format check completed"
}

cleanup() {
  if [ "$1" != "--keep" ]; then
    echo "Cleaning up test files..."
    rm -rf "$TEST_DIR"
    echo "Cleanup completed"
  else
    echo "Keeping test files in $TEST_DIR for inspection"
  fi
}

echo "=== Testing Code Formatter ==="
create_test_files
create_test_config

run_formatter
check_results

cleanup "$1"

echo "=== Test Completed ===" 