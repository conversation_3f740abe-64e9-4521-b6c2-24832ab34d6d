"""
Code Formatter Script

This script formats code files using various formatters (black, prettier, isort, etc.)
Optimized for speed and reliability in git commit workflows.
"""

import os
import sys
import json
import yaml
import time
import argparse
import subprocess
import tempfile
import concurrent.futures
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass


sys.path.append(str(Path(__file__).parent.parent))
try:
    from logger import get_logger
except ImportError:
    class FallbackLogger:
        def __init__(self, script_name, verbose=False):
            self.script_name = script_name
            self.verbose = verbose
            self.stats = {}

        def start_script(self):
            pass

        def end_script(self):
            return 0

        def info(self, message):
            if self.verbose:
                print(message)

        def debug(self, message):
            pass

        def warning(self, message):
            if self.verbose:
                print(f"WARNING: {message}")

        def error(self, message):
            print(f"ERROR: {message}")

        def critical(self, message):
            print(f"CRITICAL: {message}")

        def log_file_action(self, filepath, action, **stats):
            if self.verbose:
                print(f"{action.capitalize()}: {filepath}")
            for key, value in stats.items():
                if key not in self.stats:
                    self.stats[key] = 0
                self.stats[key] += value

        def print_summary(self):
            summary_lines = ["", "Summary:"]
            for key, value in self.stats.items():
                pretty_key = " ".join(word.capitalize() for word in key.split("_"))
                summary_lines.append(f"{pretty_key}: {value}")
            print("\n".join(summary_lines))
            return "\n".join(summary_lines)

    def get_logger(script_name, verbose=False):
        return FallbackLogger(script_name, verbose)


@dataclass
class FormatterConfig:
    """Configuration for code formatters."""
    format_python: bool = True
    format_js: bool = True
    format_jsx: bool = True
    format_tsx: bool = True
    format_html: bool = True
    format_css: bool = True
    format_shell: bool = True
    format_yaml: bool = True
    format_json: bool = True
    format_markdown: bool = False
    format_django_templates: bool = False
    
    python_dirs: List[str] = None
    js_dirs: List[str] = None
    html_dirs: List[str] = None
    css_dirs: List[str] = None
    shell_dirs: List[str] = None
    yaml_dirs: List[str] = None
    markdown_dirs: List[str] = None
    
    html_exclude_patterns: List[str] = None
    prettier_config: Optional[str] = ".prettierrc"
    timeout_per_file: int = 10
    max_file_size_mb: int = 5
    
    def __post_init__(self):
        """Set default values for directory lists."""
        if self.python_dirs is None:
            self.python_dirs = ["./"]
        if self.js_dirs is None:
            self.js_dirs = ["./"]
        if self.html_dirs is None:
            self.html_dirs = ["./"]
        if self.css_dirs is None:
            self.css_dirs = ["./"]
        if self.shell_dirs is None:
            self.shell_dirs = ["./"]
        if self.yaml_dirs is None:
            self.yaml_dirs = ["./"]
        if self.markdown_dirs is None:
            self.markdown_dirs = ["./"]
        if self.html_exclude_patterns is None:
            self.html_exclude_patterns = ["**/templates/**"]


class CodeFormatter:
    """Formats code files using various formatters."""

    def __init__(self, config: Optional[FormatterConfig] = None, verbose: bool = False, dry_run: bool = False):
        """
        Initialize the CodeFormatter.

        Args:
            config: Formatter configuration
            verbose: Whether to show verbose output
            dry_run: Whether to run without making changes
        """
        self.logger = get_logger("format_code", verbose)
        self.logger.start_script()
        
        self.config = config or FormatterConfig()
        self.dry_run = dry_run
        self.verbose = verbose
        self.start_time = time.time()
        self.max_execution_time = 8  
        
        
        self.stats = {
            "python_files_formatted": 0,
            "js_files_formatted": 0,
            "html_files_formatted": 0,
            "css_files_formatted": 0,
            "shell_files_formatted": 0,
            "yaml_files_formatted": 0,
            "json_files_formatted": 0,
            "markdown_files_formatted": 0,
            "total_files_formatted": 0,
            "errors": 0
        }
        
        
        self._formatters_available = {}
        
    def _check_timeout(self) -> bool:
        """Check if we're approaching the time limit."""
        return (time.time() - self.start_time) > self.max_execution_time

    def _is_formatter_available(self, formatter: str) -> bool:
        """Check if a formatter is available and cache the result."""
        if formatter not in self._formatters_available:
            try:
                if formatter == "npx":
                    result = subprocess.run(
                        ["npx", "--version"],
                        capture_output=True,
                        timeout=2,
                        check=True
                    )
                else:
                    result = subprocess.run(
                        [formatter, "--version"],
                        capture_output=True,
                        timeout=2,
                        check=True
                    )
                self._formatters_available[formatter] = True
                self.logger.debug(f"Formatter {formatter} is available")
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
                self._formatters_available[formatter] = False
                self.logger.debug(f"Formatter {formatter} is not available")
        
        return self._formatters_available[formatter]

    def _run_formatter(self, command: List[str], timeout: int = None) -> Tuple[bool, str]:
        """
        Run a formatter command safely.

        Args:
            command: Command to run
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, error_message)
        """
        timeout = timeout or self.config.timeout_per_file
        
        try:
            if self.dry_run:
                
                if "--check" in command or "--diff" in command or "--dry-run" in command:
                    result = subprocess.run(
                        command,
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        check=False
                    )
                    return result.returncode == 0, result.stderr
                else:
                    return True, ""
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                check=True
            )
            return True, ""
        
        except subprocess.TimeoutExpired:
            return False, f"Command timed out after {timeout}s"
        except subprocess.CalledProcessError as e:
            return False, f"Command failed: {e.stderr}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"

    def _should_process_file(self, filepath: str) -> bool:
        """Check if file should be processed."""
        if not os.path.exists(filepath):
            return False
        
        
        try:
            file_size = os.path.getsize(filepath) / (1024 * 1024)  
            if file_size > self.config.max_file_size_mb:
                self.logger.warning(f"Skipping large file {filepath} ({file_size:.1f} MB)")
                return False
        except OSError:
            return False
        
        return True

    def format_python_files(self, files: List[str]) -> int:
        """Format Python files using black and isort."""
        if not self.config.format_python or self._check_timeout():
            return 0

        python_files = [f for f in files if f.endswith('.py') and self._should_process_file(f)]
        if not python_files:
            return 0

        formatted_count = 0

        
        if self._is_formatter_available("black"):
            self.logger.info(f"Formatting {len(python_files)} Python files with black")
            
            for file in python_files:
                if self._check_timeout():
                    break
                
                command = ["black"]
                if self.dry_run:
                    command.append("--check")
                command.append(file)
                
                success, error = self._run_formatter(command)
                if success:
                    if not self.dry_run:
                        self.logger.log_file_action(file, "formatted with black")
                        self.stats["python_files_formatted"] += 1
                        formatted_count += 1
                else:
                    self.logger.error(f"Black formatting failed for {file}: {error}")
                    self.stats["errors"] += 1

        
        if self._is_formatter_available("isort") and not self._check_timeout():
            self.logger.info(f"Sorting imports in {len(python_files)} Python files with isort")
            
            for file in python_files:
                if self._check_timeout():
                    break
                
                command = ["isort"]
                if self.dry_run:
                    command.append("--check-only")
                command.append(file)
                
                success, error = self._run_formatter(command)
                if not success:
                    self.logger.error(f"Isort failed for {file}: {error}")
                    self.stats["errors"] += 1

        return formatted_count

    def format_js_ts_files(self, files: List[str]) -> int:
        """Format JavaScript/TypeScript files using prettier."""
        if self._check_timeout():
            return 0

        js_extensions = {'.js', '.jsx', '.ts', '.tsx', '.json'}
        js_files = []
        
        for file in files:
            ext = Path(file).suffix
            if ext in js_extensions and self._should_process_file(file):
                if (ext == '.js' and self.config.format_js) or \
                   (ext == '.jsx' and self.config.format_jsx) or \
                   (ext in ['.ts', '.tsx'] and self.config.format_tsx) or \
                   (ext == '.json' and self.config.format_json):
                    js_files.append(file)

        if not js_files:
            return 0

        if not self._is_formatter_available("npx"):
            self.logger.error("npx is not available, skipping JS/TS formatting")
            return 0

        formatted_count = 0
        self.logger.info(f"Formatting {len(js_files)} JS/TS files with prettier")

        
        batch_size = 10
        for i in range(0, len(js_files), batch_size):
            if self._check_timeout():
                break
                
            batch = js_files[i:i + batch_size]
            command = ["npx", "prettier"]
            
            if self.config.prettier_config and os.path.exists(self.config.prettier_config):
                command.extend(["--config", self.config.prettier_config])
            
            if self.dry_run:
                command.append("--check")
            else:
                command.append("--write")
            
            command.extend(batch)
            
            success, error = self._run_formatter(command, timeout=20)
            if success:
                for file in batch:
                    if not self.dry_run:
                        self.logger.log_file_action(file, "formatted with prettier")
                        self.stats["js_files_formatted"] += 1
                        formatted_count += 1
            else:
                self.logger.error(f"Prettier formatting failed for batch: {error}")
                self.stats["errors"] += 1

        return formatted_count

    def format_html_files(self, files: List[str]) -> int:
        """Format HTML files using prettier."""
        if not self.config.format_html or self._check_timeout():
            return 0

        html_files = [f for f in files if f.endswith('.html') and self._should_process_file(f)]
        if not html_files:
            return 0

        if not self._is_formatter_available("npx"):
            self.logger.error("npx is not available, skipping HTML formatting")
            return 0

        formatted_count = 0
        self.logger.info(f"Formatting {len(html_files)} HTML files with prettier")

        
        batch_size = 5
        for i in range(0, len(html_files), batch_size):
            if self._check_timeout():
                break
                
            batch = html_files[i:i + batch_size]
            command = ["npx", "prettier"]
            
            if self.config.prettier_config and os.path.exists(self.config.prettier_config):
                command.extend(["--config", self.config.prettier_config])
            
            if self.dry_run:
                command.append("--check")
            else:
                command.append("--write")
            
            command.extend(batch)
            
            success, error = self._run_formatter(command, timeout=15)
            if success:
                for file in batch:
                    if not self.dry_run:
                        self.logger.log_file_action(file, "formatted with prettier")
                        self.stats["html_files_formatted"] += 1
                        formatted_count += 1
            else:
                self.logger.error(f"HTML formatting failed for batch: {error}")
                self.stats["errors"] += 1

        return formatted_count

    def format_css_files(self, files: List[str]) -> int:
        """Format CSS/SCSS files using prettier."""
        if not self.config.format_css or self._check_timeout():
            return 0

        css_extensions = {'.css', '.scss', '.sass', '.less'}
        css_files = [f for f in files if Path(f).suffix in css_extensions and self._should_process_file(f)]
        
        if not css_files:
            return 0

        if not self._is_formatter_available("npx"):
            self.logger.error("npx is not available, skipping CSS formatting")
            return 0

        formatted_count = 0
        self.logger.info(f"Formatting {len(css_files)} CSS files with prettier")

        
        batch_size = 8
        for i in range(0, len(css_files), batch_size):
            if self._check_timeout():
                break
                
            batch = css_files[i:i + batch_size]
            command = ["npx", "prettier"]
            
            if self.config.prettier_config and os.path.exists(self.config.prettier_config):
                command.extend(["--config", self.config.prettier_config])
            
            if self.dry_run:
                command.append("--check")
            else:
                command.append("--write")
            
            command.extend(batch)
            
            success, error = self._run_formatter(command, timeout=15)
            if success:
                for file in batch:
                    if not self.dry_run:
                        self.logger.log_file_action(file, "formatted with prettier")
                        self.stats["css_files_formatted"] += 1
                        formatted_count += 1
            else:
                self.logger.error(f"CSS formatting failed for batch: {error}")
                self.stats["errors"] += 1

        return formatted_count

    def format_shell_files(self, files: List[str]) -> int:
        """Format shell scripts using shfmt."""
        if not self.config.format_shell or self._check_timeout():
            return 0

        shell_files = [f for f in files if f.endswith('.sh') and self._should_process_file(f)]
        if not shell_files:
            return 0

        if not self._is_formatter_available("shfmt"):
            self.logger.warning("shfmt is not available, skipping shell formatting")
            return 0

        formatted_count = 0
        self.logger.info(f"Formatting {len(shell_files)} shell files with shfmt")

        for file in shell_files:
            if self._check_timeout():
                break
            
            command = ["shfmt"]
            if self.dry_run:
                command.extend(["-d", file])
            else:
                command.extend(["-w", file])
            
            success, error = self._run_formatter(command)
            if success:
                if not self.dry_run:
                    self.logger.log_file_action(file, "formatted with shfmt")
                    self.stats["shell_files_formatted"] += 1
                    formatted_count += 1
            else:
                self.logger.error(f"Shfmt formatting failed for {file}: {error}")
                self.stats["errors"] += 1

        return formatted_count

    def format_yaml_files(self, files: List[str]) -> int:
        """Format YAML files using prettier."""
        if not self.config.format_yaml or self._check_timeout():
            return 0

        yaml_extensions = {'.yml', '.yaml'}
        yaml_files = [f for f in files if Path(f).suffix in yaml_extensions and self._should_process_file(f)]
        
        if not yaml_files:
            return 0

        if not self._is_formatter_available("npx"):
            self.logger.warning("npx is not available, skipping YAML formatting")
            return 0

        formatted_count = 0
        self.logger.info(f"Formatting {len(yaml_files)} YAML files with prettier")

        
        batch_size = 10
        for i in range(0, len(yaml_files), batch_size):
            if self._check_timeout():
                break
                
            batch = yaml_files[i:i + batch_size]
            command = ["npx", "prettier"]
            
            if self.config.prettier_config and os.path.exists(self.config.prettier_config):
                command.extend(["--config", self.config.prettier_config])
            
            if self.dry_run:
                command.append("--check")
            else:
                command.append("--write")
            
            command.extend(batch)
            
            success, error = self._run_formatter(command, timeout=10)
            if success:
                for file in batch:
                    if not self.dry_run:
                        self.logger.log_file_action(file, "formatted with prettier")
                        self.stats["yaml_files_formatted"] += 1
                        formatted_count += 1
            else:
                self.logger.error(f"YAML formatting failed for batch: {error}")
                self.stats["errors"] += 1

        return formatted_count

    def format_files(self, files: List[str]) -> int:
        """
        Format the specified files.

        Args:
            files: List of file paths to format

        Returns:
            Total number of files formatted
        """
        if not files:
            self.logger.info("No files to format")
            return 0

        
        valid_files = [f for f in files if self._should_process_file(f)]
        
        if not valid_files:
            self.logger.info("No valid files to format")
            return 0

        self.logger.info(f"Formatting {len(valid_files)} files")

        total_formatted = 0
        
        
        total_formatted += self.format_python_files(valid_files)
        total_formatted += self.format_js_ts_files(valid_files)
        total_formatted += self.format_html_files(valid_files)
        total_formatted += self.format_css_files(valid_files)
        total_formatted += self.format_shell_files(valid_files)
        total_formatted += self.format_yaml_files(valid_files)

        self.stats["total_files_formatted"] = total_formatted
        return total_formatted

    def get_staged_files(self) -> List[str]:
        """Get list of staged files from git."""
        try:
            result = subprocess.run(
                ["git", "diff", "--cached", "--name-only"],
                capture_output=True,
                text=True,
                check=True,
                timeout=5
            )
            files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
            return files
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            self.logger.error(f"Failed to get staged files: {e}")
            return []

    def format_staged_files(self) -> int:
        """Format all staged files."""
        staged_files = self.get_staged_files()
        if not staged_files:
            self.logger.info("No staged files to format")
            return 0
        
        return self.format_files(staged_files)

    def print_summary(self) -> str:
        """Print formatting summary."""
        execution_time = time.time() - self.start_time
        
        summary_lines = [
            "",
            "Code Formatting Summary:",
            f"Python files formatted: {self.stats['python_files_formatted']}",
            f"JavaScript/TypeScript files formatted: {self.stats['js_files_formatted']}",
            f"HTML files formatted: {self.stats['html_files_formatted']}",
            f"CSS files formatted: {self.stats['css_files_formatted']}",
            f"Shell files formatted: {self.stats['shell_files_formatted']}",
            f"YAML files formatted: {self.stats['yaml_files_formatted']}",
            f"Total files formatted: {self.stats['total_files_formatted']}",
            f"Errors encountered: {self.stats['errors']}",
            f"Execution time: {execution_time:.2f}s"
        ]
        
        summary = "\n".join(summary_lines)
        print(summary)
        
        
        for key, value in self.stats.items():
            self.logger.stats[key] = value
        
        self.logger.end_script()
        return summary


def load_config_from_file(config_path: str) -> FormatterConfig:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        return FormatterConfig()
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f) or {}
        
        
        return FormatterConfig(
            format_python=config_data.get('format_python', True),
            format_js=config_data.get('format_js', True),
            format_jsx=config_data.get('format_jsx', True),
            format_tsx=config_data.get('format_tsx', True),
            format_html=config_data.get('format_html', True),
            format_css=config_data.get('format_css', True),
            format_shell=config_data.get('format_shell', True),
            format_yaml=config_data.get('format_yaml', True),
            format_json=config_data.get('format_json', True),
            format_markdown=config_data.get('format_markdown', False),
            format_django_templates=config_data.get('format_django_templates', False),
            
            python_dirs=config_data.get('python_dirs', './').split(',') if isinstance(config_data.get('python_dirs'), str) else config_data.get('python_dirs', ['./'])[0:1],
            js_dirs=config_data.get('js_dirs', './').split(',') if isinstance(config_data.get('js_dirs'), str) else config_data.get('js_dirs', ['./'])[0:1],
            html_dirs=config_data.get('html_dirs', './').split(',') if isinstance(config_data.get('html_dirs'), str) else config_data.get('html_dirs', ['./'])[0:1],
            css_dirs=config_data.get('css_dirs', './').split(',') if isinstance(config_data.get('css_dirs'), str) else config_data.get('css_dirs', ['./'])[0:1],
            shell_dirs=config_data.get('shell_dirs', './').split(',') if isinstance(config_data.get('shell_dirs'), str) else config_data.get('shell_dirs', ['./'])[0:1],
            yaml_dirs=config_data.get('yaml_dirs', './').split(',') if isinstance(config_data.get('yaml_dirs'), str) else config_data.get('yaml_dirs', ['./'])[0:1],
            markdown_dirs=config_data.get('markdown_dirs', './').split(',') if isinstance(config_data.get('markdown_dirs'), str) else config_data.get('markdown_dirs', ['./'])[0:1],
            
            html_exclude_patterns=config_data.get('html_exclude_patterns', ['**/templates/**']),
            prettier_config=config_data.get('prettier_config', '.prettierrc'),
            timeout_per_file=config_data.get('timeout_per_file', 10),
            max_file_size_mb=config_data.get('max_file_size_mb', 5)
        )
    
    except Exception as e:
        print(f"Error loading config from {config_path}: {e}")
        return FormatterConfig()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Format code files using various formatters")
    parser.add_argument("--verbose", "-v", action="store_true", help="Show verbose output")
    parser.add_argument("--dry-run", "-n", action="store_true", help="Run without making changes")
    parser.add_argument("--config", "-c", help="Path to config file")
    parser.add_argument("--files", help="Comma-separated list of files to format")
    parser.add_argument("--staged", "-s", action="store_true", help="Format only staged files")
    parser.add_argument("--quiet", "-q", action="store_true", help="Minimal output")

    args = parser.parse_args()

    
    script_dir = Path(__file__).parent
    config_path = args.config or script_dir / "config.yml"
    config = load_config_from_file(str(config_path))

    
    verbose = args.verbose and not args.quiet
    formatter = CodeFormatter(config=config, verbose=verbose, dry_run=args.dry_run)

    
    if args.files:
        files = [f.strip() for f in args.files.split(',') if f.strip()]
        total_formatted = formatter.format_files(files)
    elif args.staged:
        total_formatted = formatter.format_staged_files()
    else:
        
        total_formatted = formatter.format_staged_files()

    
    if not args.quiet:
        formatter.print_summary()
    
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 