[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["**/migrations/*", "venv/*"]

[tool.ruff]
line-length = 88
select = ["E", "F", "I", "C90"]
ignore = ["E203", "E501"]
exclude = ["migrations", "venv", ".git", "__pycache__"]
fix = true

[tool.ruff.isort]
profile = "black"

[tool.ruff.mccabe]
max-complexity = 10 