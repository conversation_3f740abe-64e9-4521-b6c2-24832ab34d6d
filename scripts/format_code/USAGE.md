# Code Formatter Usage Guide

This guide provides practical examples for using the code formatter in different scenarios.

## Quick Start

Format all files with default configuration:

```bash
./code_formatter.sh
```

## Common Use Cases

### 1. Format Only Python Files

Edit your `config.yml` to enable only Python formatting:

```yaml
format_python: true
format_js: false
format_jsx: false
format_tsx: false
format_html: false
format_css: false
format_django_templates: false
format_shell: false
format_yaml: false
format_json: false
format_markdown: false

python_dirs: "./api,./scripts"
```

Then run:

```bash
./code_formatter.sh
```

### 2. Format Only a Specific Directory

Edit your `config.yml` to target only a specific directory:

```yaml
python_dirs: "./api/core"
js_dirs: "./ui/src/components"
```

Then run:

```bash
./code_formatter.sh
```

### 3. Pre-Commit Check

To check if files would be formatted differently without making changes:

```bash
./code_formatter.sh --dry-run --verbose
```

### 4. Integrate with Git Hooks

Add to your pre-commit hook:

```bash
#!/bin/bash
./scripts/format_code/code_formatter.sh --verbose || exit 1
```

### 5. Format New Files Only

If you want to format only newly added files:

```bash
# Get list of added files
FILES=$(git diff --staged --name-only)

# Create a temporary config with just these files
cat > temp_config.yml << EOF
format_python: true
format_js: true
# ... other formats
python_dirs: "$(dirname $FILES | grep '\.py$' | tr '\n' ',' | sed 's/,$//')"
js_dirs: "$(dirname $FILES | grep '\.js$' | tr '\n' ',' | sed 's/,$//')"
# ... other directories
EOF

# Run formatter with temporary config
./code_formatter.sh --config temp_config.yml
```

## Configuration Examples

### Minimal Configuration

```yaml
format_python: true
format_js: true
format_jsx: true
format_tsx: true
format_html: true
python_dirs: "./api"
js_dirs: "./ui/src"
html_dirs: "./ui/public"
```

### Full Configuration

```yaml
# Format settings
format_python: true
format_js: true
format_jsx: true
format_tsx: true
format_html: true
format_css: true 
format_django_templates: true
format_shell: true
format_yaml: true
format_json: true
format_markdown: true

# Directories
python_dirs: "./api,./scripts"
js_dirs: "./ui/src"
html_dirs: "./ui/public,./api/templates"
css_dirs: "./ui/src"
shell_dirs: "./scripts"
yaml_dirs: "./"
markdown_dirs: "./docs,."

# Exclusions
exclude_patterns: "node_modules/**,env/**,venv/**,**/migrations/**"
html_exclude_patterns: "**/node_modules/**"

# Config files
prettier_config: "./ui/.prettierrc"
black_config: "pyproject.toml"
isort_config: "pyproject.toml"
eslint_config: "./ui/.eslintrc.js"
djlint_config: ".djlintrc"
```

## Testing Your Configuration

Run with dry-run and verbose options to test your configuration:

```bash
./code_formatter.sh --dry-run --verbose
```

## Using with CI/CD

Example GitHub Action workflow:

```yaml
name: Format Code

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
          
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black isort ruff
          npm install -g prettier
          
      - name: Run formatter
        run: |
          chmod +x ./scripts/format_code/code_formatter.sh
          ./scripts/format_code/code_formatter.sh --verbose
          
      - name: Check for changes
        run: |
          if git diff --quiet; then
            echo "No formatting changes required"
          else
            echo "Formatting changes required!"
            git diff
            exit 1
          fi
```

## Troubleshooting

### Missing Formatters

If you see errors about missing formatters, install them:

```bash
# Python formatters
pip install black isort ruff

# JavaScript/TypeScript formatters
npm install -g prettier eslint

# HTML formatters
pip install djlint
npm install -g prettier

# Shell formatters
# On macOS with Homebrew:
brew install shfmt shellcheck

# YAML formatters
pip install yamllint
```

### Configuration Loading Issues

If your configuration isn't being loaded correctly, use the `--verbose` flag to see what's being loaded:

```bash
./code_formatter.sh --verbose
```

### Permissions Issues

If you can't execute the script:

```bash
chmod +x code_formatter.sh
```

## Advanced Usage

### Customizing Formatter Options

Many formatters support custom options in their configuration files:

- **Black**: `pyproject.toml`
- **isort**: `pyproject.toml`
- **Prettier**: `.prettierrc`
- **djLint**: `.djlintrc`

See the README for details on configuration options for each formatter. 