# Code Formatter

A comprehensive shell script for automatically formatting code across multiple languages with a consistent style. This tool applies language-specific formatters to ensure code follows project standards.

## Features

- **Multi-Language Support**: Formats a wide variety of file types:
  - Python: Black & isort
  - JavaScript/TypeScript: Prettier & ESLint
  - JSON: Prettier
  - HTML: Prettier & djLint (for Django templates)
  - CSS/SCSS: Prettier
  - Shell: shfmt
  - YAML: Prettier & yamllint
  - Markdown: Prettier
- **Configurable**: Extensive YAML configuration for customizing behavior per language
- **Detailed Logging**: Comprehensive logs with file-level tracking
- **Dry Run Mode**: Test formatting changes without modifying files
- **Selective Formatting**: Format only specific languages or directories
- **Robust Error Handling**: Graceful handling of missing formatters and errors

## Installation

The script relies on external formatters that should be installed either system-wide or in a virtual environment:

### Prerequisites

- **Python formatters**: `pip install black isort ruff`
- **JavaScript/TypeScript formatters**: `npm install -g prettier eslint`
- **HTML formatters**: `pip install djlint` and `npm install -g prettier`
- **CSS formatters**: `npm install -g prettier`
- **Shell formatters**: Install `shfmt` and `shellcheck` for your platform
- **YAML formatters**: `pip install yamllint` and prettier
- **Markdown formatters**: `npm install -g prettier`

You can use our `install_formatters.py` script to set up all formatters automatically.

## Usage

### Basic Usage

```bash
# Format all supported files in current directory with default configuration
./code_formatter.sh

# Run with verbose output
./code_formatter.sh --verbose

# Dry run (show what would be formatted without making changes)
./code_formatter.sh --dry-run

# Use a custom configuration file
./code_formatter.sh --config /path/to/custom/config.yml
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--verbose`, `-v` | Show detailed output during formatting |
| `--dry-run`, `-n` | Run without making changes to files |
| `--config`, `-c` | Specify path to custom configuration file |
| `--help`, `-h` | Show help information |

## Configuration

The formatter is configured through a YAML file (`config.yml`) that defines which languages to format and how:

```yaml
# Format settings (true/false)
format_python: true
format_js: true
format_jsx: true
format_tsx: true
format_html: true
format_css: true 
format_django_templates: true
format_shell: true
format_yaml: true
format_json: true
format_markdown: true

# Directories to format (comma-separated paths)
python_dirs: "./api,./scripts"
js_dirs: "./ui/src"
html_dirs: "./ui/public,./api/templates"
css_dirs: "./ui/src"
shell_dirs: "./scripts"
yaml_dirs: "./"
markdown_dirs: "./docs,."

# Exclusion patterns
exclude_patterns: "node_modules/**,env/**,venv/**,**/migrations/**"
html_exclude_patterns: "**/node_modules/**"

# Formatter config files
prettier_config: "./ui/.prettierrc"
black_config: "pyproject.toml"
```

### Key Configuration Options

| Option | Description |
|--------|-------------|
| `format_*` | Enable/disable formatting for specific languages |
| `*_dirs` | Comma-separated list of directories to process for each language |
| `exclude_patterns` | Glob patterns for files to exclude from formatting |
| `*_config` | Path to formatter-specific configuration files |
| `*_options` | Language-specific formatting options |

## Example Configurations

### Python-Only Formatting

```yaml
format_python: true
format_js: false
format_jsx: false
format_tsx: false
format_html: false
format_django_templates: false
format_shell: false
format_yaml: false
format_json: false
format_markdown: false

python_dirs: "./api,./scripts"
```

### Frontend-Only Formatting

```yaml
format_python: false
format_js: true
format_jsx: true
format_tsx: true
format_html: true
format_css: true
format_django_templates: false
format_shell: false
format_yaml: false
format_json: true
format_markdown: false

js_dirs: "./ui/src"
html_dirs: "./ui/public"
css_dirs: "./ui/src"
```

## Formatter-Specific Configurations

### Black (Python)

Black uses `pyproject.toml` for configuration:

```toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''
```

### isort (Python)

isort also uses `pyproject.toml` for configuration:

```toml
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
```

### Prettier (JavaScript/TypeScript/JSON/HTML/CSS/YAML/Markdown)

Prettier uses `.prettierrc` for configuration:

```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 100,
  "trailingComma": "es5"
}
```

### djLint (Django Templates)

djLint uses `.djlintrc` for configuration:

```json
{
  "profile": "django",
  "indent": 2,
  "max_line_length": 120,
  "preserve_blank_lines": true
}
```

### shfmt (Shell)

shfmt can be configured via command-line options in the script:

```
-i 2 (indent with 2 spaces)
-bn (binary ops start on new line)
-ci (indent cases)
```

## Integration with Other Scripts

This formatter works as part of a larger script ecosystem:

1. Can be run as a pre-commit hook via the commit script
2. Is integrated into the main `run_all_checks.py` script 
3. Logs all activities to the central logging system
4. Can be called by CI/CD pipelines for automated formatting

## Workflow Patterns

### Pre-Commit Formatting

```bash
# In pre-commit hook
./format_code/code_formatter.sh --verbose
```

### Selective Formatting Before Submission

Edit the configuration to only format the languages and directories relevant to your changes:

```yaml
format_python: true  # Only enable relevant languages
format_js: false
format_jsx: false
# ... other languages disabled

python_dirs: "./api/core"  # Only format specific directories
```

## Troubleshooting

If you encounter any issues:

1. Check if the required formatters are installed with `command -v <formatter-name>`
2. Run with `--verbose` to see detailed output and error messages
3. Verify the config file is properly formatted YAML with correct indentation
4. Make sure the configured directories exist
5. Check log files in the `scripts/logs` directory for error details
6. Ensure proper permissions to modify files in the target directories
7. Try with `--dry-run` flag to test without making changes 