#!/bin/bash


set -e

SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
SCRIPTS_DIR="$(dirname "$SCRIPT_DIR")"

VERBOSE=false
DRY_RUN=false
SCRIPT_NAME="format_code"
LOG_DIR="$SCRIPTS_DIR/logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/${SCRIPT_NAME}_${TIMESTAMP}.log"

mkdir -p "$LOG_DIR"

PYTHON_FILES_FORMATTED=0
JS_FILES_FORMATTED=0
HTML_FILES_FORMATTED=0
CSS_FILES_FORMATTED=0
SHELL_FILES_FORMATTED=0
YAML_FILES_FORMATTED=0
JSON_FILES_FORMATTED=0
MARKDOWN_FILES_FORMATTED=0
TOTAL_FILES_FORMATTED=0
ERRORS=0

log() {
  local level=$1
  local message=$2
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
  
  if [ "$VERBOSE" = true ] || [ "$level" = "ERROR" ]; then
    echo "[$level] $message"
  fi
}

log_start() {
  log "INFO" "Starting code formatter script"
  log "INFO" "Working directory: $(pwd)"
  log "INFO" "Command arguments: $*"
  START_TIME=$(date +%s)
}

log_end() {
  END_TIME=$(date +%s)
  DURATION=$((END_TIME - START_TIME))
  log "INFO" "Script completed in ${DURATION} seconds"
  
  echo
  echo "Summary:"
  echo "Python files formatted: $PYTHON_FILES_FORMATTED"
  echo "JavaScript/TypeScript files formatted: $JS_FILES_FORMATTED"
  echo "HTML files formatted: $HTML_FILES_FORMATTED"
  echo "CSS files formatted: $CSS_FILES_FORMATTED"
  echo "Shell files formatted: $SHELL_FILES_FORMATTED"
  echo "YAML files formatted: $YAML_FILES_FORMATTED"
  echo "JSON files formatted: $JSON_FILES_FORMATTED"
  echo "Markdown files formatted: $MARKDOWN_FILES_FORMATTED"
  echo "Total files formatted: $TOTAL_FILES_FORMATTED"
  echo "Errors encountered: $ERRORS"
  
  log "INFO" "Summary:"
  log "INFO" "Python files formatted: $PYTHON_FILES_FORMATTED"
  log "INFO" "JavaScript/TypeScript files formatted: $JS_FILES_FORMATTED"
  log "INFO" "HTML files formatted: $HTML_FILES_FORMATTED"
  log "INFO" "CSS files formatted: $CSS_FILES_FORMATTED"
  log "INFO" "Shell files formatted: $SHELL_FILES_FORMATTED"
  log "INFO" "YAML files formatted: $YAML_FILES_FORMATTED"
  log "INFO" "JSON files formatted: $JSON_FILES_FORMATTED"
  log "INFO" "Markdown files formatted: $MARKDOWN_FILES_FORMATTED"
  log "INFO" "Total files formatted: $TOTAL_FILES_FORMATTED"
  log "INFO" "Errors encountered: $ERRORS"
}

log_file_action() {
  local file=$1
  local action=$2
  local type=$3
  
  # Use printf to capitalize first letter instead of bash parameter expansion
  local capitalized_action="$(printf '%s' "$action" | awk '{print toupper(substr($0,1,1)) substr($0,2)}')"
  log "INFO" "${capitalized_action}: $file"
  
  case "$type" in
    python)
      ((PYTHON_FILES_FORMATTED++))
      ;;
    js)
      ((JS_FILES_FORMATTED++))
      ;;
    html)
      ((HTML_FILES_FORMATTED++))
      ;;
    css)
      ((CSS_FILES_FORMATTED++))
      ;;
    shell)
      ((SHELL_FILES_FORMATTED++))
      ;;
    yaml)
      ((YAML_FILES_FORMATTED++))
      ;;
    json)
      ((JSON_FILES_FORMATTED++))
      ;;
    markdown)
      ((MARKDOWN_FILES_FORMATTED++))
      ;;
  esac
  
  ((TOTAL_FILES_FORMATTED++))
}

print_header() {
  if [ "$VERBOSE" = true ]; then
    echo "🔧 $1"
  fi
  log "INFO" "$1"
}

print_success() {
  if [ "$VERBOSE" = true ]; then
    echo "✅ $1"
  fi
  log "INFO" "$1"
}

print_error() {
  echo "❌ $1"
  log "ERROR" "$1"
  ((ERRORS++))
}

# Default variables
VERBOSE=false
DRY_RUN=false
CONFIG_FILE=""
FILES=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --verbose|-v)
      VERBOSE=true
      shift
      ;;
    --dry-run|-n)
      DRY_RUN=true
      shift
      ;;
    --config|-c)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --files)
      FILES="$2"
      shift 2
      ;;
    --help|-h)
      echo "Usage: $0 [options]"
      echo
      echo "Options:"
      echo "  --verbose, -v     Show detailed output"
      echo "  --dry-run, -n     Run without making changes"
      echo "  --config, -c      Path to config file"
      echo "  --files           Comma-separated list of files to format (instead of directories)"
      echo "  --help, -h        Show this help"
      echo
      exit 0
      ;;
    *)
      shift
      ;;
  esac
done

CONFIG_FILE=${CONFIG_FILE:-"$SCRIPT_DIR/config.yml"}

if [ -f "$CONFIG_FILE" ]; then
  log "INFO" "Loading configuration from $CONFIG_FILE"
  
  FORMAT_PYTHON=$(grep "format_python:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_JS=$(grep "format_js:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_JSX=$(grep "format_jsx:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_TSX=$(grep "format_tsx:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_HTML=$(grep "format_html:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_CSS=$(grep "format_css:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_DJANGO_TEMPLATES=$(grep "format_django_templates:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "false")
  FORMAT_SHELL=$(grep "format_shell:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_YAML=$(grep "format_yaml:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_JSON=$(grep "format_json:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "true")
  FORMAT_MARKDOWN=$(grep "format_markdown:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "false")
  
  PYTHON_DIRS=$(grep "python_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  JS_DIRS=$(grep "js_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  HTML_DIRS=$(grep "html_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  CSS_DIRS=$(grep "css_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  SHELL_DIRS=$(grep "shell_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  YAML_DIRS=$(grep "yaml_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  MARKDOWN_DIRS=$(grep "markdown_dirs:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "./")
  
  HTML_EXCLUDE_PATTERNS=$(grep "html_exclude_patterns:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo "**/templates/**")
  PRETTIER_CONFIG=$(grep "prettier_config:" "$CONFIG_FILE" | cut -d ':' -f2 | tr -d ' ' || echo ".prettierrc")
else
  log "WARNING" "Config file not found at $CONFIG_FILE, using default settings"
  
  FORMAT_PYTHON=true
  FORMAT_JS=true
  FORMAT_JSX=true
  FORMAT_TSX=true
  FORMAT_HTML=true
  FORMAT_CSS=true
  FORMAT_DJANGO_TEMPLATES=false
  FORMAT_SHELL=true
  FORMAT_YAML=true
  FORMAT_JSON=true
  FORMAT_MARKDOWN=false
  
  PYTHON_DIRS="./"
  JS_DIRS="./"
  HTML_DIRS="./"
  CSS_DIRS="./"
  SHELL_DIRS="./"
  YAML_DIRS="./"
  MARKDOWN_DIRS="./"
  
  HTML_EXCLUDE_PATTERNS="**/templates/**"
  PRETTIER_CONFIG=".prettierrc"
fi

log_start "$@"

log "INFO" "Configuration:"
log "INFO" "Format Python: $FORMAT_PYTHON"
log "INFO" "Format JS: $FORMAT_JS"
log "INFO" "Format JSX: $FORMAT_JSX"
log "INFO" "Format TSX: $FORMAT_TSX"
log "INFO" "Format HTML: $FORMAT_HTML"
log "INFO" "Format CSS: $FORMAT_CSS"
log "INFO" "Format Django Templates: $FORMAT_DJANGO_TEMPLATES"
log "INFO" "Format Shell: $FORMAT_SHELL"
log "INFO" "Format YAML: $FORMAT_YAML"
log "INFO" "Format JSON: $FORMAT_JSON"
log "INFO" "Format Markdown: $FORMAT_MARKDOWN"

if [ "$FORMAT_PYTHON" = "true" ]; then
  print_header "Formatting Python files"
  
  if [ -n "$FILES" ]; then
    # Format only the specified files
    python_files=()
    js_files=()
    IFS=',' read -ra ALL_FILES <<< "$FILES"
    for file in "${ALL_FILES[@]}"; do
      # Trim leading/trailing whitespace
      file=$(echo "$file" | xargs)
      # Skip if file doesn't exist
      if [[ ! -f "$file" ]]; then
        log "WARNING" "File not found: $file"
        continue
      fi
      
      if [[ "$file" == *.py ]]; then
        python_files+=("$file")
      elif [[ "$file" == *.js || "$file" == *.jsx || "$file" == *.ts || "$file" == *.tsx || "$file" == *.json ]]; then
        js_files+=("$file")
      fi
    done
    
    if [[ ${#python_files[@]} -gt 0 ]]; then
      if command -v black &> /dev/null; then
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format ${#python_files[@]} Python files using black (dry run)"
          if [ "$VERBOSE" = true ]; then
            black --check "${python_files[@]}" || true
          else
            black --check "${python_files[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting ${#python_files[@]} Python files using black"
          for file in "${python_files[@]}"; do
            if [ -f "$file" ]; then
              if [ "$VERBOSE" = true ]; then
                black "$file" || true
                log_file_action "$file" "formatted with black" "python"
              else
                black "$file" &> /dev/null || true
                log_file_action "$file" "formatted with black" "python"
              fi
            else
              log "WARNING" "File not found: $file"
            fi
          done
        fi
      else
        print_error "black is not installed, skipping Python formatting with black"
      fi
      
      # Sort imports if isort is available
      if command -v isort &> /dev/null; then
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would sort imports in ${#python_files[@]} Python files using isort (dry run)"
          if [ "$VERBOSE" = true ]; then
            isort --check-only "${python_files[@]}" || true
          else
            isort --check-only "${python_files[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Sorting imports in ${#python_files[@]} Python files using isort"
          if [ "$VERBOSE" = true ]; then
            isort "${python_files[@]}" || true
          else
            isort "${python_files[@]}" &> /dev/null || true
          fi
        fi
      else
        print_error "isort is not installed, skipping Python import sorting"
      fi
    else
      log "INFO" "No Python files to format in the provided file list"
    fi
  else
    # Use the directory-based approach when no files are specified
    IFS=',' read -ra DIRS <<< "$PYTHON_DIRS"
    for dir in "${DIRS[@]}"; do
      if [ -d "$dir" ]; then
        if command -v black &> /dev/null; then
          if [ "$DRY_RUN" = true ]; then
            log "INFO" "Would format Python files in $dir using black (dry run)"
            if [ "$VERBOSE" = true ]; then
              black --check "$dir" || true
            else
              black --check "$dir" &> /dev/null || true
            fi
          else
            log "INFO" "Formatting Python files in $dir using black"
            if [ "$VERBOSE" = true ]; then
              python_files_count=$(find "$dir" -name "*.py" | wc -l)
              black "$dir" || true
              log_file_action "$dir" "formatted with black" "python"
              log "INFO" "Formatted approximately $python_files_count Python files with black"
            else
              python_files=$(find "$dir" -name "*.py")
              if [ -n "$python_files" ]; then
                black "$dir" &> /dev/null || true
                for file in $python_files; do
                  log_file_action "$file" "formatted with black" "python"
                done
              fi
            fi
          fi
        else
          print_error "black is not installed, skipping Python formatting with black"
        fi
        
        if command -v isort &> /dev/null; then
          if [ "$DRY_RUN" = true ]; then
            log "INFO" "Would sort imports in $dir using isort (dry run)"
            if [ "$VERBOSE" = true ]; then
              isort --check-only "$dir" || true
            else
              isort --check-only "$dir" &> /dev/null || true
            fi
          else
            log "INFO" "Sorting imports in $dir using isort"
            if [ "$VERBOSE" = true ]; then
              isort "$dir" || true
              log "INFO" "Sorted imports in Python files"
            else
              isort "$dir" &> /dev/null || true
            fi
          fi
        else
          print_error "isort is not installed, skipping Python import sorting"
        fi
      else
        log "WARNING" "Directory $dir does not exist, skipping"
      fi
    done
  fi
  
  print_success "Python formatting complete"
fi

if [ "$FORMAT_JS" = "true" ] || [ "$FORMAT_JSX" = "true" ] || [ "$FORMAT_TSX" = "true" ] || [ "$FORMAT_JSON" = "true" ]; then
  print_header "Formatting JavaScript, TypeScript, and JSON files"
  
  if [ -n "$FILES" ]; then
    # Format only the specified files
    js_files=()
    IFS=',' read -ra ALL_FILES <<< "$FILES"
    for file in "${ALL_FILES[@]}"; do
      if [[ "$file" == *.js || "$file" == *.mjs || "$file" == *.cjs ]] && [ "$FORMAT_JS" = "true" ]; then
        js_files+=("$file")
      elif [[ "$file" == *.jsx ]] && [ "$FORMAT_JSX" = "true" ]; then
        js_files+=("$file")
      elif [[ "$file" == *.ts || "$file" == *.tsx ]] && [ "$FORMAT_TSX" = "true" ]; then
        js_files+=("$file")
      elif [[ "$file" == *.json ]] && [ "$FORMAT_JSON" = "true" ]; then
        js_files+=("$file")
      fi
    done
    
    if [ ${#js_files[@]} -gt 0 ]; then
      if command -v npx &> /dev/null; then
        prettier_args=()
        
        if [ -f "$PRETTIER_CONFIG" ]; then
          prettier_args+=("--config" "$PRETTIER_CONFIG")
        fi
        
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format ${#js_files[@]} JS/TS/JSON files using prettier (dry run)"
          if [ "$VERBOSE" = true ]; then
            npx prettier "${prettier_args[@]}" --check "${js_files[@]}" || true
          else
            npx prettier "${prettier_args[@]}" --check "${js_files[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting ${#js_files[@]} JS/TS/JSON files using prettier"
          
          for file in "${js_files[@]}"; do
            if [ -f "$file" ]; then
              if [ "$VERBOSE" = true ]; then
                npx prettier "${prettier_args[@]}" --write "$file" || true
              else
                npx prettier "${prettier_args[@]}" --write "$file" &> /dev/null || true
              fi
              
              if [[ "$file" == *.js || "$file" == *.mjs || "$file" == *.cjs ]]; then
                log_file_action "$file" "formatted with prettier" "js"
              elif [[ "$file" == *.jsx ]]; then
                log_file_action "$file" "formatted with prettier" "jsx"
              elif [[ "$file" == *.ts ]]; then
                log_file_action "$file" "formatted with prettier" "typescript"
              elif [[ "$file" == *.tsx ]]; then
                log_file_action "$file" "formatted with prettier" "tsx"
              elif [[ "$file" == *.json ]]; then
                log_file_action "$file" "formatted with prettier" "json"
              fi
            else
              log "WARNING" "File not found: $file"
            fi
          done
        fi
      else
        print_error "npx is not installed, skipping JS/TS/JSON formatting with prettier"
      fi
    else
      log "INFO" "No JS/TS/JSON files to format in the provided file list"
    fi
  else
    # Use the directory-based approach when no files are specified
    IFS=',' read -ra DIRS <<< "$JS_DIRS"
    for dir in "${DIRS[@]}"; do
      if [ -d "$dir" ]; then
        if command -v npx &> /dev/null; then
          prettier_args=()
          
          if [ -f "$PRETTIER_CONFIG" ]; then
            prettier_args+=("--config" "$PRETTIER_CONFIG")
          fi
          
          patterns=()
          if [ "$FORMAT_JS" = "true" ]; then
            patterns+=("**/*.js" "**/*.mjs" "**/*.cjs")
          fi
          if [ "$FORMAT_JSX" = "true" ]; then
            patterns+=("**/*.jsx")
          fi
          if [ "$FORMAT_TSX" = "true" ]; then
            patterns+=("**/*.ts" "**/*.tsx")
          fi
          if [ "$FORMAT_JSON" = "true" ]; then
            patterns+=("**/*.json")
          fi
          
          if [ "${#patterns[@]}" -gt 0 ]; then
            pushd "$dir" > /dev/null
            
            if [ "$DRY_RUN" = true ]; then
              log "INFO" "Would format JS/TS/JSON files in $dir using prettier (dry run)"
              if [ "$VERBOSE" = true ]; then
                npx prettier "${prettier_args[@]}" --check "${patterns[@]}" || true
              else
                npx prettier "${prettier_args[@]}" --check "${patterns[@]}" &> /dev/null || true
              fi
            else
              log "INFO" "Formatting JS/TS/JSON files in $dir using prettier"
              if [ "$VERBOSE" = true ]; then
                js_files_count=$(find . -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -o -name "*.json" \) | wc -l)
                npx prettier "${prettier_args[@]}" --write "${patterns[@]}" || true
                log "INFO" "Formatted approximately $js_files_count JS/TS/JSON files with prettier"
              else
                npx prettier "${prettier_args[@]}" --write "${patterns[@]}" &> /dev/null || true
                # Log each formatted file type
                for pattern in "${patterns[@]}"; do
                  files=$(find . -name "${pattern#*/}")
                  for file in $files; do
                    if [[ "$file" == *.js ]]; then
                      log_file_action "$file" "formatted with prettier" "js"
                    elif [[ "$file" == *.jsx ]]; then
                      log_file_action "$file" "formatted with prettier" "jsx"
                    elif [[ "$file" == *.ts ]]; then
                      log_file_action "$file" "formatted with prettier" "typescript"
                    elif [[ "$file" == *.tsx ]]; then
                      log_file_action "$file" "formatted with prettier" "tsx"
                    elif [[ "$file" == *.json ]]; then
                      log_file_action "$file" "formatted with prettier" "json"
                    fi
                  done
                done
              fi
            fi
            
            popd > /dev/null
          fi
        else
          print_error "npx is not installed, skipping JS/TS/JSON formatting with prettier"
        fi
      else
        log "WARNING" "Directory $dir does not exist, skipping"
      fi
    done
  fi
  
  print_success "JavaScript, TypeScript, and JSON formatting complete"
fi

if [ "$FORMAT_HTML" = "true" ]; then
  print_header "Formatting HTML files"
  IFS=',' read -ra DIRS <<< "$HTML_DIRS"
  for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
      if command -v npx &> /dev/null; then
        prettier_args=()
        
        if [ -f "$PRETTIER_CONFIG" ]; then
          prettier_args+=("--config" "$PRETTIER_CONFIG")
        fi
        
        patterns=("**/*.html")
        
        if [ "$FORMAT_DJANGO_TEMPLATES" = "true" ] && [ -n "$HTML_EXCLUDE_PATTERNS" ]; then
          prettier_args+=("--ignore-path" "$HTML_EXCLUDE_PATTERNS")
        fi
        
        pushd "$dir" > /dev/null
        
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format HTML files in $dir using prettier (dry run)"
          if [ "$VERBOSE" = true ]; then
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" || true
          else
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting HTML files in $dir using prettier"
          if [ "$VERBOSE" = true ]; then
            html_files_count=$(find . -type f -name "*.html" | wc -l)
            npx prettier "${prettier_args[@]}" --write "${patterns[@]}" || true
            log "INFO" "Formatted approximately $html_files_count HTML files with prettier"
          else
            html_files=$(find . -type f -name "*.html")
            if [ -n "$html_files" ]; then
              npx prettier "${prettier_args[@]}" --write "${patterns[@]}" &> /dev/null || true
              for file in $html_files; do
                log_file_action "$dir/$file" "formatted with prettier" "html"
              done
            fi
          fi
        fi
        
        popd > /dev/null
      else
        print_error "npx/prettier is not installed, skipping HTML formatting with prettier"
      fi
      
      if [ "$FORMAT_DJANGO_TEMPLATES" = "true" ]; then
        if command -v djlint &> /dev/null; then
          django_template_dirs="$dir/**/templates"
          
          if [ "$DRY_RUN" = true ]; then
            log "INFO" "Would format Django templates in $django_template_dirs using djlint (dry run)"
            if [ "$VERBOSE" = true ]; then
              djlint --check $django_template_dirs || true
            else
              djlint --check $django_template_dirs &> /dev/null || true
            fi
          else
            log "INFO" "Formatting Django templates in $django_template_dirs using djlint"
            if [ "$VERBOSE" = true ]; then
              template_files_count=$(find $django_template_dirs -type f -name "*.html" | wc -l)
              djlint --reformat $django_template_dirs || true
              log "INFO" "Formatted approximately $template_files_count Django templates with djlint"
            else
              template_files=$(find $django_template_dirs -type f -name "*.html")
              if [ -n "$template_files" ]; then
                djlint --reformat $django_template_dirs &> /dev/null || true
                for file in $template_files; do
                  log_file_action "$file" "formatted with djlint" "html"
                done
              fi
            fi
          fi
        else
          print_error "djlint is not installed, skipping Django template formatting"
        fi
      fi
    else
      log "WARNING" "Directory $dir does not exist, skipping"
    fi
  done
  print_success "HTML formatting complete"
fi

if [ "$FORMAT_CSS" = "true" ]; then
  print_header "Formatting CSS/SCSS files"
  IFS=',' read -ra DIRS <<< "$CSS_DIRS"
  for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
      if command -v npx &> /dev/null; then
        prettier_args=()
        
        if [ -f "$PRETTIER_CONFIG" ]; then
          prettier_args+=("--config" "$PRETTIER_CONFIG")
        fi
        
        patterns=("**/*.css" "**/*.scss" "**/*.sass" "**/*.less")
        
        pushd "$dir" > /dev/null
        
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format CSS files in $dir using prettier (dry run)"
          if [ "$VERBOSE" = true ]; then
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" || true
          else
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting CSS files in $dir using prettier"
          if [ "$VERBOSE" = true ]; then
            css_files_count=$(find . -type f \( -name "*.css" -o -name "*.scss" -o -name "*.sass" -o -name "*.less" \) | wc -l)
            npx prettier "${prettier_args[@]}" --write "${patterns[@]}" || true
            log "INFO" "Formatted approximately $css_files_count CSS files"
          else
            css_files=$(find . -type f \( -name "*.css" -o -name "*.scss" -o -name "*.sass" -o -name "*.less" \))
            if [ -n "$css_files" ]; then
              npx prettier "${prettier_args[@]}" --write "${patterns[@]}" &> /dev/null || true
              for file in $css_files; do
                log_file_action "$dir/$file" "formatted" "css"
              done
            fi
          fi
        fi
        
        popd > /dev/null
      else
        print_error "npx/prettier is not installed, skipping CSS formatting"
      fi
    else
      log "WARNING" "Directory $dir does not exist, skipping"
    fi
  done
  print_success "CSS formatting complete"
fi

if [ "$FORMAT_SHELL" = "true" ]; then
  print_header "Formatting shell scripts"
  IFS=',' read -ra DIRS <<< "$SHELL_DIRS"
  for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
      if command -v shfmt &> /dev/null; then
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format shell scripts in $dir using shfmt (dry run)"
          if [ "$VERBOSE" = true ]; then
            find "$dir" -type f \( -name "*.sh" -o -path "*/bin/*" \) -exec shfmt -d {} \; || true
          else
            find "$dir" -type f \( -name "*.sh" -o -path "*/bin/*" \) -exec shfmt -d {} \; &> /dev/null || true
          fi
        else
          log "INFO" "Formatting shell scripts in $dir using shfmt"
          shell_files=$(find "$dir" -type f \( -name "*.sh" -o -path "*/bin/*" -not -path "*/node_modules/*" \))
          if [ -n "$shell_files" ]; then
            if [ "$VERBOSE" = true ]; then
              shell_files_count=$(echo "$shell_files" | wc -l)
              find "$dir" -type f \( -name "*.sh" -o -path "*/bin/*" \) -exec shfmt -w {} \; || true
              log "INFO" "Formatted approximately $shell_files_count shell scripts"
            else
              find "$dir" -type f \( -name "*.sh" -o -path "*/bin/*" \) -exec shfmt -w {} \; &> /dev/null || true
              for file in $shell_files; do
                log_file_action "$file" "formatted" "shell"
              done
            fi
          fi
        fi
      else
        print_error "shfmt is not installed, skipping shell script formatting"
      fi
    else
      log "WARNING" "Directory $dir does not exist, skipping"
    fi
  done
  print_success "Shell script formatting complete"
fi

if [ "$FORMAT_YAML" = "true" ]; then
  print_header "Formatting YAML files"
  IFS=',' read -ra DIRS <<< "$YAML_DIRS"
  for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
      if command -v npx &> /dev/null; then
        prettier_args=()
        
        if [ -f "$PRETTIER_CONFIG" ]; then
          prettier_args+=("--config" "$PRETTIER_CONFIG")
        fi
        
        patterns=("**/*.yml" "**/*.yaml")
        
        pushd "$dir" > /dev/null
        
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format YAML files in $dir using prettier (dry run)"
          if [ "$VERBOSE" = true ]; then
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" || true
          else
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting YAML files in $dir using prettier"
          if [ "$VERBOSE" = true ]; then
            yaml_files_count=$(find . -type f \( -name "*.yml" -o -name "*.yaml" \) | wc -l)
            npx prettier "${prettier_args[@]}" --write "${patterns[@]}" || true
            log "INFO" "Formatted approximately $yaml_files_count YAML files"
          else
            yaml_files=$(find . -type f \( -name "*.yml" -o -name "*.yaml" \))
            if [ -n "$yaml_files" ]; then
              npx prettier "${prettier_args[@]}" --write "${patterns[@]}" &> /dev/null || true
              for file in $yaml_files; do
                log_file_action "$dir/$file" "formatted" "yaml"
              done
            fi
          fi
        fi
        
        popd > /dev/null
      elif command -v yamllint &> /dev/null; then
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would check YAML files in $dir using yamllint (dry run)"
          if [ "$VERBOSE" = true ]; then
            yamllint -f parsable "$dir" || true
          else
            yamllint -f parsable "$dir" &> /dev/null || true
          fi
        else
          log "INFO" "Checking YAML files in $dir using yamllint"
          if [ "$VERBOSE" = true ]; then
            yaml_files_count=$(find "$dir" -type f \( -name "*.yml" -o -name "*.yaml" \) | wc -l)
            yamllint -f parsable "$dir" || true
            log "INFO" "Checked approximately $yaml_files_count YAML files"
          else
            yaml_files=$(find "$dir" -type f \( -name "*.yml" -o -name "*.yaml" \))
            if [ -n "$yaml_files" ]; then
              yamllint -f parsable "$dir" &> /dev/null || true
              for file in $yaml_files; do
                log_file_action "$file" "checked" "yaml"
              done
            fi
          fi
        fi
      else
        print_error "Neither prettier nor yamllint is installed, skipping YAML formatting"
      fi
    else
      log "WARNING" "Directory $dir does not exist, skipping"
    fi
  done
  print_success "YAML formatting complete"
fi

if [ "$FORMAT_MARKDOWN" = "true" ]; then
  print_header "Formatting Markdown files"
  IFS=',' read -ra DIRS <<< "$MARKDOWN_DIRS"
  for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
      if command -v npx &> /dev/null; then
        prettier_args=()
        
        if [ -f "$PRETTIER_CONFIG" ]; then
          prettier_args+=("--config" "$PRETTIER_CONFIG")
        fi
        
        patterns=("**/*.md" "**/*.markdown")
        
        pushd "$dir" > /dev/null
        
        if [ "$DRY_RUN" = true ]; then
          log "INFO" "Would format Markdown files in $dir using prettier (dry run)"
          if [ "$VERBOSE" = true ]; then
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" || true
          else
            npx prettier "${prettier_args[@]}" --check "${patterns[@]}" &> /dev/null || true
          fi
        else
          log "INFO" "Formatting Markdown files in $dir using prettier"
          if [ "$VERBOSE" = true ]; then
            md_files_count=$(find . -type f \( -name "*.md" -o -name "*.markdown" \) | wc -l)
            npx prettier "${prettier_args[@]}" --write "${patterns[@]}" || true
            log "INFO" "Formatted approximately $md_files_count Markdown files"
          else
            md_files=$(find . -type f \( -name "*.md" -o -name "*.markdown" \))
            if [ -n "$md_files" ]; then
              npx prettier "${prettier_args[@]}" --write "${patterns[@]}" &> /dev/null || true
              for file in $md_files; do
                log_file_action "$dir/$file" "formatted" "markdown"
              done
            fi
          fi
        fi
        
        popd > /dev/null
      else
        print_error "npx/prettier is not installed, skipping Markdown formatting"
      fi
    else
      log "WARNING" "Directory $dir does not exist, skipping"
    fi
  done
  print_success "Markdown formatting complete"
fi

log_end 