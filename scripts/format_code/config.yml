


format_python: true
format_js: true
format_jsx: true
format_tsx: true
format_html: true
format_css: true 
format_django_templates: true
format_shell: true
format_yaml: true
format_json: true
format_markdown: true


python_dirs: "./api,./scripts"
js_dirs: "./ui/src"
html_dirs: "./ui/public,./api/templates"
css_dirs: "./ui/src"
shell_dirs: "./scripts"
yaml_dirs: "./"
markdown_dirs: "./docs,."


exclude_patterns: "node_modules/**,env/**,venv/**,**/migrations/**"
html_exclude_patterns: "**/node_modules/**"


prettier_config: "./ui/.prettierrc"
black_config: "pyproject.toml"
isort_config: "pyproject.toml"
eslint_config: "./ui/.eslintrc.js"
djlint_config: ".djlintrc"


preserved_comment_patterns:
  - "TODO:"
  - "FIXME:"
  - "NOTE:"
  - "WARNING:"
  - "HACK:"
  - "Django"
  - "License"
  - "Copyright"
  - "@"
  

black_options:
  line_length: 88
  target_version: "py39"
  skip_string_normalization: true
  
isort_options:
  profile: "black"
  line_length: 88
  multi_line_output: 3
  
ruff_options:
  line_length: 88
  select: ["E", "F", "I"]
  ignore: ["E203"]
  

prettier_options:
  single_quote: true
  semi: true
  tab_width: 2
  print_width: 100
  trailing_comma: "es5"
  bracket_spacing: true
  

djlint_options:
  profile: "django"
  preserve_blank_lines: true
  preserve_leading_space: true
  indent: 2
  max_line_length: 120
  

shfmt_options:
  indent: 2
  binary_next_line: true
  case_indent: true
  

yamllint_options:
  relaxed: true
  line_length: 120





format_python: true
format_js: true
format_jsx: true
format_tsx: true
format_html: true
format_css: true
format_shell: true
format_yaml: true
format_json: true
format_markdown: false  
format_django_templates: false  


python_dirs: "./"
js_dirs: "./"
html_dirs: "./"
css_dirs: "./"
shell_dirs: "./"
yaml_dirs: "./"
markdown_dirs: "./"


prettier_config: ".prettierrc"


html_exclude_patterns:
  - "**/templates/**"
  - "**/node_modules/**"
  - "**/dist/**"
  - "**/build/**"


timeout_per_file: 10  
max_file_size_mb: 5   


fast_mode: true       
batch_processing: true  