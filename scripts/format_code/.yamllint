---
# YAM<PERSON> configuration for Django project

extends: relaxed

rules:
  line-length:
    max: 120
    allow-non-breakable-words: true
    allow-non-breakable-inline-mappings: true
  
  comments:
    require-starting-space: true
    min-spaces-from-content: 1
  
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false
  
  document-start:
    present: true

  trailing-spaces: enable
  truthy: enable

ignore: |
  node_modules/
  .git/
  env/
  venv/
  migrations/ 