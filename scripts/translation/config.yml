# Translation Module Configuration

# Ollama API configuration
ollama:
  host: "ollama"
  port: 11434
  model: "gemma3:1b"
  temperature: 0.1

# Translation settings
translation:
  source_language: "en"
  default_target_language: "ar"
  batch_size: 10
  supported_languages:
    - ar
    - fr
    - es
    - de

# Cache settings
cache:
  enabled: true
  directory: "scripts/translation/cache"

# Logging settings
logging:
  level: "INFO"
  file: "scripts/translation/logs/translation.log"
  max_size_mb: 10
  backup_count: 3 