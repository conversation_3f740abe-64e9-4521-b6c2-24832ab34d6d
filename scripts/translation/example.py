"""
Example script for using the translation module.
"""

import os
import sys
import argparse
from pathlib import Path

from translator import Translator
from cache_manager import <PERSON><PERSON><PERSON>ana<PERSON>


def translate_examples():
    """
    Demonstrate translation of common patterns in Django translation files.
    """

    translator = Translator(target_lang="ar")
    cache = CacheManager(target_lang="ar")

    examples = [
        "User with this email already exists.",
        "User with this phone number already exists.",
        "Your verification code is: {verification_code.code}",
        "Verify Your Email",
        "User does not exist",
        "Please enter a valid phone number.",
        'Click <a href="reset-password">here</a> to reset your password.',
        "You have %(count)d unread messages.",
        "Welcome to %(site_name)s!",
    ]

    print("Translating example strings from English to Arabic:\n")
    print("-" * 80)

    for example in examples:

        cached = cache.get(example)
        if cached:
            print(f"Original: {example}")
            print(f"Translated (from cache): {cached}")
            print("-" * 80)
            continue

        try:
            translation = translator.translate_text(example)
            print(f"Original: {example}")
            print(f"Translated: {translation}")

            cache.set(example, translation)
        except Exception as e:
            print(f"Error translating '{example}': {e}")

        print("-" * 80)

    cache.close()


if __name__ == "__main__":
    translate_examples()
