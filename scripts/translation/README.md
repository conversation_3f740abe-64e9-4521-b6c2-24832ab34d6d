# Translation Module

A robust tool for automated translation of Django localization files using Ollama's LLM models.

## Features

- Automated translation of `.po` files using Ollama's language models
- Direct translation of individual strings
- Preserves variable placeholders and formatting in translations
- Efficient caching system to avoid redundant translations
- Multi-language support
- Batch processing for handling large files
- Comprehensive logging and error handling
- Simulation mode for testing/development without Ollama API

## Requirements

- Access to Ollama service with the gemma3:1b model
- Python 3.9+
- Django 4.2+

## Usage

### Basic Usage

```bash
# Translate a specific .po file
python -m scripts.translation.translate_file --file path/to/locale/ar/LC_MESSAGES/django.po

# Translate all .po files for a specific language
python -m scripts.translation.translate_all --language ar

# Force retranslation (ignore cache)
python -m scripts.translation.translate_file --file path/to/file.po --force

# Translate a single string
python -m scripts.translation.translate_string --text "Hello, world!" --target-language ar
```

### Single String Translation

The module provides several ways to translate individual strings:

#### Command Line

```bash
# Basic usage
python -m scripts.translation.translate_string --text "Text to translate" --target-language ar

# With custom source language
python -m scripts.translation.translate_string --text "Hola mundo" --source-language es --target-language fr

# Disable caching
python -m scripts.translation.translate_string --text "Hello" --no-cache

# Use simulation mode (no API calls)
python -m scripts.translation.translate_string --text "Hello" --simulation

# Force using the real API even when running locally
python -m scripts.translation.translate_string --text "Hello" --force-api
```

#### Programmatic Usage

```python
from scripts.translation.translate_string import translate_string

# Basic usage
result = translate_string("Text to translate", target_lang="ar")

# With custom options
result = translate_string(
    text="Hello world",
    source_lang="en",
    target_lang="fr",
    model="gemma3:1b",
    cache_enabled=True,
    simulated=True  # Use simulation mode instead of real API
)

print(f"Translated: {result}")
```

### Simulation Mode

The translation module includes a simulation mode that provides predefined translations for common strings without making actual API calls. This is useful for:

- Development and testing without Ollama API access
- Running in environments where Ollama isn't available
- Faster execution of tests and demos

When running locally (outside Docker), simulation mode is enabled by default. You can control it with:

```bash
# Explicitly enable simulation mode
python -m scripts.translation.translate_string --text "Hello" --simulation

# Force using real API even when running locally
python -m scripts.translation.translate_string --text "Hello" --force-api
```

Or programmatically:

```python
# Enable simulation mode
translator = Translator(simulated=True)

# Translate with simulation mode
result = translate_string(text="Hello", simulated=True)
```

### Configuration

The translation module can be configured using environment variables or a config file:

- `OLLAMA_HOST`: Hostname of the Ollama service (default: "ollama")
- `OLLAMA_PORT`: Port of the Ollama service (default: 11434)
- `OLLAMA_MODEL`: Model to use for translation (default: "gemma3:1b")
- `CACHE_ENABLED`: Whether to use caching (default: True)
- `BATCH_SIZE`: Number of strings to translate in a single batch (default: 10)
- `TRANSLATION_SIMULATED`: Use simulation mode instead of real API (default: False)
- `OLLAMA_CHECK_CONNECTION`: Whether to check API connection on startup (default: True)

## Modules

- `translate_file.py`: Translates a single .po file
- `translate_all.py`: Translates all .po files for a language
- `translate_string.py`: Translates a single string
- `translator.py`: Core translation logic
- `cache_manager.py`: Handles caching of translations
- `utils.py`: Utility functions for PO file parsing/saving

## Cache

Translations are cached in the `cache` directory to avoid redundant API calls. Each language has its own cache file.

## Testing

Run the tests using pytest:

```bash
python -m pytest scripts/translation/tests
```

## Examples

See `example.py` for sample code demonstrating string translation. 