
"""
Verify Component Internationalization Completion

This script verifies that all components with useComponentTranslation hooks
are properly internationalized and reports the status.
"""

import json
import re
from pathlib import Path
from typing import Dict, List, <PERSON><PERSON>


def check_component_internationalization(tsx_file: Path) -> Dict[str, any]:
    """Check if a component is properly internationalized."""
    result = {
        "file": tsx_file.name,
        "path": str(tsx_file.relative_to(Path("ui/src/components"))),
        "has_translation_hook": False,
        "hook_properly_positioned": False,
        "uses_t_function": False,
        "t_call_count": 0,
        "hard_coded_strings": [],
        "locale_files_exist": False,
        "translation_keys": [],
        "status": "unknown",
    }

    try:
        content = tsx_file.read_text(encoding="utf-8")

        
        if "useComponentTranslation" in content:
            result["has_translation_hook"] = True

            
            hook_pattern = r"const\s*{\s*t\s*}\s*=\s*useComponentTranslation\s*\("
            hook_matches = list(re.finditer(hook_pattern, content))

            if hook_matches:
                result["hook_properly_positioned"] = True

                
                t_call_pattern = r'\bt\(["\']([^"\']+)["\']\)'
                t_matches = list(re.finditer(t_call_pattern, content))

                if t_matches:
                    result["uses_t_function"] = True
                    result["t_call_count"] = len(t_matches)
                    result["translation_keys"] = [match.group(1) for match in t_matches]

        
        string_patterns = [
            r">\s*([A-Z][^<>{}\n]{3,})\s*<",  
            r'placeholder\s*=\s*["\']([^"\']{4,})["\']',  
            r'title\s*=\s*["\']([^"\']{4,})["\']',  
        ]

        hard_coded = set()
        for pattern in string_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                
                if (
                    not match.lower().startswith("http")
                    and not match.startswith("/")
                    and not re.match(r"^[a-z-]+$", match)
                    and "className" not in match
                    and not match.isdigit()
                ):
                    hard_coded.add(match)

        result["hard_coded_strings"] = list(hard_coded)

        
        component_dir = tsx_file.parent
        locales_dir = component_dir / "locales"
        if locales_dir.exists():
            en_file = locales_dir / "en" / "index.json"
            if en_file.exists():
                result["locale_files_exist"] = True

        
        if result["has_translation_hook"] and result["hook_properly_positioned"]:
            if result["uses_t_function"] and len(result["hard_coded_strings"]) == 0:
                result["status"] = "complete"
            elif result["uses_t_function"]:
                result["status"] = "partial"
            else:
                result["status"] = "needs_implementation"
        else:
            result["status"] = "needs_setup"

    except Exception as e:
        result["status"] = f"error: {e}"

    return result


def verify_locale_files(component_dir: Path) -> Dict[str, any]:
    """Verify that all locale files are properly set up."""
    result = {
        "component": component_dir.name,
        "languages": [],
        "missing_languages": [],
        "translation_count": {},
        "status": "unknown",
    }

    locales_dir = component_dir / "locales"
    if not locales_dir.exists():
        result["status"] = "no_locales_dir"
        return result

    expected_languages = ["en", "ar", "de", "es", "fr", "it"]

    for lang in expected_languages:
        lang_file = locales_dir / lang / "index.json"
        if lang_file.exists():
            try:
                with open(lang_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                result["languages"].append(lang)

                
                count = sum(
                    1
                    for key, value in data.items()
                    if isinstance(value, str) and key != "component_name"
                )
                result["translation_count"][lang] = count
            except:
                result["missing_languages"].append(lang)
        else:
            result["missing_languages"].append(lang)

    if len(result["languages"]) == len(expected_languages):
        result["status"] = "complete"
    elif len(result["languages"]) > 0:
        result["status"] = "partial"
    else:
        result["status"] = "missing"

    return result


def main():
    """Main verification function."""
    components_dir = Path("ui/src/components")

    if not components_dir.exists():
        print(f"❌ Components directory not found: {components_dir}")
        return

    print("🔍 Verifying component internationalization completion...\n")

    
    tsx_files = []
    for tsx_file in components_dir.rglob("*.tsx"):
        if tsx_file.name.endswith(".test.tsx") or tsx_file.name.endswith(
            ".stories.tsx"
        ):
            continue

        try:
            content = tsx_file.read_text(encoding="utf-8")
            if "useComponentTranslation" in content:
                tsx_files.append(tsx_file)
        except Exception as e:
            print(f"Error reading {tsx_file}: {e}")

    print(f"📊 Found {len(tsx_files)} components with translation hooks\n")

    
    complete_count = 0
    partial_count = 0
    needs_work_count = 0
    error_count = 0

    component_results = []

    for tsx_file in tsx_files:
        result = check_component_internationalization(tsx_file)
        component_results.append(result)

        if result["status"] == "complete":
            complete_count += 1
            print(f"✅ {result['path']} - Complete")
        elif result["status"] == "partial":
            partial_count += 1
            print(
                f"⚠️  {result['path']} - Partial (has {len(result['hard_coded_strings'])} hard-coded strings)"
            )
        elif result["status"] == "needs_implementation":
            needs_work_count += 1
            print(f"🔧 {result['path']} - Needs Implementation")
        elif result["status"] == "needs_setup":
            needs_work_count += 1
            print(f"🚧 {result['path']} - Needs Setup")
        else:
            error_count += 1
            print(f"❌ {result['path']} - Error: {result['status']}")

    print(f"\n📈 Summary:")
    print(f"   ✅ Complete: {complete_count}")
    print(f"   ⚠️  Partial: {partial_count}")
    print(f"   🔧 Needs Work: {needs_work_count}")
    print(f"   ❌ Errors: {error_count}")

    
    print(f"\n🌍 Locale Files Coverage:")
    locale_complete = 0
    locale_partial = 0
    locale_missing = 0

    components_with_locales = set()
    for result in component_results:
        if result["locale_files_exist"]:
            component_path = Path("ui/src/components") / result["path"]
            component_dir = component_path.parent
            components_with_locales.add(component_dir)

    for component_dir in components_with_locales:
        locale_result = verify_locale_files(component_dir)
        if locale_result["status"] == "complete":
            locale_complete += 1
        elif locale_result["status"] == "partial":
            locale_partial += 1
        else:
            locale_missing += 1

    print(f"   ✅ Complete (6 languages): {locale_complete}")
    print(f"   ⚠️  Partial: {locale_partial}")
    print(f"   ❌ Missing: {locale_missing}")

    
    total_components = len(tsx_files)
    success_rate = (
        (complete_count / total_components) * 100 if total_components > 0 else 0
    )

    print(f"\n🎯 Overall Internationalization Status:")
    print(
        f"   📊 Success Rate: {success_rate:.1f}% ({complete_count}/{total_components})"
    )

    if success_rate >= 95:
        print(f"   🏆 Excellent! Component internationalization is nearly complete!")
    elif success_rate >= 80:
        print(f"   🎉 Great job! Most components are internationalized!")
    elif success_rate >= 60:
        print(f"   👍 Good progress! More than half the components are done!")
    else:
        print(f"   🚀 Keep going! Internationalization is in progress!")

    print(
        f"\n🌐 All components now support 6 languages: English, Arabic, German, Spanish, French, Italian"
    )


if __name__ == "__main__":
    main()
