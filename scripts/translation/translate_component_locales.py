
"""
Translate Component Locale JSON Files

This script automatically translates all component locale JSON files from English
to the target languages (Arabic, German, Spanish, French, Italian) using the 
existing Ollama translation infrastructure.
"""

import json
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List


sys.path.append(str(Path(__file__).parent))

try:
    from cache_manager import CacheManager
    from translator import Translator
except ImportError as e:
    print(f"Error importing translation modules: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)


LANGUAGES = {
    "ar": "Arabic",
    "de": "German",
    "es": "Spanish",
    "fr": "French",
    "it": "Italian",
}

COMPONENTS_ROOT = (
    Path(__file__).resolve().parent.parent.parent / "ui" / "src" / "components"
)


def is_english_text(text: str) -> bool:
    """Check if text appears to be English and needs translation."""
    if not isinstance(text, str):
        return False

    
    if "{" in text or "{{" in text or text.startswith("http") or len(text.strip()) == 0:
        return False

    
    english_indicators = [
        "Loading...",
        "Error",
        "Success",
        "Cancel",
        "Confirm",
        "Save",
        "Edit",
        "Delete",
        "Search",
        "Filter",
        "Next",
        "Previous",
        "Close",
        "Open",
        "Home",
        "Back",
        "Forward",
        "Profile",
        "Settings",
        "Help",
        "About",
        "Login",
        "Logout",
        "Register",
        "Dashboard",
        "Menu",
        "Today",
        "Yesterday",
        "Tomorrow",
        "Draft",
        "Published",
        "Pending",
        "Approved",
        "Rejected",
        "Active",
        "Inactive",
        "Online",
        "Offline",
        "Available",
        "Unavailable",
        "Public",
        "Private",
        "Required",
        "Optional",
        "Invalid",
        "Valid",
    ]

    
    for indicator in english_indicators:
        if indicator.lower() in text.lower():
            return True

    
    if text.endswith("...") and any(
        word in text.lower() for word in ["load", "process", "sav", "upload"]
    ):
        return True

    return False


def translate_dict_values(
    data: Dict[str, Any],
    translator: Translator,
    cache: CacheManager,
    processed_count: List[int],
    total_count: int,
) -> Dict[str, Any]:
    """Recursively translate dictionary values that appear to be English."""
    if not isinstance(data, dict):
        return data

    result = {}

    for key, value in data.items():
        if isinstance(value, dict):
            result[key] = translate_dict_values(
                value, translator, cache, processed_count, total_count
            )
        elif isinstance(value, str) and is_english_text(value):
            
            cached_translation = cache.get(value)
            if cached_translation:
                result[key] = cached_translation
                print(f"  ✓ Cached: {value[:50]}{'...' if len(value) > 50 else ''}")
            else:
                try:
                    
                    translation = translator.translate_text(value)
                    if translation and translation != value:
                        result[key] = translation
                        cache.set(value, translation)
                        print(
                            f"  ✓ Translated: {value[:30]}{'...' if len(value) > 30 else ''} → {translation[:30]}{'...' if len(translation) > 30 else ''}"
                        )
                    else:
                        result[key] = value
                        print(f"  ⚠ No translation: {value}")
                except Exception as e:
                    print(f"  ❌ Translation failed for '{value}': {e}")
                    result[key] = value

                processed_count[0] += 1
                if processed_count[0] % 5 == 0:
                    print(
                        f"    Progress: {processed_count[0]}/{total_count} translations processed"
                    )

                
                time.sleep(0.1)
        else:
            result[key] = value

    return result


def count_translatable_strings(data: Dict[str, Any]) -> int:
    """Count the number of strings that need translation."""
    count = 0
    if isinstance(data, dict):
        for value in data.values():
            if isinstance(value, dict):
                count += count_translatable_strings(value)
            elif isinstance(value, str) and is_english_text(value):
                count += 1
    return count


def translate_component_locale_file(en_file: Path, target_lang: str) -> None:
    """Translate a single component locale file from English to target language."""

    
    try:
        with en_file.open("r", encoding="utf-8") as f:
            en_content = json.load(f)
    except Exception as e:
        print(f"⚠️  Error reading {en_file}: {e}")
        return

    
    locales_dir = en_file.parent.parent
    target_dir = locales_dir / target_lang
    target_file = target_dir / en_file.name

    
    if target_file.exists():
        try:
            with target_file.open("r", encoding="utf-8") as f:
                target_content = json.load(f)
        except Exception:
            target_content = {}
    else:
        target_content = {}

    
    def merge_dicts(source: Dict[str, Any], target: Dict[str, Any]) -> Dict[str, Any]:
        for key, value in source.items():
            if isinstance(value, dict):
                target.setdefault(key, {})
                if isinstance(target[key], dict):
                    merge_dicts(value, target[key])
                else:
                    target[key] = value
            else:
                target.setdefault(key, value)
        return target

    merged_content = merge_dicts(en_content, target_content)

    
    translatable_count = count_translatable_strings(merged_content)

    if translatable_count == 0:
        print(
            f"  ✅ No translations needed for {target_file.relative_to(COMPONENTS_ROOT.parent.parent)}"
        )
        return

    print(
        f"  🔄 Translating {translatable_count} strings to {LANGUAGES[target_lang]}..."
    )

    
    translator = Translator(
        source_lang="en",
        target_lang=target_lang,
        simulated=False,  
    )

    cache = CacheManager(target_lang=target_lang)
    processed_count = [0]

    
    translated_content = translate_dict_values(
        merged_content, translator, cache, processed_count, translatable_count
    )

    
    target_dir.mkdir(parents=True, exist_ok=True)
    with target_file.open("w", encoding="utf-8") as f:
        json.dump(translated_content, f, ensure_ascii=False, indent=2)

    print(f"  ✅ Saved {target_file.relative_to(COMPONENTS_ROOT.parent.parent)}")


def main():
    """Main function to translate all component locale files."""
    print("🚀 Starting component locale translation")
    print("=" * 60)

    
    en_files = list(COMPONENTS_ROOT.glob("**/locales/en/**/*.json"))

    if not en_files:
        print("❌ No English locale files found")
        return

    print(f"📁 Found {len(en_files)} English locale files")

    total_files = len(en_files) * len(LANGUAGES)
    processed_files = 0

    for en_file in en_files:
        component_path = en_file.relative_to(COMPONENTS_ROOT)
        print(f"\n📝 Processing: {component_path}")

        for lang_code in LANGUAGES.keys():
            processed_files += 1
            print(
                f"\n[{processed_files}/{total_files}] Translating to {LANGUAGES[lang_code]} ({lang_code})"
            )

            try:
                translate_component_locale_file(en_file, lang_code)
            except Exception as e:
                print(f"❌ Error translating {en_file} to {lang_code}: {e}")
                continue

    print("\n" + "=" * 60)
    print("🏁 Component locale translation complete!")
    print(f"✅ Processed {len(en_files)} components across {len(LANGUAGES)} languages")


if __name__ == "__main__":
    main()
