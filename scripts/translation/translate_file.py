"""
Script for translating a single .po file using Ollama API.
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

import polib


script_dir = Path(__file__).parent.absolute()
if str(script_dir) not in sys.path:
    sys.path.insert(0, str(script_dir.parent))

from scripts.translation.translator import Translator, TranslationError
from scripts.translation.cache_manager import CacheManager
from scripts.translation.utils import (
    parse_po_file,
    write_po_file,
    backup_po_file,
    extract_metadata,
    get_language_from_path,
)


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("translate_file")


def get_target_language(file_path: str, args: argparse.Namespace) -> str:
    """
    Determine the target language for translation.

    Args:
        file_path: Path to the .po file
        args: Command-line arguments

    Returns:
        Target language code, defaults to "ar" if not determined
    """

    if args.target_language:
        return args.target_language

    lang = get_language_from_path(file_path)
    if lang:
        return lang

    logger.warning("Could not determine target language, defaulting to Arabic (ar)")
    return "ar"


def translate_file(
    file_path: str,
    source_lang: str = "en",
    target_lang: str = "ar",
    batch_size: int = 10,
    force: bool = False,
    model: str = None,
    cache_enabled: bool = True,
) -> Dict[str, Any]:
    """
    Translate a .po file using Ollama API with caching.

    Args:
        file_path: Path to the .po file
        source_lang: Source language code
        target_lang: Target language code
        batch_size: Number of strings to translate in a batch
        force: Whether to force retranslation of already translated strings
        model: Ollama model to use
        cache_enabled: Whether to use caching

    Returns:
        Dictionary with translation statistics
    """
    start_time = time.time()

    try:

        po_file, untranslated_entries = parse_po_file(file_path)

        if not untranslated_entries and not force:
            logger.info(
                f"All entries in {file_path} are already translated. Use --force to retranslate."
            )
            return {
                "file": file_path,
                "total_entries": len(po_file),
                "translated_entries": len(po_file),
                "newly_translated": 0,
                "skipped": 0,
                "time_elapsed": time.time() - start_time,
            }

        backup_file = backup_po_file(file_path)

        cache = CacheManager(target_lang=target_lang, enabled=cache_enabled)

        translator = Translator(
            source_lang=source_lang, target_lang=target_lang, model=model
        )

        stats = {
            "total_entries": len(po_file),
            "already_translated": len(po_file) - len(untranslated_entries),
            "to_translate": len(untranslated_entries) if not force else len(po_file),
            "newly_translated": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
        }

        entries_to_translate = untranslated_entries if not force else po_file

        for i in range(0, len(entries_to_translate), batch_size):
            batch = entries_to_translate[i : i + batch_size]
            logger.info(
                f"Processing batch {i//batch_size + 1}/{(len(entries_to_translate) + batch_size - 1)//batch_size}"
            )

            for entry in batch:
                source_text = entry.msgid

                if not source_text or source_text.isspace():
                    continue

                if entry.msgstr and entry.msgstr != entry.msgid and not force:
                    continue

                try:

                    cached_translation = cache.get(source_text)

                    if cached_translation and not force:
                        entry.msgstr = cached_translation
                        stats["cache_hits"] += 1
                        stats["newly_translated"] += 1
                        logger.debug(f"Cache hit for: {source_text}")
                    else:

                        stats["cache_misses"] += 1
                        translated_text = translator.translate_text(source_text)

                        if translated_text and translated_text != source_text:
                            entry.msgstr = translated_text
                            cache.set(source_text, translated_text)
                            stats["newly_translated"] += 1
                            logger.debug(
                                f"Translated: {source_text} -> {translated_text}"
                            )
                        else:
                            stats["errors"] += 1
                            logger.warning(f"Failed to translate: {source_text}")

                except TranslationError as e:
                    stats["errors"] += 1
                    logger.error(f"Translation error for '{source_text}': {e}")

            cache._save_cache()
            write_po_file(po_file, file_path)

        write_po_file(po_file, file_path)

        cache.close()

        elapsed_time = time.time() - start_time
        stats["file"] = file_path
        stats["time_elapsed"] = elapsed_time
        stats["translations_per_second"] = (
            stats["newly_translated"] / elapsed_time if elapsed_time > 0 else 0
        )

        logger.info(
            f"Translation completed: {stats['newly_translated']} entries translated in {elapsed_time:.2f} seconds"
        )
        logger.info(
            f"Cache hits: {stats['cache_hits']}, Cache misses: {stats['cache_misses']}, Errors: {stats['errors']}"
        )

        return stats

    except Exception as e:
        logger.error(f"Error translating file {file_path}: {e}")
        return {
            "file": file_path,
            "error": str(e),
            "time_elapsed": time.time() - start_time,
        }


def main():
    """Parse command line arguments and run the translation."""
    parser = argparse.ArgumentParser(
        description="Translate a .po file using Ollama API"
    )
    parser.add_argument("--file", "-f", required=True, help="Path to the .po file")
    parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    parser.add_argument("--target-language", "-t", help="Target language code")
    parser.add_argument(
        "--batch-size",
        "-b",
        type=int,
        default=10,
        help="Number of strings to translate in a batch",
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force retranslation of already translated strings",
    )
    parser.add_argument("--model", "-m", help="Ollama model to use")
    parser.add_argument(
        "--no-cache", action="store_true", help="Disable translation caching"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    target_lang = get_target_language(args.file, args)

    stats = translate_file(
        file_path=args.file,
        source_lang=args.source_language,
        target_lang=target_lang,
        batch_size=args.batch_size,
        force=args.force,
        model=args.model,
        cache_enabled=not args.no_cache,
    )

    print("\nTranslation Summary:")
    print(f"File: {stats['file']}")
    if "error" in stats:
        print(f"Error: {stats['error']}")
    else:
        print(f"Total entries: {stats.get('total_entries', 0)}")
        print(f"Newly translated: {stats.get('newly_translated', 0)}")
        print(f"Cache hits: {stats.get('cache_hits', 0)}")
        print(f"Errors: {stats.get('errors', 0)}")
        print(f"Time elapsed: {stats.get('time_elapsed', 0):.2f} seconds")


if __name__ == "__main__":
    main()
