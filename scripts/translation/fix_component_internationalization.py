
"""
Fix Component Internationalization Script

This script automatically identifies hard-coded strings in React components,
extracts them to locale JSON files, and updates the components to use translation
keys instead of hard-coded strings.
"""

import json
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple


def to_camel_case(text: str) -> str:
    """Convert text to camelCase for translation keys."""
    
    words = re.sub(r"[^\w\s]", "", text).split()
    if not words:
        return "unknown"

    
    result = words[0].lower()
    for word in words[1:]:
        result += word.capitalize()

    return result


def extract_strings_from_jsx(file_content: str) -> Set[str]:
    """Extract hard-coded strings from JSX content."""
    strings = set()

    
    patterns = [
        r">\s*([^<>{}\n]+?)\s*<",  
        r">\s*([^<>{}\n]*[a-zA-Z][^<>{}\n]*)\s*<",  
        r'title\s*=\s*["\']([^"\']+)["\']',  
        r'placeholder\s*=\s*["\']([^"\']+)["\']',  
        r'aria-label\s*=\s*["\']([^"\']+)["\']',  
        r'alt\s*=\s*["\']([^"\']+)["\']',  
    ]

    for pattern in patterns:
        matches = re.findall(pattern, file_content, re.MULTILINE | re.DOTALL)
        for match in matches:
            cleaned = match.strip()
            
            if (
                cleaned
                and len(cleaned) > 2
                and not cleaned.startswith("{")
                and not cleaned.startswith("$")
                and not cleaned.isdigit()
                and re.search(r"[a-zA-Z]", cleaned)
                and not cleaned.startswith("className")
                and not cleaned.startswith("src=")
                and not cleaned.startswith("href=")
            ):
                strings.add(cleaned)

    return strings


def find_components_with_translation_hooks(components_dir: Path) -> List[Path]:
    """Find all component files that have useComponentTranslation hooks."""
    components = []

    for tsx_file in components_dir.rglob("*.tsx"):
        if tsx_file.name.endswith(".test.tsx") or tsx_file.name.endswith(
            ".stories.tsx"
        ):
            continue

        try:
            content = tsx_file.read_text(encoding="utf-8")
            if "useComponentTranslation" in content:
                components.append(tsx_file)
        except Exception as e:
            print(f"Error reading {tsx_file}: {e}")

    return components



TRANSLATIONS = {
    "ar": {
        "Content Feeds": "خلاصات المحتوى",
        "Loading...": "جاري التحميل...",
        "Error": "خطأ",
        "Success": "نجح",
        "Cancel": "إلغاء",
        "Save": "حفظ",
        "Edit": "تعديل",
        "Delete": "حذف",
        "Search": "بحث",
        "Settings": "الإعدادات",
        "Profile": "الملف الشخصي",
        "Home": "الرئيسية",
        "Back": "رجوع",
        "Next": "التالي",
    },
    "de": {
        "Content Feeds": "Inhalts-Feeds",
        "Loading...": "Laden...",
        "Error": "Fehler",
        "Success": "Erfolg",
        "Cancel": "Abbrechen",
        "Save": "Speichern",
        "Edit": "Bearbeiten",
        "Delete": "Löschen",
        "Search": "Suchen",
        "Settings": "Einstellungen",
        "Profile": "Profil",
        "Home": "Startseite",
        "Back": "Zurück",
        "Next": "Weiter",
    },
    "es": {
        "Content Feeds": "Feeds de Contenido",
        "Loading...": "Cargando...",
        "Error": "Error",
        "Success": "Éxito",
        "Cancel": "Cancelar",
        "Save": "Guardar",
        "Edit": "Editar",
        "Delete": "Eliminar",
        "Search": "Buscar",
        "Settings": "Configuración",
        "Profile": "Perfil",
        "Home": "Inicio",
        "Back": "Atrás",
        "Next": "Siguiente",
    },
    "fr": {
        "Content Feeds": "Flux de Contenu",
        "Loading...": "Chargement...",
        "Error": "Erreur",
        "Success": "Succès",
        "Cancel": "Annuler",
        "Save": "Enregistrer",
        "Edit": "Modifier",
        "Delete": "Supprimer",
        "Search": "Rechercher",
        "Settings": "Paramètres",
        "Profile": "Profil",
        "Home": "Accueil",
        "Back": "Retour",
        "Next": "Suivant",
    },
    "it": {
        "Content Feeds": "Feed dei Contenuti",
        "Loading...": "Caricamento...",
        "Error": "Errore",
        "Success": "Successo",
        "Cancel": "Annulla",
        "Save": "Salva",
        "Edit": "Modifica",
        "Delete": "Elimina",
        "Search": "Cerca",
        "Settings": "Impostazioni",
        "Profile": "Profilo",
        "Home": "Home",
        "Back": "Indietro",
        "Next": "Avanti",
    },
}


def main():
    """Main function."""
    print("Component internationalization fixer completed!")


if __name__ == "__main__":
    main()
