
"""
Clean Up Translation Files

This script removes technical strings (CSS classes, HTML attributes, etc.)
from translation files and keeps only user-facing content that should be translated.
"""

import json
import re
from pathlib import Path
from typing import Set


def is_technical_string(text: str) -> bool:
    """Check if a string is technical and shouldn't be translated."""

    
    technical_patterns = [
        r"^[a-z-]+$",  
        r"^h-\d+",  
        r"^w-\d+",  
        r"^bg-",  
        r"^text-",  
        r"^flex",  
        r"^absolute",  
        r"^relative",  
        r"^space-",  
        r"^p-\d+",  
        r"^m[btlr]?-",  
        r"^rounded",  
        r"^border",  
        r"^shadow",  
        r"^opacity-",  
        r"^transform",  
        r"^transition",  
        r"^hover:",  
        r"^focus:",  
        r"^animate-",  
        r"^sr-only$",  
        r"^Button$",  
        r"^Card[A-Z]",  
        r"^Table[A-Z]",  
        r"^Dialog[A-Z]",  
        r"^Sheet[A-Z]",  
        r"^Alert[A-Z]",  
        r"^Radio[A-Z]",  
        r"^Form[A-Z]",  
        r"^Input$",  
        r"^Textarea$",  
        r"^Separator$",  
        r"^Progress$",  
        r"^Dropdown",  
        r"^\w+:\s*React\.",  
        r"ComponentType",  
        r"VariantProps",  
        r"^currentColor$",  
        r"^transparent$",  
        r"^default$",  
        r"^center$",  
        r"^left$",  
        r"^right$",  
        r"^top$",  
        r"^bottom$",  
        r"^horizontal$",  
        r"^vertical$",  
        r"^outline$",  
        r"^ghost$",  
        r"^secondary$",  
        r"^destructive$",  
        r"^compact$",  
        r"^inline$",  
        r"^icon$",  
        r"^dropdown$",  
        r"^modal$",  
        r"^banner$",  
        r"^card$",  
        r"^button$",  
        r"^radio$",  
        r"^radiogroup$",  
        r"^progressbar$",  
        r"^alert$",  
        r"^status$",  
        r"^polite$",  
        r"^true$",  
        r"^false$",  
        r"^ltr$",  
        r"^rtl$",  
        r"^popper$",  
        r"^end$",  
        r"^\d+px$",  
        r"^#[0-9a-fA-F]+$",  
        r"^\w+\.\w+$",  
    ]

    
    for pattern in technical_patterns:
        if re.match(pattern, text.strip()):
            return True

    
    if " " in text and any(
        word in text
        for word in [
            "flex",
            "items-",
            "justify-",
            "text-",
            "bg-",
            "border-",
            "rounded",
            "shadow",
            "hover:",
            "focus:",
            "absolute",
            "relative",
        ]
    ):
        return True

    
    words = text.split()
    if len(words) > 1:
        css_words = sum(1 for word in words if re.match(r"^[a-z-]+\d*$", word))
        if css_words / len(words) > 0.7:  
            return True

    
    if any(char in text for char in ["(", ")", "{", "}", ":", "=", "&", "<", ">"]):
        return True

    return False


def get_user_facing_strings() -> Set[str]:
    """Get a set of strings that are definitely user-facing and should be translated."""
    return {
        
        "Search",
        "Loading...",
        "Error",
        "Success",
        "Cancel",
        "Save",
        "Edit",
        "Delete",
        "Submit",
        "Reset",
        "Close",
        "Open",
        "Yes",
        "No",
        "Back",
        "Next",
        "Previous",
        "Home",
        "Profile",
        "Settings",
        "Login",
        "Logout",
        "Register",
        "More",
        "Less",
        "Show more",
        "Show less",
        "Online",
        "Offline",
        "Active",
        "Inactive",
        
        "Username",
        "Password",
        "Email",
        "Name",
        "Message",
        "Send",
        "Reply",
        "Search forums, topics, and posts...",
        "Enter your message...",
        "Type here...",
        
        "Post",
        "Comment",
        "Like",
        "Share",
        "Report",
        "Quote",
        "Content Feeds",
        "Latest Topics (RSS)",
        "Latest Posts (RSS)",
        "Latest Activity (Atom)",
        "Stay updated with the newest forum discussions",
        "Get notified of all new posts across all topics",
        "Combined feed of all forum activity",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.",
        "Tip",
        
        "No Data Found",
        "No Results Found",
        "No Recent Achievements",
        "No Streaks Yet",
        "No Active Challenges",
        "No Milestones Yet",
        "No Progress Data",
        "No Chart Data",
        "No Analytics Data",
        "No Metrics Available",
        "Unable to Load Data",
        "Something went wrong while loading the data",
        "Data will appear here once you have tracking history",
        "No data available to display",
        "Try adjusting your search criteria or filters",
        
        "Complete challenges and reach milestones to earn your first achievements",
        "Start new challenges to track your carnivore journey progress",
        "Start tracking daily activities to build your streaks",
        "Start tracking your carnivore journey to see comprehensive analytics and insights",
        "Track your activities to see progress charts and analytics",
        "Complete challenges and track progress to see your success metrics",
        "Reach important milestones in your carnivore journey",
        
        "LIVE",
        "Live Stream",
        "Event Controls",
        "Start Event",
        "End Event",
        "Attendees",
        "Chat",
        "Polls",
        "Share",
        "Host",
        "Moderator",
        "Joined",
        "Event Not Found",
        "The requested event could not be loaded",
        "No polls available for this event",
        "Allow Chat:",
        "Allow Questions:",
        
        "Install App",
        "Install",
        "Maybe Later",
        "Not Now",
        "Free",
        "Benefits:",
        
        "connecting",
        "connected",
        "typing...",
        
        "scheduled",
        "live",
        "ended",
        "attendees",
        "polls",
        "chat",
        "mod",
    }


def clean_locale_file(file_path: Path) -> bool:
    """Clean a single locale file, removing technical strings."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
    except:
        return False

    user_facing_strings = get_user_facing_strings()
    original_data = data.copy()
    cleaned_data = {}

    
    if "component_name" in data:
        cleaned_data["component_name"] = data["component_name"]

    
    for key, value in data.items():
        if key == "component_name":
            continue

        if isinstance(value, dict):
            
            cleaned_data[key] = value
        elif isinstance(value, str):
            
            if not is_technical_string(value) and (
                value in user_facing_strings
                or len(value) > 3
                and any(c.isalpha() for c in value)
                and not value.startswith("http")
                and "::" not in value
                and "className" not in value
            ):
                cleaned_data[key] = value

    
    if cleaned_data != original_data:
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
        return True

    return False


def main():
    """Main function to clean all translation files."""
    components_dir = Path("ui/src/components")

    if not components_dir.exists():
        print(f"❌ Components directory not found: {components_dir}")
        return

    print("🧹 Cleaning up translation files...")

    total_cleaned = 0
    processed_components = 0

    
    for locale_file in components_dir.rglob("*/locales/*/index.json"):
        component_name = locale_file.parent.parent.parent.name
        language = locale_file.parent.name

        if clean_locale_file(locale_file):
            print(f"  🧽 Cleaned {component_name}/{language}")
            total_cleaned += 1

        if language == "en":  
            processed_components += 1

    print(f"\n🏁 Cleanup complete!")
    print(f"   📊 Processed {processed_components} components")
    print(f"   ✅ Cleaned {total_cleaned} locale files")
    print(f"   🎯 Removed technical strings, kept only user-facing content")


if __name__ == "__main__":
    main()
