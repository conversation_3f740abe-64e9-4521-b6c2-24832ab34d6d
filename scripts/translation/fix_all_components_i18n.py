
"""
Complete Component Internationalization Fix

This script processes all components with useComponentTranslation hooks,
extracts hard-coded strings, and properly internationalizes them.
"""

import json
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple


TRANSLATIONS = {
    "ar": {
        
        "Content Feeds": "خلاصات المحتوى",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "اشترك في خلاصات RSS/Atom للبقاء محدثًا مع آخر أنشطة المنتدى",
        "Latest Topics (RSS)": "آخر المواضيع (RSS)",
        "Stay updated with the newest forum discussions": "ابق محدثًا مع أحدث نقاشات المنتدى",
        "Latest Posts (RSS)": "آخر المنشورات (RSS)",
        "Get notified of all new posts across all topics": "احصل على إشعارات لجميع المنشورات الجديدة عبر جميع المواضيع",
        "Latest Activity (Atom)": "آخر النشاطات (Atom)",
        "Combined feed of all forum activity": "خلاصة مجمعة لجميع أنشطة المنتدى",
        "Tip": "نصيحة",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "انسخ عناوين الخلاصات لاستخدامها مع قارئ RSS المفضل لديك مثل Feedly أو Inoreader أو أي تطبيق بودكاست يدعم RSS.",
        
        "Loading...": "جاري التحميل...",
        "Error": "خطأ",
        "Success": "نجح",
        "Cancel": "إلغاء",
        "Save": "حفظ",
        "Edit": "تحرير",
        "Delete": "حذف",
        "Search": "بحث",
        "Filter": "تصفية",
        "Settings": "الإعدادات",
        "Profile": "الملف الشخصي",
        "Home": "الرئيسية",
        "Back": "رجوع",
        "Next": "التالي",
        "Previous": "السابق",
        "Submit": "إرسال",
        "Reset": "إعادة تعيين",
        "Close": "إغلاق",
        "Open": "فتح",
        "Show more": "عرض المزيد",
        "Show less": "عرض أقل",
        "Online": "متصل",
        "Offline": "غير متصل",
        "Active": "نشط",
        "Inactive": "غير نشط",
        "Login": "تسجيل الدخول",
        "Logout": "تسجيل الخروج",
        "Register": "التسجيل",
        "Username": "اسم المستخدم",
        "Password": "كلمة المرور",
        "Email": "البريد الإلكتروني",
        "Name": "الاسم",
        "Message": "الرسالة",
        "Send": "إرسال",
        "Reply": "رد",
        "Post": "منشور",
        "Comment": "تعليق",
        "Like": "إعجاب",
        "Share": "مشاركة",
        "Report": "إبلاغ",
        "Quote": "اقتباس",
    },
    "de": {
        
        "Content Feeds": "Inhalts-Feeds",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Abonnieren Sie unsere RSS/Atom-Feeds, um über die neueste Forum-Aktivität auf dem Laufenden zu bleiben",
        "Latest Topics (RSS)": "Neueste Themen (RSS)",
        "Stay updated with the newest forum discussions": "Bleiben Sie über die neuesten Forum-Diskussionen auf dem Laufenden",
        "Latest Posts (RSS)": "Neueste Beiträge (RSS)",
        "Get notified of all new posts across all topics": "Erhalten Sie Benachrichtigungen über alle neuen Beiträge zu allen Themen",
        "Latest Activity (Atom)": "Neueste Aktivität (Atom)",
        "Combined feed of all forum activity": "Kombinierter Feed aller Forum-Aktivitäten",
        "Tip": "Tipp",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Kopieren Sie Feed-URLs zur Verwendung mit Ihrem bevorzugten RSS-Reader wie Feedly, Inoreader oder jeder Podcast-App, die RSS unterstützt.",
        
        "Loading...": "Laden...",
        "Error": "Fehler",
        "Success": "Erfolg",
        "Cancel": "Abbrechen",
        "Save": "Speichern",
        "Edit": "Bearbeiten",
        "Delete": "Löschen",
        "Search": "Suchen",
        "Filter": "Filter",
        "Settings": "Einstellungen",
        "Profile": "Profil",
        "Home": "Startseite",
        "Back": "Zurück",
        "Next": "Weiter",
        "Previous": "Vorherige",
        "Submit": "Absenden",
        "Reset": "Zurücksetzen",
        "Close": "Schließen",
        "Open": "Öffnen",
        "Show more": "Mehr anzeigen",
        "Show less": "Weniger anzeigen",
        "Online": "Online",
        "Offline": "Offline",
        "Active": "Aktiv",
        "Inactive": "Inaktiv",
        "Login": "Anmelden",
        "Logout": "Abmelden",
        "Register": "Registrieren",
        "Username": "Benutzername",
        "Password": "Passwort",
        "Email": "E-Mail",
        "Name": "Name",
        "Message": "Nachricht",
        "Send": "Senden",
        "Reply": "Antworten",
        "Post": "Beitrag",
        "Comment": "Kommentar",
        "Like": "Gefällt mir",
        "Share": "Teilen",
        "Report": "Melden",
        "Quote": "Zitieren",
    },
    "es": {
        
        "Content Feeds": "Feeds de Contenido",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Suscríbete a nuestros feeds RSS/Atom para mantenerte actualizado con la última actividad del foro",
        "Latest Topics (RSS)": "Últimos Temas (RSS)",
        "Stay updated with the newest forum discussions": "Mantente actualizado con las discusiones más recientes del foro",
        "Latest Posts (RSS)": "Últimas Publicaciones (RSS)",
        "Get notified of all new posts across all topics": "Recibe notificaciones de todas las nuevas publicaciones en todos los temas",
        "Latest Activity (Atom)": "Última Actividad (Atom)",
        "Combined feed of all forum activity": "Feed combinado de toda la actividad del foro",
        "Tip": "Consejo",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copia las URLs de los feeds para usar con tu lector RSS favorito como Feedly, Inoreader o cualquier aplicación de podcast que soporte RSS.",
        
        "Loading...": "Cargando...",
        "Error": "Error",
        "Success": "Éxito",
        "Cancel": "Cancelar",
        "Save": "Guardar",
        "Edit": "Editar",
        "Delete": "Eliminar",
        "Search": "Buscar",
        "Filter": "Filtrar",
        "Settings": "Configuración",
        "Profile": "Perfil",
        "Home": "Inicio",
        "Back": "Atrás",
        "Next": "Siguiente",
        "Previous": "Anterior",
        "Submit": "Enviar",
        "Reset": "Restablecer",
        "Close": "Cerrar",
        "Open": "Abrir",
        "Show more": "Mostrar más",
        "Show less": "Mostrar menos",
        "Online": "En línea",
        "Offline": "Desconectado",
        "Active": "Activo",
        "Inactive": "Inactivo",
        "Login": "Iniciar sesión",
        "Logout": "Cerrar sesión",
        "Register": "Registrarse",
        "Username": "Nombre de usuario",
        "Password": "Contraseña",
        "Email": "Correo electrónico",
        "Name": "Nombre",
        "Message": "Mensaje",
        "Send": "Enviar",
        "Reply": "Responder",
        "Post": "Publicación",
        "Comment": "Comentario",
        "Like": "Me gusta",
        "Share": "Compartir",
        "Report": "Reportar",
        "Quote": "Citar",
    },
    "fr": {
        
        "Content Feeds": "Flux de Contenu",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Abonnez-vous à nos flux RSS/Atom pour rester à jour avec la dernière activité du forum",
        "Latest Topics (RSS)": "Derniers Sujets (RSS)",
        "Stay updated with the newest forum discussions": "Restez informé des dernières discussions du forum",
        "Latest Posts (RSS)": "Derniers Messages (RSS)",
        "Get notified of all new posts across all topics": "Recevez des notifications pour tous les nouveaux messages sur tous les sujets",
        "Latest Activity (Atom)": "Dernière Activité (Atom)",
        "Combined feed of all forum activity": "Flux combiné de toute l'activité du forum",
        "Tip": "Conseil",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copiez les URLs des flux pour les utiliser avec votre lecteur RSS préféré comme Feedly, Inoreader ou toute application de podcast qui prend en charge RSS.",
        
        "Loading...": "Chargement...",
        "Error": "Erreur",
        "Success": "Succès",
        "Cancel": "Annuler",
        "Save": "Enregistrer",
        "Edit": "Modifier",
        "Delete": "Supprimer",
        "Search": "Rechercher",
        "Filter": "Filtrer",
        "Settings": "Paramètres",
        "Profile": "Profil",
        "Home": "Accueil",
        "Back": "Retour",
        "Next": "Suivant",
        "Previous": "Précédent",
        "Submit": "Soumettre",
        "Reset": "Réinitialiser",
        "Close": "Fermer",
        "Open": "Ouvrir",
        "Show more": "Afficher plus",
        "Show less": "Afficher moins",
        "Online": "En ligne",
        "Offline": "Hors ligne",
        "Active": "Actif",
        "Inactive": "Inactif",
        "Login": "Se connecter",
        "Logout": "Se déconnecter",
        "Register": "S'inscrire",
        "Username": "Nom d'utilisateur",
        "Password": "Mot de passe",
        "Email": "E-mail",
        "Name": "Nom",
        "Message": "Message",
        "Send": "Envoyer",
        "Reply": "Répondre",
        "Post": "Publication",
        "Comment": "Commentaire",
        "Like": "J'aime",
        "Share": "Partager",
        "Report": "Signaler",
        "Quote": "Citer",
    },
    "it": {
        
        "Content Feeds": "Feed dei Contenuti",
        "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Iscriviti ai nostri feed RSS/Atom per rimanere aggiornato con l'ultima attività del forum",
        "Latest Topics (RSS)": "Ultimi Argomenti (RSS)",
        "Stay updated with the newest forum discussions": "Rimani aggiornato con le discussioni più recenti del forum",
        "Latest Posts (RSS)": "Ultimi Post (RSS)",
        "Get notified of all new posts across all topics": "Ricevi notifiche per tutti i nuovi post su tutti gli argomenti",
        "Latest Activity (Atom)": "Ultima Attività (Atom)",
        "Combined feed of all forum activity": "Feed combinato di tutta l'attività del forum",
        "Tip": "Suggerimento",
        "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copia gli URL dei feed da utilizzare con il tuo lettore RSS preferito come Feedly, Inoreader o qualsiasi app podcast che supporta RSS.",
        
        "Loading...": "Caricamento...",
        "Error": "Errore",
        "Success": "Successo",
        "Cancel": "Annulla",
        "Save": "Salva",
        "Edit": "Modifica",
        "Delete": "Elimina",
        "Search": "Cerca",
        "Filter": "Filtra",
        "Settings": "Impostazioni",
        "Profile": "Profilo",
        "Home": "Home",
        "Back": "Indietro",
        "Next": "Avanti",
        "Previous": "Precedente",
        "Submit": "Invia",
        "Reset": "Reimposta",
        "Close": "Chiudi",
        "Open": "Apri",
        "Show more": "Mostra di più",
        "Show less": "Mostra di meno",
        "Online": "Online",
        "Offline": "Offline",
        "Active": "Attivo",
        "Inactive": "Inattivo",
        "Login": "Accedi",
        "Logout": "Disconnetti",
        "Register": "Registrati",
        "Username": "Nome utente",
        "Password": "Password",
        "Email": "Email",
        "Name": "Nome",
        "Message": "Messaggio",
        "Send": "Invia",
        "Reply": "Rispondi",
        "Post": "Post",
        "Comment": "Commento",
        "Like": "Mi piace",
        "Share": "Condividi",
        "Report": "Segnala",
        "Quote": "Cita",
    },
}


def to_camel_case(text: str) -> str:
    """Convert text to camelCase for translation keys."""
    
    words = re.sub(r"[^\w\s]", "", text).split()
    if not words:
        return "unknown"

    
    result = words[0].lower()
    for word in words[1:]:
        result += word.capitalize()

    return result


def extract_strings_from_jsx(file_content: str) -> Set[str]:
    """Extract hard-coded strings from JSX content."""
    strings = set()

    
    patterns = [
        r">\s*([^<>{}\n]+?)\s*<",  
        r'title\s*=\s*["\']([^"\']+)["\']',  
        r'placeholder\s*=\s*["\']([^"\']+)["\']',  
        r'aria-label\s*=\s*["\']([^"\']+)["\']',  
        r'alt\s*=\s*["\']([^"\']+)["\']',  
    ]

    for pattern in patterns:
        matches = re.findall(pattern, file_content, re.MULTILINE | re.DOTALL)
        for match in matches:
            cleaned = match.strip()
            
            if (
                cleaned
                and len(cleaned) > 2
                and not cleaned.startswith("{")
                and not cleaned.startswith("$")
                and not cleaned.isdigit()
                and re.search(r"[a-zA-Z]", cleaned)
                and not cleaned.startswith("className")
                and not cleaned.startswith("src=")
                and not cleaned.startswith("href=")
                and not cleaned.startswith("http")
                and not cleaned.startswith("/")
                and "{{" not in cleaned
            ):
                strings.add(cleaned)

    return strings


def update_locale_files_with_translations(
    component_dir: Path, translation_keys: Dict[str, str]
):
    """Update locale JSON files with new translation keys."""
    locales_dir = component_dir / "locales"
    if not locales_dir.exists():
        return

    for lang in ["de", "es", "fr", "it"]:  
        lang_file = locales_dir / lang / "index.json"
        if not lang_file.exists():
            continue

        try:
            with open(lang_file, "r", encoding="utf-8") as f:
                locale_data = json.load(f)
        except:
            locale_data = {}

        
        updated = False
        for key, english_text in translation_keys.items():
            if key not in locale_data:
                translated_text = TRANSLATIONS.get(lang, {}).get(
                    english_text, english_text
                )
                locale_data[key] = translated_text
                updated = True

        
        if updated:
            with open(lang_file, "w", encoding="utf-8") as f:
                json.dump(locale_data, f, indent=2, ensure_ascii=False)
            print(f"Updated {lang_file}")


def main():
    """Main function to update all remaining locale files."""
    
    feed_links_dir = Path("ui/src/components/search/feed-links")

    translation_keys = {
        "contentFeeds": "Content Feeds",
        "subscribeToFeeds": "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity",
        "latestTopicsRss": "Latest Topics (RSS)",
        "stayUpdatedNewestDiscussions": "Stay updated with the newest forum discussions",
        "latestPostsRss": "Latest Posts (RSS)",
        "getNotifiedNewPosts": "Get notified of all new posts across all topics",
        "latestActivityAtom": "Latest Activity (Atom)",
        "combinedFeedAllActivity": "Combined feed of all forum activity",
        "tip": "Tip",
        "copyFeedUrlsInstructions": "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.",
    }

    update_locale_files_with_translations(feed_links_dir, translation_keys)
    print("Updated feed-links component translations for all languages")


if __name__ == "__main__":
    main()
