
"""
Complete Component Internationalization Automation

This script automatically fixes all components with useComponentTranslation hooks:
1. Moves translation hooks to correct scope
2. Extracts hard-coded strings  
3. Replaces strings with t() calls
4. Updates all locale JSON files with translations
"""

import json
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple


def to_camel_case(text: str) -> str:
    """Convert text to camelCase for translation keys."""
    
    text = re.sub(r"[^\w\s]", "", text.strip())
    words = text.split()
    if not words:
        return "unknown"

    
    result = words[0].lower()
    for word in words[1:]:
        result += word.capitalize()

    return result


def extract_hard_coded_strings(content: str) -> Set[str]:
    """Extract hard-coded strings that should be translated."""
    strings = set()

    
    jsx_text_pattern = r">\s*([^<>{}\n]+?)\s*<"
    matches = re.findall(jsx_text_pattern, content, re.MULTILINE)

    for match in matches:
        cleaned = match.strip()
        
        if (
            cleaned
            and len(cleaned) > 1
            and not cleaned.startswith("{")
            and not cleaned.startswith("$")
            and not cleaned.isdigit()
            and re.search(r"[a-zA-Z]", cleaned)
            and not cleaned.startswith("className")
            and not cleaned.startswith("http")
            and not cleaned.startswith("/")
            and "{{" not in cleaned
            and not re.match(r"^[^a-zA-Z]*$", cleaned)
        ):  
            strings.add(cleaned)

    
    attr_patterns = [
        r'placeholder\s*=\s*["\']([^"\']+)["\']',
        r'title\s*=\s*["\']([^"\']+)["\']',
        r'aria-label\s*=\s*["\']([^"\']+)["\']',
        r'alt\s*=\s*["\']([^"\']+)["\']',
    ]

    for pattern in attr_patterns:
        matches = re.findall(pattern, content, re.MULTILINE)
        for match in matches:
            cleaned = match.strip()
            if cleaned and len(cleaned) > 1 and not cleaned.startswith("{"):
                strings.add(cleaned)

    return strings


def fix_translation_hook_position(content: str, component_path: str) -> str:
    """Fix useComponentTranslation hook position and ensure it's in component scope."""

    
    component_func_pattern = r"export const \w+.*?=.*?\(.*?\).*?=>\s*{"
    component_match = re.search(
        component_func_pattern, content, re.MULTILINE | re.DOTALL
    )

    if not component_match:
        return content

    
    content = re.sub(
        r"\s*const\s*{\s*t\s*}\s*=\s*useComponentTranslation\([^)]+\);\s*", "", content
    )

    
    hook_code = f"""  const {{ t }} = useComponentTranslation({{
    componentPath: '{component_path}',
    namespaces: ['index'],
    fallbackNamespace: 'common'
  }});

"""

    
    insertion_point = component_match.end()
    content = content[:insertion_point] + "\n" + hook_code + content[insertion_point:]

    return content


def replace_strings_with_translations(
    content: str, translation_map: Dict[str, str]
) -> str:
    """Replace hard-coded strings with t() calls."""

    for original_string, key in translation_map.items():
        escaped_string = re.escape(original_string)

        
        pattern1 = f">{escaped_string}<"
        replacement1 = f">{{t('{key}')}}<"
        content = re.sub(pattern1, replacement1, content)

        
        pattern2 = f"([\"']){escaped_string}([\"'])"
        replacement2 = f"{{t('{key}')}}"
        content = re.sub(pattern2, replacement2, content)

        
        pattern3 = f"=\s*[\"'][^\"']*{escaped_string}[^\"']*[\"']"
        if re.search(pattern3, content):
            content = re.sub(pattern3, f"= {{t('{key}')}}", content)

    return content


def update_all_locale_files(component_dir: Path, translation_map: Dict[str, str]):
    """Update all locale JSON files with new translation keys."""
    locales_dir = component_dir / "locales"
    if not locales_dir.exists():
        return

    
    translations = {
        "ar": {
            "Search forums, topics, and posts...": "ابحث في المنتديات والمواضيع والمنشورات...",
            "Search": "بحث",
            "Content Feeds": "خلاصات المحتوى",
            "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "اشترك في خلاصات RSS/Atom للبقاء محدثًا مع آخر أنشطة المنتدى",
            "Latest Topics (RSS)": "آخر المواضيع (RSS)",
            "Stay updated with the newest forum discussions": "ابق محدثًا مع أحدث نقاشات المنتدى",
            "Latest Posts (RSS)": "آخر المنشورات (RSS)",
            "Get notified of all new posts across all topics": "احصل على إشعارات لجميع المنشورات الجديدة عبر جميع المواضيع",
            "Latest Activity (Atom)": "آخر النشاطات (Atom)",
            "Combined feed of all forum activity": "خلاصة مجمعة لجميع أنشطة المنتدى",
            "Tip": "نصيحة",
            "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "انسخ عناوين الخلاصات لاستخدامها مع قارئ RSS المفضل لديك مثل Feedly أو Inoreader أو أي تطبيق بودكاست يدعم RSS.",
            "Loading...": "جاري التحميل...",
            "Error": "خطأ",
            "Success": "نجح",
            "Cancel": "إلغاء",
            "Save": "حفظ",
            "Edit": "تحرير",
            "Delete": "حذف",
        },
        "de": {
            "Search forums, topics, and posts...": "Durchsuchen Sie Foren, Themen und Beiträge...",
            "Search": "Suchen",
            "Content Feeds": "Inhalts-Feeds",
            "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Abonnieren Sie unsere RSS/Atom-Feeds, um über die neueste Forum-Aktivität auf dem Laufenden zu bleiben",
            "Latest Topics (RSS)": "Neueste Themen (RSS)",
            "Stay updated with the newest forum discussions": "Bleiben Sie über die neuesten Forum-Diskussionen auf dem Laufenden",
            "Latest Posts (RSS)": "Neueste Beiträge (RSS)",
            "Get notified of all new posts across all topics": "Erhalten Sie Benachrichtigungen über alle neuen Beiträge zu allen Themen",
            "Latest Activity (Atom)": "Neueste Aktivität (Atom)",
            "Combined feed of all forum activity": "Kombinierter Feed aller Forum-Aktivitäten",
            "Tip": "Tipp",
            "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Kopieren Sie Feed-URLs zur Verwendung mit Ihrem bevorzugten RSS-Reader wie Feedly, Inoreader oder jeder Podcast-App, die RSS unterstützt.",
            "Loading...": "Laden...",
            "Error": "Fehler",
            "Success": "Erfolg",
            "Cancel": "Abbrechen",
            "Save": "Speichern",
            "Edit": "Bearbeiten",
            "Delete": "Löschen",
        },
        "es": {
            "Search forums, topics, and posts...": "Buscar en foros, temas y publicaciones...",
            "Search": "Buscar",
            "Content Feeds": "Feeds de Contenido",
            "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Suscríbete a nuestros feeds RSS/Atom para mantenerte actualizado con la última actividad del foro",
            "Latest Topics (RSS)": "Últimos Temas (RSS)",
            "Stay updated with the newest forum discussions": "Mantente actualizado con las discusiones más recientes del foro",
            "Latest Posts (RSS)": "Últimas Publicaciones (RSS)",
            "Get notified of all new posts across all topics": "Recibe notificaciones de todas las nuevas publicaciones en todos los temas",
            "Latest Activity (Atom)": "Última Actividad (Atom)",
            "Combined feed of all forum activity": "Feed combinado de toda la actividad del foro",
            "Tip": "Consejo",
            "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copia las URLs de los feeds para usar con tu lector RSS favorito como Feedly, Inoreader o cualquier aplicación de podcast que soporte RSS.",
            "Loading...": "Cargando...",
            "Error": "Error",
            "Success": "Éxito",
            "Cancel": "Cancelar",
            "Save": "Guardar",
            "Edit": "Editar",
            "Delete": "Eliminar",
        },
        "fr": {
            "Search forums, topics, and posts...": "Rechercher dans les forums, sujets et messages...",
            "Search": "Rechercher",
            "Content Feeds": "Flux de Contenu",
            "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Abonnez-vous à nos flux RSS/Atom pour rester à jour avec la dernière activité du forum",
            "Latest Topics (RSS)": "Derniers Sujets (RSS)",
            "Stay updated with the newest forum discussions": "Restez informé des dernières discussions du forum",
            "Latest Posts (RSS)": "Derniers Messages (RSS)",
            "Get notified of all new posts across all topics": "Recevez des notifications pour tous les nouveaux messages sur tous les sujets",
            "Latest Activity (Atom)": "Dernière Activité (Atom)",
            "Combined feed of all forum activity": "Flux combiné de toute l'activité du forum",
            "Tip": "Conseil",
            "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copiez les URLs des flux pour les utiliser avec votre lecteur RSS préféré comme Feedly, Inoreader ou toute application de podcast qui prend en charge RSS.",
            "Loading...": "Chargement...",
            "Error": "Erreur",
            "Success": "Succès",
            "Cancel": "Annuler",
            "Save": "Enregistrer",
            "Edit": "Modifier",
            "Delete": "Supprimer",
        },
        "it": {
            "Search forums, topics, and posts...": "Cerca nei forum, argomenti e post...",
            "Search": "Cerca",
            "Content Feeds": "Feed dei Contenuti",
            "Subscribe to our RSS/Atom feeds to stay updated with the latest forum activity": "Iscriviti ai nostri feed RSS/Atom per rimanere aggiornato con l'ultima attività del forum",
            "Latest Topics (RSS)": "Ultimi Argomenti (RSS)",
            "Stay updated with the newest forum discussions": "Rimani aggiornato con le discussioni più recenti del forum",
            "Latest Posts (RSS)": "Ultimi Post (RSS)",
            "Get notified of all new posts across all topics": "Ricevi notifiche per tutti i nuovi post su tutti gli argomenti",
            "Latest Activity (Atom)": "Ultima Attività (Atom)",
            "Combined feed of all forum activity": "Feed combinato di tutta l'attività del forum",
            "Tip": "Suggerimento",
            "Copy feed URLs to use with your favorite RSS reader like Feedly, Inoreader, or any podcast app that supports RSS.": "Copia gli URL dei feed da utilizzare con il tuo lettore RSS preferito come Feedly, Inoreader o qualsiasi app podcast che supporta RSS.",
            "Loading...": "Caricamento...",
            "Error": "Errore",
            "Success": "Successo",
            "Cancel": "Annulla",
            "Save": "Salva",
            "Edit": "Modifica",
            "Delete": "Elimina",
        },
    }

    for lang in ["en", "ar", "de", "es", "fr", "it"]:
        lang_file = locales_dir / lang / "index.json"
        if not lang_file.exists():
            continue

        try:
            with open(lang_file, "r", encoding="utf-8") as f:
                locale_data = json.load(f)
        except:
            locale_data = {}

        
        updated = False
        for key, english_text in translation_map.items():
            if key not in locale_data:
                if lang == "en":
                    locale_data[key] = english_text
                else:
                    
                    locale_data[key] = translations.get(lang, {}).get(
                        english_text, english_text
                    )
                updated = True

        if updated:
            with open(lang_file, "w", encoding="utf-8") as f:
                json.dump(locale_data, f, indent=2, ensure_ascii=False)
            print(f"Updated {lang_file}")


def process_component(tsx_file: Path, components_dir: Path):
    """Process a single component file."""
    print(f"\nProcessing: {tsx_file.relative_to(components_dir)}")

    
    content = tsx_file.read_text(encoding="utf-8")
    original_content = content

    
    component_path = str(tsx_file.relative_to(components_dir).parent)

    
    strings = extract_hard_coded_strings(content)

    if not strings:
        print("No hard-coded strings found")
        return

    print(f"Found {len(strings)} strings to translate:")
    for s in sorted(strings):
        print(f"  - '{s}'")

    
    translation_map = {}
    for string in strings:
        key = to_camel_case(string)
        translation_map[key] = string

    
    content = fix_translation_hook_position(content, component_path)

    
    content = replace_strings_with_translations(content, translation_map)

    
    if content != original_content:
        tsx_file.write_text(content, encoding="utf-8")
        print(f"Updated component file: {tsx_file}")

    
    update_all_locale_files(tsx_file.parent, translation_map)


def main():
    """Main function to process all components."""
    components_dir = Path("ui/src/components")

    if not components_dir.exists():
        print(f"Components directory not found: {components_dir}")
        return

    
    tsx_files = []
    for tsx_file in components_dir.rglob("*.tsx"):
        if tsx_file.name.endswith(".test.tsx") or tsx_file.name.endswith(
            ".stories.tsx"
        ):
            continue

        try:
            content = tsx_file.read_text(encoding="utf-8")
            if "useComponentTranslation" in content:
                tsx_files.append(tsx_file)
        except Exception as e:
            print(f"Error reading {tsx_file}: {e}")

    print(f"Found {len(tsx_files)} components with translation hooks")

    processed_count = 0
    for tsx_file in tsx_files:
        try:
            process_component(tsx_file, components_dir)
            processed_count += 1
        except Exception as e:
            print(f"Error processing {tsx_file}: {e}")

    print(f"\nCompleted! Successfully processed {processed_count} components")


if __name__ == "__main__":
    main()
