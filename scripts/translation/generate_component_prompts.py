"""
Generate Component Translation Prompts
Creates individualized prompts for each component to fix their internationalization
"""

import os
from pathlib import Path
from typing import List, <PERSON><PERSON>


def find_all_components() -> List[Tuple[str, str]]:
    """
    Find all components in ui/src/components directory.
    Returns list of tuples: (component_name, relative_path_from_components)
    """
    components = []
    components_dir = Path("ui/src/components")

    if not components_dir.exists():
        print(f"Error: {components_dir} does not exist")
        return components

    for tsx_file in components_dir.rglob("*.tsx"):
        if tsx_file.name.endswith((".test.tsx", ".stories.tsx")):
            continue

        relative_path = tsx_file.relative_to(components_dir)

        component_path = str(relative_path.parent)
        if component_path == ".":
            component_path = tsx_file.stem

        component_name = tsx_file.stem

        locales_dir = tsx_file.parent / "locales"
        if locales_dir.exists():
            components.append((component_name, component_path))

    return sorted(components)


def generate_prompt_for_component(component_name: str, component_path: str) -> str:
    """Generate a specific prompt for a component."""

    prompt = f"""
## Component: `{component_name}` (Path: `{component_path}`)

I want you to act as a **Senior TypeScript Developer with 20+ years of experience**: you value clean code, robustness, security, and strict adherence to React Framework best practices.

### Task: Fix Component Internationalization

**Component Location**: `ui/src/components/{component_path}/{component_name}.tsx`

**Requirements**:

1. **Verify Translation Hook Setup**: Ensure the component uses the translation hook correctly:
   ```typescript
   const {{ t }} = useComponentTranslation({{
     componentPath: '{component_path}',
     namespaces: ['index'],
     fallbackNamespace: 'common'
   }});
   ```

2. **Audit Hard-coded Strings**: Find all user-facing text in the component that should be translatable.

3. **Replace with Translation Calls**: Convert all hard-coded strings to `t('key')` calls.

4. **Synchronize ALL 6 Locale Files**: This is a critical step. For every translation key you add or use in the component, you **must** update all 6 language files. Do not update only the English file. The changes must cover all of the following files:
   - `.../locales/en/index.json` (English)
   - `.../locales/ar/index.json` (Arabic)
   - `.../locales/de/index.json` (German)
   - `.../locales/es/index.json` (Spanish)
   - `.../locales/fr/index.json` (French)
   - `.../locales/it/index.json` (Italian)

5. **Clean Unused Translations**: While ensuring full coverage for used keys, you must also remove any and all unused translation keys from all 6 locale files. The files should be perfectly synchronized with the component's needs.

6. **Mandatory Full Coverage**: Every single `t('key')` call in the component **must** have a corresponding, meaningful translation in all 6 language files listed above. Partial translation is not acceptable.

### Technical Constraints:
- Use Docker for all commands: `docker compose exec react_dev_frontend` (reference Makefile)
- Preserve existing file and folder structures
- Follow camelCase naming for translation keys
- Maintain Shadcn/UI patterns and existing component structure
- DO NOT translate CSS classes, component names, or technical identifiers
- Only translate user-facing text that users will read

### Success Criteria:
- ✅ Component uses `useComponentTranslation()` hook correctly
- ✅ All user-facing strings are replaced with `t('key')` calls
- ✅ **All 6 locale JSON files** contain exactly the keys used in the component
- ✅ No unused translation keys remain in any locale file
- ✅ **All 6 languages have complete and meaningful translations**
- ✅ Translation keys follow camelCase convention
- ✅ Component builds without TypeScript errors

### Important Notes:
- Focus ONLY on this specific component: `{component_name}`
- Do not modify other components or shared utilities
- Test the component still renders correctly after changes
- Ensure translations are meaningful and contextually appropriate for all 6 languages.

"""

    return prompt


def generate_all_prompts():
    """Generate prompts for all components and write to file."""

    components = find_all_components()

    if not components:
        print("No components with locales found!")
        return

    prompts_dir = Path("prompts")
    prompts_dir.mkdir(exist_ok=True)

    output_file = prompts_dir / "12-translations02.md"

    content = """# Component Internationalization Fix Prompts

This file contains individualized prompts for fixing internationalization in each component.
Each component needs to have its hard-coded strings replaced with proper translation calls
and locale files cleaned to contain only the translations actually used.

## Overview

**Total Components**: {total_components}

**Goal**: Ensure every component:
1. Uses `useComponentTranslation()` hook correctly
2. Has all user-facing strings replaced with `t('key')` calls  
3. Has clean locale files with only used translation keys
4. Supports all 6 languages (en, ar, de, es, fr, it)

---

""".format(
        total_components=len(components)
    )

    for i, (component_name, component_path) in enumerate(components, 1):
        content += f"### {i}. {component_name}\n"
        content += generate_prompt_for_component(component_name, component_path)
        content += "\n---\n\n"

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(content)

        print(f"✅ Generated prompts for {len(components)} components")
        print(f"📄 Prompts written to: {output_file}")
        print(f"\nComponents processed:")
        for component_name, component_path in components:
            print(f"  - {component_name} ({component_path})")

    except Exception as e:
        print(f"❌ Error writing prompts file: {e}")


if __name__ == "__main__":
    generate_all_prompts()
