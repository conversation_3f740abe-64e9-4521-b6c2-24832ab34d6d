"""
Unit tests for the cache manager module.
"""

import unittest
import tempfile
import json
import shutil
import os
from pathlib import Path

import sys
from unittest.mock import patch, MagicMock, mock_open


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from cache_manager import CacheManager


class TestCacheManager(unittest.TestCase):
    """Test cases for the CacheManager class."""

    def setUp(self):
        """Set up test fixtures."""

        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(
            cache_dir=self.temp_dir, target_lang="ar", enabled=True
        )

    def tearDown(self):
        """Tear down test fixtures."""

        shutil.rmtree(self.temp_dir)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.cache_manager.target_lang, "ar")
        self.assertEqual(self.cache_manager.cache_dir, Path(self.temp_dir))
        self.assertEqual(
            self.cache_manager.cache_file, Path(self.temp_dir) / "ar_cache.json"
        )
        self.assertTrue(self.cache_manager.enabled)
        self.assertEqual(self.cache_manager.cache, {})
        self.assertFalse(self.cache_manager.modified)

    def test_get_nonexistent(self):
        """Test getting a translation that doesn't exist in the cache."""
        self.assertIsNone(self.cache_manager.get("Hello world"))

    def test_set_and_get(self):
        """Test setting and getting a translation."""
        self.cache_manager.set("Hello world", "مرحبا بالعالم")
        self.assertEqual(self.cache_manager.get("Hello world"), "مرحبا بالعالم")
        self.assertTrue(self.cache_manager.modified)

    def test_normalize_key(self):
        """Test normalizing cache keys."""

        self.assertEqual(
            self.cache_manager._normalize_key("  Hello   world  "), "Hello world"
        )

        self.cache_manager.set("Hello world", "مرحبا بالعالم")
        self.assertEqual(self.cache_manager.get("  Hello   world  "), "مرحبا بالعالم")

    def test_disabled_cache(self):
        """Test behavior when cache is disabled."""
        disabled_cache = CacheManager(
            cache_dir=self.temp_dir, target_lang="ar", enabled=False
        )

        disabled_cache.set("Hello world", "مرحبا بالعالم")
        self.assertIsNone(disabled_cache.get("Hello world"))
        self.assertFalse(disabled_cache.modified)

    def test_save_and_load_cache(self):
        """Test saving and loading the cache."""

        self.cache_manager.set("Hello", "مرحبا")
        self.cache_manager.set("World", "عالم")

        self.cache_manager._save_cache()
        self.assertFalse(self.cache_manager.modified)

        self.assertTrue(self.cache_manager.cache_file.exists())

        new_cache = CacheManager(
            cache_dir=self.temp_dir, target_lang="ar", enabled=True
        )

        self.assertEqual(new_cache.get("Hello"), "مرحبا")
        self.assertEqual(new_cache.get("World"), "عالم")

    def test_clear(self):
        """Test clearing the cache."""

        self.cache_manager.set("Hello", "مرحبا")
        self.cache_manager.set("World", "عالم")

        self.cache_manager.clear()

        self.assertEqual(self.cache_manager.cache, {})
        self.assertIsNone(self.cache_manager.get("Hello"))

        with open(self.cache_manager.cache_file, "r") as f:
            data = json.load(f)
            self.assertEqual(data, {})

    def test_get_stats(self):
        """Test getting cache statistics."""

        self.cache_manager.set("Hello", "مرحبا")
        self.cache_manager.set("World", "عالم")

        stats = self.cache_manager.get_stats()

        self.assertEqual(stats["target_language"], "ar")
        self.assertEqual(stats["cache_entries"], 2)
        self.assertEqual(stats["cache_file"], str(self.cache_manager.cache_file))
        self.assertTrue(stats["cache_enabled"])

    def test_context_manager(self):
        """Test using the cache manager as a context manager."""
        with CacheManager(cache_dir=self.temp_dir, target_lang="fr") as cache:
            cache.set("Hello", "Bonjour")
            cache.set("World", "Monde")

        new_cache = CacheManager(cache_dir=self.temp_dir, target_lang="fr")
        self.assertEqual(new_cache.get("Hello"), "Bonjour")
        self.assertEqual(new_cache.get("World"), "Monde")


if __name__ == "__main__":
    unittest.main()
