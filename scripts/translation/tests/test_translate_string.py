"""
Unit tests for the single string translation module.
"""

import unittest
import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock


parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


from translate_string import translate_string
from translator import TranslationError, Translator


class TestTranslateString(unittest.TestCase):
    """Test cases for the translate_string function."""

    def setUp(self):
        """Set up test fixtures."""

        self.translator_patcher = patch("translate_string.Translator")
        self.cache_patcher = patch("translate_string.CacheManager")

        self.mock_translator_class = self.translator_patcher.start()
        self.mock_cache_class = self.cache_patcher.start()

        self.mock_translator = MagicMock()
        self.mock_translator.translate_text.return_value = "مرحبا بالعالم"
        self.mock_translator_class.return_value = self.mock_translator

        self.mock_cache = MagicMock()
        self.mock_cache.get.return_value = None
        self.mock_cache_class.return_value = self.mock_cache

    def tearDown(self):
        """Tear down test fixtures."""
        self.translator_patcher.stop()
        self.cache_patcher.stop()

    def test_translate_string_basic(self):
        """Test basic string translation."""
        result = translate_string("Hello world", "en", "ar", cache_enabled=True)

        self.assertEqual(result, "مرحبا بالعالم")

        self.mock_translator_class.assert_called_once_with(
            source_lang="en", target_lang="ar", model=None, simulated=None
        )
        self.mock_translator.translate_text.assert_called_once_with("Hello world")

        self.mock_cache.get.assert_called_once_with("Hello world")
        self.mock_cache.set.assert_called_once_with("Hello world", "مرحبا بالعالم")
        self.mock_cache.close.assert_called_once()

    def test_translate_string_simulated(self):
        """Test translation with simulation mode."""
        result = translate_string(
            "Hello world", "en", "ar", cache_enabled=True, simulated=True
        )

        self.mock_translator_class.assert_called_once_with(
            source_lang="en", target_lang="ar", model=None, simulated=True
        )

        self.mock_translator.translate_text.assert_called_once_with("Hello world")

        self.assertEqual(result, "مرحبا بالعالم")
        self.mock_cache.set.assert_called_once_with("Hello world", "مرحبا بالعالم")

    def test_translate_string_cache_hit(self):
        """Test translation with cache hit."""

        self.mock_cache.get.return_value = "كاش مرحبا بالعالم"

        result = translate_string("Hello world", "en", "ar", cache_enabled=True)

        self.assertEqual(result, "كاش مرحبا بالعالم")

        self.mock_translator.translate_text.assert_not_called()

        self.mock_cache.get.assert_called_once_with("Hello world")
        self.mock_cache.set.assert_not_called()
        self.mock_cache.close.assert_called_once()

    def test_translate_string_no_cache(self):
        """Test translation with caching disabled."""
        result = translate_string("Hello world", "en", "ar", cache_enabled=False)

        self.assertEqual(result, "مرحبا بالعالم")

        self.mock_translator.translate_text.assert_called_once_with("Hello world")

        self.mock_cache_class.assert_not_called()

    def test_translate_string_error(self):
        """Test handling translation errors."""

        self.mock_translator.translate_text.side_effect = TranslationError("API error")

        with self.assertRaises(TranslationError):
            translate_string("Hello world", "en", "ar")


if __name__ == "__main__":
    unittest.main()
