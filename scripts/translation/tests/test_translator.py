"""
Unit tests for the translator module.
"""

import unittest
import re
from unittest.mock import patch, MagicMock

import sys
import os
from pathlib import Path


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from translator import Translator, TranslationError


class TestTranslator(unittest.TestCase):
    """Test cases for the Translator class."""

    def setUp(self):
        """Set up test fixtures."""

        self.api_patcher = patch("translator.requests.get")
        self.mock_api_get = self.api_patcher.start()

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"models": [{"name": "gemma3:1b"}]}
        self.mock_api_get.return_value = mock_response

        self.translator = Translator(
            host="mock_host",
            port=12345,
            model="gemma3:1b",
            source_lang="en",
            target_lang="ar",
        )

    def tearDown(self):
        """Tear down test fixtures."""
        self.api_patcher.stop()

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.translator.host, "mock_host")
        self.assertEqual(self.translator.port, 12345)
        self.assertEqual(self.translator.model, "gemma3:1b")
        self.assertEqual(self.translator.source_lang, "en")
        self.assertEqual(self.translator.target_lang, "ar")
        self.assertEqual(self.translator.base_url, "http://mock_host:12345/api")

    def test_extract_placeholders(self):
        """Test extracting placeholders from text."""

        text = "Hello {name}, you have {count} messages"
        text_with_tokens, placeholders = self.translator._extract_placeholders(text)
        self.assertEqual(len(placeholders), 2)
        self.assertIn("{name}", list(placeholders.values()))
        self.assertIn("{count}", list(placeholders.values()))

        text = "Hello {user.username}, welcome to {site.name}"
        text_with_tokens, placeholders = self.translator._extract_placeholders(text)
        self.assertEqual(len(placeholders), 2)
        self.assertIn("{user.username}", list(placeholders.values()))
        self.assertIn("{site.name}", list(placeholders.values()))

        text = "Click <a href='/login'>here</a> to login"
        text_with_tokens, placeholders = self.translator._extract_placeholders(text)
        self.assertEqual(len(placeholders), 2)
        self.assertIn("<a href='/login'>", list(placeholders.values()))
        self.assertIn("</a>", list(placeholders.values()))

    def test_restore_placeholders(self):
        """Test restoring placeholders in translated text."""
        placeholders = {"__PH0__": "{name}", "__PH1__": "{count}"}
        text_with_tokens = "مرحبا __PH0__، لديك __PH1__ رسائل"
        original_text = self.translator._restore_placeholders(
            text_with_tokens, placeholders
        )
        self.assertEqual(original_text, "مرحبا {name}، لديك {count} رسائل")

    @patch("translator.requests.post")
    def test_translate_text(self, mock_post):
        """Test translating text."""

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"response": "مرحبا بالعالم"}
        mock_post.return_value = mock_response

        result = self.translator.translate_text("Hello world")
        self.assertEqual(result, "مرحبا بالعالم")

        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(kwargs["json"]["model"], "gemma3:1b")
        self.assertEqual(kwargs["json"]["temperature"], 0.1)
        self.assertFalse(kwargs["json"]["stream"])
        self.assertIn("Hello world", kwargs["json"]["prompt"])

        mock_post.reset_mock()
        mock_response.json.return_value = {"response": "مرحبا __PH0__"}

        with patch.object(
            self.translator,
            "_extract_placeholders",
            return_value=("Hello __PH0__", {"__PH0__": "{name}"}),
        ):
            with patch.object(
                self.translator, "_restore_placeholders", return_value="مرحبا {name}"
            ):
                result = self.translator.translate_text("Hello {name}")
                self.assertEqual(result, "مرحبا {name}")

    @patch("translator.requests.post")
    def test_translate_text_error(self, mock_post):
        """Test handling API errors."""

        mock_post.side_effect = Exception("API connection failed")

        with self.assertRaises(TranslationError) as context:
            self.translator.translate_text("Hello world")

        self.assertIn("API connection failed", str(context.exception))

    @patch("translator.Translator.translate_text")
    @patch("translator.time.sleep")
    def test_translate_batch(self, mock_sleep, mock_translate):
        """Test batch translation."""

        mock_translate.side_effect = ["مرحبا", "عالم"]

        results = self.translator.translate_batch(["Hello", "World"], batch_size=2)
        self.assertEqual(results, ["مرحبا", "عالم"])
        self.assertEqual(mock_translate.call_count, 2)

        mock_translate.reset_mock()
        mock_translate.side_effect = [TranslationError("API error"), "عالم"]

        results = self.translator.translate_batch(["Hello", "World"], batch_size=2)
        self.assertEqual(results, ["Hello", "عالم"])
        self.assertEqual(mock_translate.call_count, 2)


if __name__ == "__main__":
    unittest.main()
