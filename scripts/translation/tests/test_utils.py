"""
Unit tests for the translation utility functions.
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
import sys
from unittest.mock import patch, MagicMock, mock_open


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from utils import (
    parse_po_file,
    write_po_file,
    backup_po_file,
    find_po_files,
    extract_metadata,
    get_language_from_path,
)


SAMPLE_PO_CONTENT = """
msgid ""
msgstr ""
"Project-Id-Version: Django\\n"
"Report-Msgid-Bugs-To: \\n"
"Language: ar\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"

#: apps/accounts/auth/serializers/register.py:42
msgid "User with this email already exists."
msgstr ""

#: apps/accounts/auth/serializers/register.py:50
msgid "User with this phone number already exists."
msgstr "مستخدم بهذا الرقم موجود بالفعل."
"""


class TestTranslationUtils(unittest.TestCase):
    """Test cases for the translation utility functions."""

    def setUp(self):
        """Set up test fixtures."""

        self.temp_dir = tempfile.mkdtemp()

        self.po_file_path = os.path.join(self.temp_dir, "django.po")
        with open(self.po_file_path, "w", encoding="utf-8") as f:
            f.write(SAMPLE_PO_CONTENT)

        self.locale_dir = os.path.join(self.temp_dir, "locale")
        self.ar_dir = os.path.join(self.locale_dir, "ar", "LC_MESSAGES")
        self.fr_dir = os.path.join(self.locale_dir, "fr", "LC_MESSAGES")

        os.makedirs(self.ar_dir, exist_ok=True)
        os.makedirs(self.fr_dir, exist_ok=True)

        with open(os.path.join(self.ar_dir, "django.po"), "w", encoding="utf-8") as f:
            f.write(SAMPLE_PO_CONTENT)

        with open(os.path.join(self.fr_dir, "django.po"), "w", encoding="utf-8") as f:
            f.write(SAMPLE_PO_CONTENT.replace("Language: ar", "Language: fr"))

    def tearDown(self):
        """Tear down test fixtures."""

        shutil.rmtree(self.temp_dir)

    def test_parse_po_file(self):
        """Test parsing a PO file."""
        po_file, untranslated_entries = parse_po_file(self.po_file_path)

        self.assertEqual(len(po_file), 2)
        self.assertEqual(len(untranslated_entries), 1)

        self.assertEqual(
            untranslated_entries[0].msgid, "User with this email already exists."
        )
        self.assertEqual(untranslated_entries[0].msgstr, "")

    def test_write_po_file(self):
        """Test writing a PO file."""

        po_file, _ = parse_po_file(self.po_file_path)

        for entry in po_file:
            if entry.msgid == "User with this email already exists.":
                entry.msgstr = "مستخدم بهذا البريد الإلكتروني موجود بالفعل."

        new_file_path = os.path.join(self.temp_dir, "modified.po")
        write_po_file(po_file, new_file_path)

        new_po_file, _ = parse_po_file(new_file_path)
        for entry in new_po_file:
            if entry.msgid == "User with this email already exists.":
                self.assertEqual(
                    entry.msgstr, "مستخدم بهذا البريد الإلكتروني موجود بالفعل."
                )

    def test_backup_po_file(self):
        """Test creating a backup of a PO file."""
        backup_path = backup_po_file(self.po_file_path)

        self.assertTrue(os.path.exists(backup_path))

        with open(backup_path, "r", encoding="utf-8") as f:
            backup_content = f.read()

        with open(self.po_file_path, "r", encoding="utf-8") as f:
            original_content = f.read()

        self.assertEqual(backup_content, original_content)

    def test_find_po_files(self):
        """Test finding PO files in a directory tree."""

        po_files = find_po_files(self.temp_dir)
        self.assertEqual(len(po_files), 2)

        ar_po_files = find_po_files(self.temp_dir, language="ar")
        self.assertEqual(len(ar_po_files), 1)
        self.assertTrue(
            ar_po_files[0].endswith(os.path.join("ar", "LC_MESSAGES", "django.po"))
        )

        fr_po_files = find_po_files(self.temp_dir, language="fr")
        self.assertEqual(len(fr_po_files), 1)
        self.assertTrue(
            fr_po_files[0].endswith(os.path.join("fr", "LC_MESSAGES", "django.po"))
        )

    def test_extract_metadata(self):
        """Test extracting metadata from a PO file."""
        po_file, _ = parse_po_file(self.po_file_path)
        metadata = extract_metadata(po_file)

        self.assertEqual(metadata["language"], "ar")
        self.assertEqual(metadata["total_entries"], 2)
        self.assertEqual(metadata["translated_entries"], 1)

    def test_get_language_from_path(self):
        """Test extracting language code from a PO file path."""

        ar_path = os.path.join(
            self.temp_dir, "locale", "ar", "LC_MESSAGES", "django.po"
        )
        self.assertEqual(get_language_from_path(ar_path), "ar")

        fr_path = os.path.join(
            self.temp_dir, "locale", "fr", "LC_MESSAGES", "django.po"
        )
        self.assertEqual(get_language_from_path(fr_path), "fr")

        invalid_path = os.path.join(self.temp_dir, "django.po")
        self.assertIsNone(get_language_from_path(invalid_path))


if __name__ == "__main__":
    unittest.main()
