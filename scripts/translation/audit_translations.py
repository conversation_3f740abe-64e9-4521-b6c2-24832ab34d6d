#!/usr/bin/env python3
"""
Translation Audit Script

Comprehensive audit of translation keys and missing translations.
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, Set, List, Tuple

class TranslationAuditor:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.ui_src = self.project_root / "ui" / "src"
        self.locales_dir = self.ui_src / "locales"
        self.translation_keys = set()
        self.missing_translations = {}
        self.unused_keys = {}
        
    def scan_tsx_files(self) -> Set[str]:
        """Scan all TSX files for translation key usage"""
        print("🔍 Scanning TSX files for translation keys...")
        
        # Patterns to match translation key usage
        patterns = [
            r't\([\'"`]([^\'"`]+)[\'"`]\)',  # t('key')
            r'useTranslation\(\).*t\([\'"`]([^\'"`]+)[\'"`]\)',  # const {t} = useTranslation(); t('key')
            r'i18n\.t\([\'"`]([^\'"`]+)[\'"`]\)',  # i18n.t('key')
        ]
        
        found_keys = set()
        
        for tsx_file in self.ui_src.rglob("*.tsx"):
            try:
                content = tsx_file.read_text(encoding='utf-8')
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        found_keys.add(match)
                        print(f"📝 Found key '{match}' in {tsx_file.relative_to(self.ui_src)}")
                        
            except Exception as e:
                print(f"❌ Error reading {tsx_file}: {e}")
                
        return found_keys
    
    def load_translation_files(self) -> Dict[str, Dict]:
        """Load all translation JSON files"""
        print("📚 Loading translation files...")
        
        translations = {}
        
        for locale_dir in self.locales_dir.iterdir():
            if locale_dir.is_dir():
                locale_code = locale_dir.name
                translation_file = locale_dir / "translation.json"
                
                if translation_file.exists():
                    try:
                        with open(translation_file, 'r', encoding='utf-8') as f:
                            translations[locale_code] = json.load(f)
                        print(f"✅ Loaded {locale_code} translations")
                    except Exception as e:
                        print(f"❌ Error loading {translation_file}: {e}")
                        
        return translations
    
    def get_nested_keys(self, obj: Dict, prefix: str = "") -> Set[str]:
        """Recursively get all nested keys from translation object"""
        keys = set()
        
        for key, value in obj.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                keys.update(self.get_nested_keys(value, full_key))
            else:
                keys.add(full_key)
                
        return keys
    
    def audit_translations(self) -> Dict:
        """Perform comprehensive translation audit"""
        print("🔍 Starting translation audit...")
        
        # Get translation keys used in code
        used_keys = self.scan_tsx_files()
        
        # Load translation files
        translations = self.load_translation_files()
        
        # Get available translation keys
        available_keys = {}
        for locale, translation_data in translations.items():
            available_keys[locale] = self.get_nested_keys(translation_data)
        
        # Find missing translations
        missing_translations = {}
        for locale, available in available_keys.items():
            missing = used_keys - available
            if missing:
                missing_translations[locale] = sorted(missing)
        
        # Find unused translations
        unused_translations = {}
        for locale, available in available_keys.items():
            unused = available - used_keys
            if unused:
                unused_translations[locale] = sorted(unused)
        
        # Generate report
        report = {
            'summary': {
                'total_used_keys': len(used_keys),
                'total_available_keys': {locale: len(keys) for locale, keys in available_keys.items()},
                'missing_translations': {locale: len(keys) for locale, keys in missing_translations.items()},
                'unused_translations': {locale: len(keys) for locale, keys in unused_translations.items()}
            },
            'used_keys': sorted(used_keys),
            'missing_translations': missing_translations,
            'unused_translations': unused_translations,
            'available_locales': list(translations.keys())
        }
        
        return report
    
    def generate_missing_keys_template(self, missing_keys: List[str], locale: str) -> Dict:
        """Generate template for missing translation keys"""
        template = {}
        
        for key in missing_keys:
            # Create nested structure for dot-notation keys
            parts = key.split('.')
            current = template
            
            for i, part in enumerate(parts):
                if i == len(parts) - 1:
                    # Last part - add the actual key with placeholder
                    current[part] = f"[TRANSLATE:{locale.upper()}] {key}"
                else:
                    # Intermediate part - create nested object
                    if part not in current:
                        current[part] = {}
                    current = current[part]
        
        return template

def main():
    project_root = "/app"  # Docker container path
    auditor = TranslationAuditor(project_root)
    
    print("🚀 Starting Translation Audit")
    print("=" * 50)
    
    report = auditor.audit_translations()
    
    # Print summary
    print("\n📊 AUDIT SUMMARY")
    print("=" * 30)
    print(f"Total translation keys used in code: {report['summary']['total_used_keys']}")
    print(f"Available locales: {', '.join(report['summary']['available_locales'])}")
    
    for locale in report['summary']['available_locales']:
        available = report['summary']['total_available_keys'][locale]
        missing = report['summary']['missing_translations'].get(locale, 0)
        unused = report['summary']['unused_translations'].get(locale, 0)
        
        print(f"\n{locale.upper()} Locale:")
        print(f"  📚 Available translations: {available}")
        print(f"  ❌ Missing translations: {missing}")
        print(f"  🗑️  Unused translations: {unused}")
    
    # Print missing translations
    if report['missing_translations']:
        print("\n❌ MISSING TRANSLATIONS")
        print("=" * 30)
        for locale, missing_keys in report['missing_translations'].items():
            print(f"\n{locale.upper()} missing keys:")
            for key in missing_keys:
                print(f"  - {key}")
    
    # Generate templates for missing translations
    for locale, missing_keys in report['missing_translations'].items():
        if missing_keys:
            template = auditor.generate_missing_keys_template(missing_keys, locale)
            output_file = f"/app/ui/src/locales/{locale}/missing_translations.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)
            
            print(f"\n📝 Generated missing translations template: {output_file}")
    
    print("\n✅ Translation audit complete!")

if __name__ == "__main__":
    main() 