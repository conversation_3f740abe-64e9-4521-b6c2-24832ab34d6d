"""
Script for translating all .po files for a specific language using Ollama API.
"""

import os
import sys
import argparse
import logging
import time
import json
from pathlib import Path
from typing import Dict, List, Any


script_dir = Path(__file__).parent.absolute()
if str(script_dir) not in sys.path:
    sys.path.insert(0, str(script_dir.parent))

from scripts.translation.translate_file import translate_file
from scripts.translation.utils import find_po_files


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("translate_all")


def translate_all_files(
    base_dir: str,
    language: str = "ar",
    source_lang: str = "en",
    batch_size: int = 10,
    force: bool = False,
    model: str = None,
    cache_enabled: bool = True,
) -> Dict[str, Any]:
    """
    Translate all .po files for a specific language.

    Args:
        base_dir: Base directory to search for .po files
        language: Target language code
        source_lang: Source language code
        batch_size: Number of strings to translate in a batch
        force: Whether to force retranslation of already translated strings
        model: Ollama model to use
        cache_enabled: Whether to use caching

    Returns:
        Dictionary with translation statistics
    """
    start_time = time.time()

    try:

        po_files = find_po_files(base_dir, language)

        if not po_files:
            logger.warning(
                f"No .po files found for language '{language}' in {base_dir}"
            )
            return {
                "base_dir": base_dir,
                "language": language,
                "files_found": 0,
                "files_processed": 0,
                "time_elapsed": time.time() - start_time,
            }

        logger.info(f"Found {len(po_files)} .po files for language '{language}'")

        stats = {
            "base_dir": base_dir,
            "language": language,
            "files_found": len(po_files),
            "files_processed": 0,
            "files_with_errors": 0,
            "total_entries": 0,
            "newly_translated": 0,
            "file_stats": [],
        }

        for file_path in po_files:
            logger.info(
                f"Processing file {stats['files_processed'] + 1}/{len(po_files)}: {file_path}"
            )

            try:

                file_stats = translate_file(
                    file_path=file_path,
                    source_lang=source_lang,
                    target_lang=language,
                    batch_size=batch_size,
                    force=force,
                    model=model,
                    cache_enabled=cache_enabled,
                )

                stats["files_processed"] += 1
                if "error" in file_stats:
                    stats["files_with_errors"] += 1
                else:
                    stats["total_entries"] += file_stats.get("total_entries", 0)
                    stats["newly_translated"] += file_stats.get("newly_translated", 0)

                stats["file_stats"].append(file_stats)

            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                stats["files_with_errors"] += 1
                stats["file_stats"].append({"file": file_path, "error": str(e)})

        elapsed_time = time.time() - start_time
        stats["time_elapsed"] = elapsed_time

        logger.info(
            f"Translation completed: {stats['newly_translated']} entries translated in {elapsed_time:.2f} seconds"
        )
        logger.info(
            f"Files processed: {stats['files_processed']}, Files with errors: {stats['files_with_errors']}"
        )

        return stats

    except Exception as e:
        logger.error(f"Error translating files: {e}")
        return {
            "base_dir": base_dir,
            "language": language,
            "error": str(e),
            "time_elapsed": time.time() - start_time,
        }


def main():
    """Parse command line arguments and run the translation."""
    parser = argparse.ArgumentParser(
        description="Translate all .po files for a specific language"
    )
    parser.add_argument("--language", "-l", required=True, help="Target language code")
    parser.add_argument(
        "--base-dir", "-d", default=".", help="Base directory to search for .po files"
    )
    parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    parser.add_argument(
        "--batch-size",
        "-b",
        type=int,
        default=10,
        help="Number of strings to translate in a batch",
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force retranslation of already translated strings",
    )
    parser.add_argument("--model", "-m", help="Ollama model to use")
    parser.add_argument(
        "--no-cache", action="store_true", help="Disable translation caching"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument("--output", "-o", help="Path to save statistics as JSON")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    stats = translate_all_files(
        base_dir=args.base_dir,
        language=args.language,
        source_lang=args.source_language,
        batch_size=args.batch_size,
        force=args.force,
        model=args.model,
        cache_enabled=not args.no_cache,
    )

    print("\nTranslation Summary:")
    print(f"Language: {stats['language']}")
    print(f"Base directory: {stats['base_dir']}")
    if "error" in stats:
        print(f"Error: {stats['error']}")
    else:
        print(f"Files found: {stats.get('files_found', 0)}")
        print(f"Files processed: {stats.get('files_processed', 0)}")
        print(f"Files with errors: {stats.get('files_with_errors', 0)}")
        print(f"Total entries: {stats.get('total_entries', 0)}")
        print(f"Newly translated: {stats.get('newly_translated', 0)}")
        print(f"Time elapsed: {stats.get('time_elapsed', 0):.2f} seconds")

    if args.output:
        try:
            with open(args.output, "w", encoding="utf-8") as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"Statistics saved to {args.output}")
        except Exception as e:
            logger.error(f"Error saving statistics to {args.output}: {e}")


if __name__ == "__main__":
    main()
