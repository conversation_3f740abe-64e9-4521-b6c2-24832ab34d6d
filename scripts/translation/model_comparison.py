"""
<PERSON><PERSON><PERSON> to compare translation speed of different Ollama models.

This script tests translation performance of multiple Ollama models by translating
a set of 50 strings and measuring the time taken by each model. Results are sorted
by speed (fastest first).
"""

import time
import logging
import json
import os
import requests
from typing import List, Dict, Tuple, Any
import statistics
from pathlib import Path
import argparse
import sys


script_dir = Path(__file__).parent
if str(script_dir) not in sys.path:
    sys.path.insert(0, str(script_dir))


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("model_comparison")


TEST_STRINGS = [
    "Hello, world!",
    "Welcome to our application.",
    "Please enter your username and password to continue.",
    "Your account has been successfully created.",
    "Thank you for your purchase.",
    "The requested resource was not found.",
    "Invalid email address format.",
    "Password must contain at least 8 characters including a number and a special character.",
    "Your session has expired. Please log in again.",
    "An unexpected error occurred. Please try again later.",
    "The file was uploaded successfully.",
    "Unable to connect to the server. Please check your internet connection.",
    "Your order has been shipped and will arrive within 3-5 business days.",
    "Please confirm your email address by clicking the link we sent you.",
    "This feature is only available to premium users.",
    "Would you like to receive notifications about new products and offers?",
    "The changes you made have been saved.",
    "Your subscription will renew automatically on January 15, 2024.",
    "Please select your preferred payment method.",
    "The coupon code you entered is invalid or has expired.",
    "Your password has been reset successfully.",
    "Please complete all required fields marked with an asterisk.",
    "You have successfully subscribed to our newsletter.",
    "Your account has been locked due to multiple failed login attempts.",
    "Please verify your identity by entering the code sent to your phone.",
    "The item you added to your cart is currently out of stock.",
    "Your feedback has been submitted. Thank you for helping us improve!",
    "This action cannot be undone. Are you sure you want to proceed?",
    "Your profile information has been updated successfully.",
    "The maximum file size allowed is 10MB.",
    "Your message has been sent. We will get back to you within 24 hours.",
    "This content is not available in your country.",
    "Please accept the terms and conditions to continue.",
    "Your account will be deleted permanently. This action cannot be undone.",
    "The username you entered is already taken. Please choose another one.",
    "You have unsaved changes. Are you sure you want to leave this page?",
    "Your free trial will end in 3 days. Upgrade now to continue using premium features.",
    "Please provide a valid phone number.",
    "The link you followed has expired.",
    "You must be at least 18 years old to register.",
    "Please enable cookies to use all features of this website.",
    "Your device is not compatible with this application.",
    "Please check your spam folder if you cannot find the email.",
    "The promotion code has been applied to your order.",
    "Your current plan allows up to 5 users.",
    "This field is required and cannot be left blank.",
    "Please provide a valid credit card number.",
    "You are about to leave the secure area of this website.",
    "Your download will start automatically in a few seconds.",
    "Please rotate your device for a better experience.",
]


class ModelSpeedTest:
    """Class to test and compare translation speed of different Ollama models."""

    def __init__(self, host: str = "localhost", port: int = 11434):
        """
        Initialize the model speed test.

        Args:
            host: Ollama API host (default: "localhost")
            port: Ollama API port (default: 11434)
        """
        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}/api"
        self.models = []

        self._check_connection()

    def _check_connection(self) -> None:
        """
        Check if Ollama API is accessible.

        Raises:
            ConnectionError: If Ollama API is not accessible
        """
        try:
            response = requests.get(f"{self.base_url}/tags", timeout=5)
            if response.status_code != 200:
                raise ConnectionError(
                    f"Ollama API returned error: {response.status_code}"
                )
            logger.info("Successfully connected to Ollama API")
        except requests.RequestException as e:
            logger.error(f"Failed to connect to Ollama API: {e}")
            raise ConnectionError(f"Failed to connect to Ollama API: {e}")

    def get_available_models(self) -> List[str]:
        """
        Get list of available models from Ollama.

        Returns:
            List of available model names
        """
        try:
            response = requests.get(f"{self.base_url}/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                logger.info(f"Available models: {', '.join(models)}")
                return models
            else:
                logger.warning(f"Failed to get models: {response.status_code}")
                return []
        except requests.RequestException as e:
            logger.warning(f"Failed to get models: {e}")
            return []

    def translate_string(
        self, model: str, text: str, target_lang: str = "fr"
    ) -> Tuple[str, float]:
        """
        Translate a string using the specified model and measure the time taken.

        Args:
            model: Name of the Ollama model to use
            text: Text to translate
            target_lang: Target language code (default: "fr")

        Returns:
            Tuple of translated text and time taken in seconds
        """
        prompt = f"Translate the following text to {target_lang}. Return only the translation, without explanations:\n\n{text}"

        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": 0.1,
            "stream": False,
        }

        start_time = time.time()
        try:
            response = requests.post(
                f"{self.base_url}/generate", json=payload, timeout=30
            )
            if response.status_code == 200:
                result = response.json()
                translated_text = result.get("response", "").strip()
            else:
                logger.error(f"Translation error: {response.status_code}")
                translated_text = f"Error: {response.status_code}"
        except requests.RequestException as e:
            logger.error(f"Translation request failed: {e}")
            translated_text = f"Error: {str(e)}"

        end_time = time.time()
        time_taken = end_time - start_time

        return translated_text, time_taken

    def test_model_speed(
        self, model: str, strings: List[str], target_lang: str = "fr"
    ) -> Dict[str, Any]:
        """
        Test the translation speed of a model on a list of strings.

        Args:
            model: Name of the Ollama model to use
            strings: List of strings to translate
            target_lang: Target language code (default: "fr")

        Returns:
            Dictionary with test results
        """
        logger.info(f"Testing model: {model}")

        results = []
        times = []
        total_chars = 0

        for i, text in enumerate(strings):
            total_chars += len(text)
            translated, time_taken = self.translate_string(model, text, target_lang)
            results.append(
                {
                    "original": text,
                    "translated": translated,
                    "time": time_taken,
                    "chars": len(text),
                }
            )
            times.append(time_taken)

            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(strings)} strings")

        avg_time = statistics.mean(times)
        median_time = statistics.median(times)
        min_time = min(times)
        max_time = max(times)
        total_time = sum(times)
        chars_per_second = total_chars / total_time if total_time > 0 else 0

        return {
            "model": model,
            "translations": results,
            "stats": {
                "average_time": avg_time,
                "median_time": median_time,
                "min_time": min_time,
                "max_time": max_time,
                "total_time": total_time,
                "total_characters": total_chars,
                "characters_per_second": chars_per_second,
            },
        }

    def compare_models(
        self, models: List[str], strings: List[str] = None, target_lang: str = "fr"
    ) -> List[Dict[str, Any]]:
        """
        Compare the translation speed of multiple models.

        Args:
            models: List of model names to compare
            strings: List of strings to translate (default: TEST_STRINGS)
            target_lang: Target language code (default: "fr")

        Returns:
            List of model test results sorted by speed (fastest first)
        """
        if strings is None:
            strings = TEST_STRINGS

        available_models = self.get_available_models()
        models_to_test = [model for model in models if model in available_models]

        if not models_to_test:
            logger.error(f"None of the requested models {models} are available")
            return []

        if len(models_to_test) < len(models):
            missing = set(models) - set(models_to_test)
            logger.warning(f"Some requested models are not available: {missing}")

        results = []
        for model in models_to_test:
            result = self.test_model_speed(model, strings, target_lang)
            results.append(result)

        results.sort(key=lambda x: x["stats"]["total_time"])

        return results


def main():
    """Main function to run the model comparison."""
    parser = argparse.ArgumentParser(
        description="Compare translation speed of Ollama models"
    )
    parser.add_argument(
        "--models",
        type=str,
        default="qwen3:0.6b,qwen3:1.7b,gemma3:1b",
        help="Comma-separated list of model names to test",
    )
    parser.add_argument(
        "--host",
        type=str,
        default=os.environ.get("OLLAMA_HOST", "localhost"),
        help="Ollama API host",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=int(os.environ.get("OLLAMA_PORT", "11434")),
        help="Ollama API port",
    )
    parser.add_argument(
        "--target-lang", type=str, default="fr", help="Target language for translation"
    )
    parser.add_argument(
        "--output", type=str, help="Path to save detailed results as JSON"
    )

    args = parser.parse_args()
    models = [model.strip() for model in args.models.split(",")]

    try:

        tester = ModelSpeedTest(host=args.host, port=args.port)
        results = tester.compare_models(models, target_lang=args.target_lang)

        print("\nModel Speed Comparison Results")
        print("==============================")
        if not results:
            print("No results to display. Check if models are available.")
            return

        print("\nSpeed Ranking (fastest first):")
        for i, result in enumerate(results):
            model = result["model"]
            stats = result["stats"]
            print(f"{i+1}. {model}")
            print(f"   - Total time: {stats['total_time']:.2f}s")
            print(f"   - Avg time per string: {stats['average_time']:.3f}s")
            print(f"   - Characters per second: {stats['characters_per_second']:.2f}")
            print()

        if args.output:
            with open(args.output, "w") as f:
                json.dump(results, f, indent=2)
            print(f"Detailed results saved to {args.output}")

    except Exception as e:
        logger.error(f"Error during model comparison: {e}")
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
