
"""
Simple Component Locale Translation

This script quickly translates all component locale JSON files using
predefined translation dictionaries for common UI strings.
"""

import json
from pathlib import Path
from typing import Any, Dict


TRANSLATIONS = {
    "ar": {
        "Loading...": "جاري التحميل...",
        "Error": "خطأ",
        "Success": "نجح",
        "Cancel": "إلغاء",
        "Confirm": "تأكيد",
        "Save": "حفظ",
        "Edit": "تعديل",
        "Delete": "حذف",
        "Search": "بحث",
        "Filter": "تصفية",
        "Next": "التالي",
        "Previous": "السابق",
        "Close": "إغلاق",
        "Open": "فتح",
        "Home": "الرئيسية",
        "Back": "رجوع",
        "Forward": "إلى الأمام",
        "Profile": "الملف الشخصي",
        "Settings": "الإعدادات",
        "Help": "مساعدة",
        "About": "حول",
        "Login": "تسجيل الدخول",
        "Logout": "تسجيل الخروج",
        "Register": "تسجيل",
        "Dashboard": "لوحة التحكم",
        "Menu": "القائمة",
        "Today": "اليوم",
        "Yesterday": "أمس",
        "Tomorrow": "غداً",
        "Draft": "مسودة",
        "Published": "منشور",
        "Pending": "في الانتظار",
        "Approved": "موافق عليه",
        "Rejected": "مرفوض",
        "Active": "نشط",
        "Inactive": "غير نشط",
        "Online": "متصل",
        "Offline": "غير متصل",
        "Available": "متاح",
        "Unavailable": "غير متاح",
        "Public": "عام",
        "Private": "خاص",
        "Required": "مطلوب",
        "Optional": "اختياري",
        "Invalid": "غير صالح",
        "Valid": "صالح",
        "Submit": "إرسال",
        "Reset": "إعادة تعيين",
        "Clear": "مسح",
        "View": "عرض",
        "Create": "إنشاء",
        "Update": "تحديث",
        "Apply": "تطبيق",
        "Processing...": "جاري المعالجة...",
        "Saving...": "جاري الحفظ...",
        "Uploading...": "جاري الرفع...",
        "Downloading...": "جاري التحميل...",
        "Searching...": "جاري البحث...",
        "Browse": "تصفح",
        "Upload": "رفع",
        "Download": "تحميل",
        "Copy": "نسخ",
        "Paste": "لصق",
        "Cut": "قطع",
        "Undo": "تراجع",
        "Redo": "إعادة",
        "Select": "تحديد",
        "Select All": "تحديد الكل",
        "Deselect": "إلغاء التحديد",
        "Import": "استيراد",
        "Export": "تصدير",
        "Print": "طباعة",
        "Share": "مشاركة",
        "Send": "إرسال",
        "Receive": "استقبال",
        "Add": "إضافة",
        "Remove": "إزالة",
        "Insert": "إدراج",
        "Replace": "استبدال",
        "Find": "بحث",
        "Sort": "ترتيب",
        "Group": "تجميع",
        "Expand": "توسيع",
        "Collapse": "طي",
        "Refresh": "تحديث",
        "Reload": "إعادة تحميل",
    },
    "de": {
        "Loading...": "Wird geladen...",
        "Error": "Fehler",
        "Success": "Erfolg",
        "Cancel": "Abbrechen",
        "Confirm": "Bestätigen",
        "Save": "Speichern",
        "Edit": "Bearbeiten",
        "Delete": "Löschen",
        "Search": "Suchen",
        "Filter": "Filtern",
        "Next": "Weiter",
        "Previous": "Zurück",
        "Close": "Schließen",
        "Open": "Öffnen",
        "Home": "Startseite",
        "Back": "Zurück",
        "Forward": "Vorwärts",
        "Profile": "Profil",
        "Settings": "Einstellungen",
        "Help": "Hilfe",
        "About": "Über",
        "Login": "Anmelden",
        "Logout": "Abmelden",
        "Register": "Registrieren",
        "Dashboard": "Dashboard",
        "Menu": "Menü",
        "Today": "Heute",
        "Yesterday": "Gestern",
        "Tomorrow": "Morgen",
        "Draft": "Entwurf",
        "Published": "Veröffentlicht",
        "Pending": "Ausstehend",
        "Approved": "Genehmigt",
        "Rejected": "Abgelehnt",
        "Active": "Aktiv",
        "Inactive": "Inaktiv",
        "Online": "Online",
        "Offline": "Offline",
        "Available": "Verfügbar",
        "Unavailable": "Nicht verfügbar",
        "Public": "Öffentlich",
        "Private": "Privat",
        "Required": "Erforderlich",
        "Optional": "Optional",
        "Invalid": "Ungültig",
        "Valid": "Gültig",
        "Submit": "Senden",
        "Reset": "Zurücksetzen",
        "Clear": "Löschen",
        "View": "Anzeigen",
        "Create": "Erstellen",
        "Update": "Aktualisieren",
        "Apply": "Anwenden",
        "Processing...": "Wird verarbeitet...",
        "Saving...": "Wird gespeichert...",
        "Uploading...": "Wird hochgeladen...",
        "Downloading...": "Wird heruntergeladen...",
        "Searching...": "Wird gesucht...",
        "Browse": "Durchsuchen",
        "Upload": "Hochladen",
        "Download": "Herunterladen",
        "Copy": "Kopieren",
        "Paste": "Einfügen",
        "Cut": "Ausschneiden",
        "Undo": "Rückgängig",
        "Redo": "Wiederholen",
        "Select": "Auswählen",
        "Select All": "Alle auswählen",
        "Deselect": "Auswahl aufheben",
        "Import": "Importieren",
        "Export": "Exportieren",
        "Print": "Drucken",
        "Share": "Teilen",
        "Send": "Senden",
        "Receive": "Empfangen",
        "Add": "Hinzufügen",
        "Remove": "Entfernen",
        "Insert": "Einfügen",
        "Replace": "Ersetzen",
        "Find": "Finden",
        "Sort": "Sortieren",
        "Group": "Gruppieren",
        "Expand": "Erweitern",
        "Collapse": "Einklappen",
        "Refresh": "Aktualisieren",
        "Reload": "Neu laden",
    },
    "es": {
        "Loading...": "Cargando...",
        "Error": "Error",
        "Success": "Éxito",
        "Cancel": "Cancelar",
        "Confirm": "Confirmar",
        "Save": "Guardar",
        "Edit": "Editar",
        "Delete": "Eliminar",
        "Search": "Buscar",
        "Filter": "Filtrar",
        "Next": "Siguiente",
        "Previous": "Anterior",
        "Close": "Cerrar",
        "Open": "Abrir",
        "Home": "Inicio",
        "Back": "Atrás",
        "Forward": "Adelante",
        "Profile": "Perfil",
        "Settings": "Configuración",
        "Help": "Ayuda",
        "About": "Acerca de",
        "Login": "Iniciar sesión",
        "Logout": "Cerrar sesión",
        "Register": "Registrarse",
        "Dashboard": "Panel",
        "Menu": "Menú",
        "Today": "Hoy",
        "Yesterday": "Ayer",
        "Tomorrow": "Mañana",
        "Draft": "Borrador",
        "Published": "Publicado",
        "Pending": "Pendiente",
        "Approved": "Aprobado",
        "Rejected": "Rechazado",
        "Active": "Activo",
        "Inactive": "Inactivo",
        "Online": "En línea",
        "Offline": "Fuera de línea",
        "Available": "Disponible",
        "Unavailable": "No disponible",
        "Public": "Público",
        "Private": "Privado",
        "Required": "Requerido",
        "Optional": "Opcional",
        "Invalid": "Inválido",
        "Valid": "Válido",
        "Submit": "Enviar",
        "Reset": "Restablecer",
        "Clear": "Limpiar",
        "View": "Ver",
        "Create": "Crear",
        "Update": "Actualizar",
        "Apply": "Aplicar",
        "Processing...": "Procesando...",
        "Saving...": "Guardando...",
        "Uploading...": "Subiendo...",
        "Downloading...": "Descargando...",
        "Searching...": "Buscando...",
        "Browse": "Explorar",
        "Upload": "Subir",
        "Download": "Descargar",
        "Copy": "Copiar",
        "Paste": "Pegar",
        "Cut": "Cortar",
        "Undo": "Deshacer",
        "Redo": "Rehacer",
        "Select": "Seleccionar",
        "Select All": "Seleccionar todo",
        "Deselect": "Deseleccionar",
        "Import": "Importar",
        "Export": "Exportar",
        "Print": "Imprimir",
        "Share": "Compartir",
        "Send": "Enviar",
        "Receive": "Recibir",
        "Add": "Agregar",
        "Remove": "Quitar",
        "Insert": "Insertar",
        "Replace": "Reemplazar",
        "Find": "Encontrar",
        "Sort": "Ordenar",
        "Group": "Agrupar",
        "Expand": "Expandir",
        "Collapse": "Contraer",
        "Refresh": "Actualizar",
        "Reload": "Recargar",
    },
    "fr": {
        "Loading...": "Chargement...",
        "Error": "Erreur",
        "Success": "Succès",
        "Cancel": "Annuler",
        "Confirm": "Confirmer",
        "Save": "Enregistrer",
        "Edit": "Modifier",
        "Delete": "Supprimer",
        "Search": "Rechercher",
        "Filter": "Filtrer",
        "Next": "Suivant",
        "Previous": "Précédent",
        "Close": "Fermer",
        "Open": "Ouvrir",
        "Home": "Accueil",
        "Back": "Retour",
        "Forward": "Suivant",
        "Profile": "Profil",
        "Settings": "Paramètres",
        "Help": "Aide",
        "About": "À propos",
        "Login": "Se connecter",
        "Logout": "Se déconnecter",
        "Register": "S'inscrire",
        "Dashboard": "Tableau de bord",
        "Menu": "Menu",
        "Today": "Aujourd'hui",
        "Yesterday": "Hier",
        "Tomorrow": "Demain",
        "Draft": "Brouillon",
        "Published": "Publié",
        "Pending": "En attente",
        "Approved": "Approuvé",
        "Rejected": "Rejeté",
        "Active": "Actif",
        "Inactive": "Inactif",
        "Online": "En ligne",
        "Offline": "Hors ligne",
        "Available": "Disponible",
        "Unavailable": "Indisponible",
        "Public": "Public",
        "Private": "Privé",
        "Required": "Requis",
        "Optional": "Optionnel",
        "Invalid": "Invalide",
        "Valid": "Valide",
        "Submit": "Soumettre",
        "Reset": "Réinitialiser",
        "Clear": "Effacer",
        "View": "Voir",
        "Create": "Créer",
        "Update": "Mettre à jour",
        "Apply": "Appliquer",
        "Processing...": "Traitement...",
        "Saving...": "Enregistrement...",
        "Uploading...": "Téléchargement...",
        "Downloading...": "Téléchargement...",
        "Searching...": "Recherche...",
        "Browse": "Parcourir",
        "Upload": "Télécharger",
        "Download": "Télécharger",
        "Copy": "Copier",
        "Paste": "Coller",
        "Cut": "Couper",
        "Undo": "Annuler",
        "Redo": "Refaire",
        "Select": "Sélectionner",
        "Select All": "Tout sélectionner",
        "Deselect": "Désélectionner",
        "Import": "Importer",
        "Export": "Exporter",
        "Print": "Imprimer",
        "Share": "Partager",
        "Send": "Envoyer",
        "Receive": "Recevoir",
        "Add": "Ajouter",
        "Remove": "Supprimer",
        "Insert": "Insérer",
        "Replace": "Remplacer",
        "Find": "Trouver",
        "Sort": "Trier",
        "Group": "Grouper",
        "Expand": "Développer",
        "Collapse": "Réduire",
        "Refresh": "Actualiser",
        "Reload": "Recharger",
    },
    "it": {
        "Loading...": "Caricamento...",
        "Error": "Errore",
        "Success": "Successo",
        "Cancel": "Annulla",
        "Confirm": "Conferma",
        "Save": "Salva",
        "Edit": "Modifica",
        "Delete": "Elimina",
        "Search": "Cerca",
        "Filter": "Filtra",
        "Next": "Successivo",
        "Previous": "Precedente",
        "Close": "Chiudi",
        "Open": "Apri",
        "Home": "Home",
        "Back": "Indietro",
        "Forward": "Avanti",
        "Profile": "Profilo",
        "Settings": "Impostazioni",
        "Help": "Aiuto",
        "About": "Informazioni",
        "Login": "Accedi",
        "Logout": "Esci",
        "Register": "Registrati",
        "Dashboard": "Dashboard",
        "Menu": "Menu",
        "Today": "Oggi",
        "Yesterday": "Ieri",
        "Tomorrow": "Domani",
        "Draft": "Bozza",
        "Published": "Pubblicato",
        "Pending": "In attesa",
        "Approved": "Approvato",
        "Rejected": "Rifiutato",
        "Active": "Attivo",
        "Inactive": "Inattivo",
        "Online": "Online",
        "Offline": "Offline",
        "Available": "Disponibile",
        "Unavailable": "Non disponibile",
        "Public": "Pubblico",
        "Private": "Privato",
        "Required": "Richiesto",
        "Optional": "Opzionale",
        "Invalid": "Non valido",
        "Valid": "Valido",
        "Submit": "Invia",
        "Reset": "Reimposta",
        "Clear": "Cancella",
        "View": "Visualizza",
        "Create": "Crea",
        "Update": "Aggiorna",
        "Apply": "Applica",
        "Processing...": "Elaborazione...",
        "Saving...": "Salvataggio...",
        "Uploading...": "Caricamento...",
        "Downloading...": "Scaricamento...",
        "Searching...": "Ricerca...",
        "Browse": "Sfoglia",
        "Upload": "Carica",
        "Download": "Scarica",
        "Copy": "Copia",
        "Paste": "Incolla",
        "Cut": "Taglia",
        "Undo": "Annulla",
        "Redo": "Ripeti",
        "Select": "Seleziona",
        "Select All": "Seleziona tutto",
        "Deselect": "Deseleziona",
        "Import": "Importa",
        "Export": "Esporta",
        "Print": "Stampa",
        "Share": "Condividi",
        "Send": "Invia",
        "Receive": "Ricevi",
        "Add": "Aggiungi",
        "Remove": "Rimuovi",
        "Insert": "Inserisci",
        "Replace": "Sostituisci",
        "Find": "Trova",
        "Sort": "Ordina",
        "Group": "Raggruppa",
        "Expand": "Espandi",
        "Collapse": "Comprimi",
        "Refresh": "Aggiorna",
        "Reload": "Ricarica",
    },
}

COMPONENTS_ROOT = (
    Path(__file__).resolve().parent.parent.parent / "ui" / "src" / "components"
)


def translate_dict_values(
    data: Dict[str, Any], lang_translations: Dict[str, str]
) -> Dict[str, Any]:
    """Recursively translate dictionary values using the translation dictionary."""
    if not isinstance(data, dict):
        return data

    result = {}

    for key, value in data.items():
        if isinstance(value, dict):
            result[key] = translate_dict_values(value, lang_translations)
        elif isinstance(value, str) and value in lang_translations:
            result[key] = lang_translations[value]
            print(f"  ✓ Translated: {value} → {lang_translations[value]}")
        else:
            result[key] = value

    return result


def translate_component_locale_file(en_file: Path, target_lang: str) -> None:
    """Translate a single component locale file from English to target language."""

    
    try:
        with en_file.open("r", encoding="utf-8") as f:
            en_content = json.load(f)
    except Exception as e:
        print(f"⚠️  Error reading {en_file}: {e}")
        return

    
    locales_dir = en_file.parent.parent
    target_dir = locales_dir / target_lang
    target_file = target_dir / en_file.name

    
    if target_file.exists():
        try:
            with target_file.open("r", encoding="utf-8") as f:
                target_content = json.load(f)
        except Exception:
            target_content = {}
    else:
        target_content = {}

    
    def merge_dicts(source: Dict[str, Any], target: Dict[str, Any]) -> Dict[str, Any]:
        for key, value in source.items():
            if isinstance(value, dict):
                target.setdefault(key, {})
                if isinstance(target[key], dict):
                    merge_dicts(value, target[key])
                else:
                    target[key] = value
            else:
                target.setdefault(key, value)
        return target

    merged_content = merge_dicts(en_content, target_content)

    
    lang_translations = TRANSLATIONS.get(target_lang, {})

    if not lang_translations:
        print(f"  ⚠️  No translations available for language: {target_lang}")
        return

    print(f"  🔄 Translating to {target_lang.upper()}...")

    
    translated_content = translate_dict_values(merged_content, lang_translations)

    
    target_dir.mkdir(parents=True, exist_ok=True)
    with target_file.open("w", encoding="utf-8") as f:
        json.dump(translated_content, f, ensure_ascii=False, indent=2)

    print(f"  ✅ Saved {target_file.relative_to(COMPONENTS_ROOT.parent.parent)}")


def main():
    """Main function to translate all component locale files."""
    print("🚀 Starting component locale translation")
    print("=" * 60)

    
    en_files = list(COMPONENTS_ROOT.glob("**/locales/en/**/*.json"))

    if not en_files:
        print("❌ No English locale files found")
        return

    print(f"📁 Found {len(en_files)} English locale files")

    total_files = len(en_files) * len(TRANSLATIONS)
    processed_files = 0

    for en_file in en_files:
        component_path = en_file.relative_to(COMPONENTS_ROOT)
        print(f"\n📝 Processing: {component_path}")

        for lang_code in TRANSLATIONS.keys():
            processed_files += 1
            print(
                f"\n[{processed_files}/{total_files}] Translating to {lang_code.upper()}"
            )

            try:
                translate_component_locale_file(en_file, lang_code)
            except Exception as e:
                print(f"❌ Error translating {en_file} to {lang_code}: {e}")
                continue

    print("\n" + "=" * 60)
    print("🏁 Component locale translation complete!")
    print(
        f"✅ Processed {len(en_files)} components across {len(TRANSLATIONS)} languages"
    )


if __name__ == "__main__":
    main()
