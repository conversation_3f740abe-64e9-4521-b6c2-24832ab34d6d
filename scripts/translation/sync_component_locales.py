
"""
Sync Component Locale JSON Files

This utility ensures that every component in `ui/src/components` has
complete locale JSON files for Arabic (ar), German (de), Spanish (es),
French (fr), and Italian (it). It uses the English locale file as the
source of truth and fills in any missing keys for the other languages
with the English strings as a fallback. The script is idempotent and
will create locale folders/files if they do not exist.
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict


TARGET_LANGUAGES = ["ar", "de", "es", "fr", "it"]


TRANSLATIONS = {
    "ar": {
        "searchForumsTopicsPosts": "ابحث في المنتديات والمواضيع والمنشورات...",
        "search": "بحث",
    },
    "de": {
        "searchForumsTopicsPosts": "Durchsuchen Sie Foren, Themen und Beiträge...",
        "search": "Suchen",
    },
    "es": {
        "searchForumsTopicsPosts": "Buscar en foros, temas y publicaciones...",
        "search": "Buscar",
    },
    "fr": {
        "searchForumsTopicsPosts": "Rechercher dans les forums, sujets et messages...",
        "search": "Rechercher",
    },
    "it": {
        "searchForumsTopicsPosts": "Cerca nei forum, argomenti e post...",
        "search": "Cerca",
    },
}


def merge_dictionaries(
    target: Dict[str, Any], source: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Recursively merge two dictionaries, preserving existing values in target
    and adding missing keys from source.
    """
    result = target.copy()

    for key, value in source.items():
        if key in result:
            if isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_dictionaries(result[key], value)
            
        else:
            result[key] = value

    return result


def sync_locale_file(english_path: Path, target_path: Path, lang_code: str) -> bool:
    """
    Sync a single locale file with the English source.
    Returns True if the file was updated.
    """
    try:
        
        with open(english_path, "r", encoding="utf-8") as f:
            english_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"❌ Error reading English locale {english_path}: {e}")
        return False

    
    target_data = {}
    if target_path.exists():
        try:
            with open(target_path, "r", encoding="utf-8") as f:
                target_data = json.load(f)
        except json.JSONDecodeError as e:
            print(f"⚠️  Invalid JSON in {target_path}, recreating: {e}")

    
    updated_data = merge_dictionaries(target_data, english_data)

    
    if lang_code in TRANSLATIONS:
        for key, translation in TRANSLATIONS[lang_code].items():
            if key in english_data:  
                updated_data[key] = translation

    
    if updated_data != target_data:
        
        target_path.parent.mkdir(parents=True, exist_ok=True)

        
        with open(target_path, "w", encoding="utf-8") as f:
            json.dump(updated_data, f, indent=2, ensure_ascii=False)

        return True

    return False


def process_component(component_path: Path) -> int:
    """
    Process a single component directory for locale synchronization.
    Returns the number of files updated.
    """
    locales_dir = component_path / "locales"

    if not locales_dir.exists():
        return 0

    english_locale = locales_dir / "en" / "index.json"
    if not english_locale.exists():
        return 0

    updated_count = 0
    component_name = component_path.name

    for lang_code in TARGET_LANGUAGES:
        target_locale = locales_dir / lang_code / "index.json"

        if sync_locale_file(english_locale, target_locale, lang_code):
            print(f"  📝 Updated {component_name}/{lang_code}")
            updated_count += 1

    return updated_count


def main() -> None:
    """Main execution function."""
    components_root = Path("ui/src/components")

    if not components_root.exists():
        print(f"❌ Components directory not found: {components_root}")
        return

    print("🌐 Synchronizing component locale files...")

    total_updated = 0
    processed_components = 0

    
    for component_path in components_root.rglob("*"):
        if component_path.is_dir() and (component_path / "locales").exists():
            print(f"🔍 Processing: {component_path.relative_to(components_root)}")
            updated_count = process_component(component_path)
            total_updated += updated_count
            processed_components += 1

    print(f"\n🏁 Locale synchronization complete!")
    print(f"   📊 Processed {processed_components} components")
    print(f"   ✅ Updated {total_updated} locale files")


if __name__ == "__main__":
    main()
