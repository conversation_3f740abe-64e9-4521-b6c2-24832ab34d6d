"""
<PERSON><PERSON>t for translating a single string using Ollama API.
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path


try:

    from scripts.translation.translator import Translator, TranslationError
    from scripts.translation.cache_manager import CacheManager
except ImportError:
    try:

        from .translator import Translator, TranslationError
        from .cache_manager import CacheManager
    except ImportError:

        script_dir = Path(__file__).parent.absolute()
        if str(script_dir.parent) not in sys.path:
            sys.path.insert(0, str(script_dir.parent))

        from translator import Translator, TranslationError
        from cache_manager import CacheManager


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("translate_string")


def translate_string(
    text: str,
    source_lang: str = "en",
    target_lang: str = "ar",
    model: str = None,
    cache_enabled: bool = True,
    simulated: bool = None,
) -> str:
    """
    Translate a single string using Ollama API with optional caching.

    Args:
        text: String to translate
        source_lang: Source language code
        target_lang: Target language code
        model: Ollama model to use
        cache_enabled: Whether to use caching
        simulated: Whether to use simulated translations instead of real API

    Returns:
        Translated string
    """
    try:

        cache = None
        if cache_enabled:
            cache = CacheManager(target_lang=target_lang, enabled=True)
            cached_translation = cache.get(text)
            if cached_translation:
                logger.info("Translation found in cache")
                if cache:
                    cache.close()
                return cached_translation

        translator = Translator(
            source_lang=source_lang,
            target_lang=target_lang,
            model=model,
            simulated=simulated,
        )

        translated_text = translator.translate_text(text)

        if cache_enabled and cache:
            cache.set(text, translated_text)
            cache.close()

        return translated_text

    except TranslationError as e:
        logger.error(f"Translation error: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise


def main():
    """Parse command line arguments and run the translation."""
    parser = argparse.ArgumentParser(
        description="Translate a single string using Ollama API"
    )
    parser.add_argument("--text", "-t", required=True, help="Text to translate")
    parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    parser.add_argument(
        "--target-language", "-l", default="ar", help="Target language code"
    )
    parser.add_argument("--model", "-m", help="Ollama model to use")
    parser.add_argument(
        "--no-cache", action="store_true", help="Disable translation caching"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument(
        "--simulation",
        "-sim",
        action="store_true",
        help="Use simulation mode (no API calls)",
    )
    parser.add_argument(
        "--force-api",
        action="store_true",
        help="Force using real API even when running locally",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    use_simulation = args.simulation

    if not use_simulation and not args.force_api:

        if not os.environ.get("DOCKER_CONTAINER"):
            logger.info(
                "Running locally, using simulation mode. Use --force-api to use real API."
            )
            use_simulation = True

    try:

        start_time = time.time()
        logger.info("Beginning translation...")
        result = translate_string(
            text=args.text,
            source_lang=args.source_language,
            target_lang=args.target_language,
            model=args.model,
            cache_enabled=not args.no_cache,
            simulated=use_simulation,
        )

        print("\nResult:")
        print(f"Original ({args.source_language}): {args.text}")
        print(f"Translated ({args.target_language}): {result}")
        if use_simulation:
            print(
                "Note: Using simulation mode - translation is not from the actual API"
            )
        logger.info(f"Translation completed in {time.time() - start_time:.2f} seconds")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
