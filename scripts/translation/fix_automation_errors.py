
"""
Fix Automation Errors Script
Fix syntax errors introduced by the internationalization automation
where string literals were incorrectly converted to {t()} calls
"""

import os
import re
from pathlib import Path
from typing import List, <PERSON><PERSON>


def fix_automation_errors():
    """Fix all syntax errors introduced by the automation."""

    ui_dir = Path("ui/src")

    
    error_patterns = [
        
        (r"= \{t\(\'([^\']+)\'\)\}", r"= '\1'"),
        
        (r"variant=\{t\(\'([^\']+)\'\)\}", r"variant='\1'"),
        (r"variant = \{t\(\'([^\']+)\'\)\}", r"variant = '\1'"),
        
        (r"position=\{t\(\'([^\']+)\'\)\}", r"position='\1'"),
        (r"position = \{t\(\'([^\']+)\'\)\}", r"position = '\1'"),
        
        (r"align=\{t\(\'([^\']+)\'\)\}", r"align='\1'"),
        (r"align = \{t\(\'([^\']+)\'\)\}", r"align = '\1'"),
        
        (r"side=\{t\(\'([^\']+)\'\)\}", r"side='\1'"),
        (r"side = \{t\(\'([^\']+)\'\)\}", r"side = '\1'"),
        
        (r"orientation=\{t\(\'([^\']+)\'\)\}", r"orientation='\1'"),
        (r"orientation = \{t\(\'([^\']+)\'\)\}", r"orientation = '\1'"),
        
        (r"=== \{t\(\'([^\']+)\'\)\}", r"=== '\1'"),
        (r"!== \{t\(\'([^\']+)\'\)\}", r"!== '\1'"),
        (r"== \{t\(\'([^\']+)\'\)\}", r"== '\1'"),
        (r"!= \{t\(\'([^\']+)\'\)\}", r"!= '\1'"),
        
        (r"= \{t\(\'([^\']+)\'\)\} \|", r"= '\1' |"),
        (r"\| \{t\(\'([^\']+)\'\)\}", r"| '\1'"),
        
        (r"className=\{t\(\'([^\']+)\'\)\}", r"className='\1'"),
        (r"className = \{t\(\'([^\']+)\'\)\}", r"className = '\1'"),
        
        (
            r"style\.\w+ = \{t\(\'([^\']+)\'\)\}",
            lambda m: f"style.{m.group(0).split('.')[1].split(' ')[0]} = '{m.group(1)}'",
        ),
        
        (r"direction === \{t\(\'([^\']+)\'\)\}", r"direction === '\1'"),
        
        (
            r"\{t\(\'([a-z-]+(?:\s+[a-z-]+)*)\'\)\}",
            lambda m: f"'{m.group(1)}'" if is_css_class(m.group(1)) else m.group(0),
        ),
    ]

    fixed_files = []

    for tsx_file in ui_dir.rglob("*.tsx"):
        if tsx_file.name.endswith(".test.tsx") or tsx_file.name.endswith(
            ".stories.tsx"
        ):
            continue

        try:
            content = tsx_file.read_text(encoding="utf-8")
            original_content = content

            
            for pattern, replacement in error_patterns:
                if callable(replacement):
                    content = re.sub(pattern, replacement, content)
                else:
                    content = re.sub(pattern, replacement, content)

            
            if content != original_content:
                tsx_file.write_text(content, encoding="utf-8")
                fixed_files.append(tsx_file)
                print(f"Fixed: {tsx_file.relative_to(Path('ui/src'))}")

        except Exception as e:
            print(f"Error processing {tsx_file}: {e}")

    
    fix_auth_layout_export()
    fix_translation_hook_issues()

    print(f"\n✅ Fixed {len(fixed_files)} files")
    return fixed_files


def is_css_class(text: str) -> bool:
    """Check if text looks like CSS classes."""
    css_patterns = [
        r"^[a-z-]+$",  
        r"^[a-z-]+(?:\s+[a-z-]+)*$",  
        r"flex|grid|text-|bg-|border-|rounded|shadow|p-|m-|w-|h-",  
    ]

    for pattern in css_patterns:
        if re.match(pattern, text):
            return True
    return False


def fix_auth_layout_export():
    """Fix the auth layout export issue."""
    auth_layout_index = Path("ui/src/features/auth/layouts/index.ts")

    if auth_layout_index.exists():
        try:
            content = auth_layout_index.read_text(encoding="utf-8")
            
            content = re.sub(
                r"export \{ default as auth-layout \}",
                "export { default as AuthLayout }",
                content,
            )
            auth_layout_index.write_text(content, encoding="utf-8")
            print(f"Fixed auth layout export")
        except Exception as e:
            print(f"Error fixing auth layout: {e}")


def fix_translation_hook_issues():
    """Fix issues in translation utility files."""

    
    hook_file = Path("ui/src/hooks/useComponentTranslation.ts")
    if hook_file.exists():
        try:
            content = hook_file.read_text(encoding="utf-8")

            
            
            jsx_pattern = r'<div className="[^"]*">.*?</div>'
            content = re.sub(
                jsx_pattern, "// Loading component removed", content, flags=re.DOTALL
            )

            
            content = re.sub(
                r"<[^>]+>.*?</[^>]+>", "// JSX removed", content, flags=re.DOTALL
            )

            hook_file.write_text(content, encoding="utf-8")
            print(f"Fixed translation hook")
        except Exception as e:
            print(f"Error fixing translation hook: {e}")

    
    utils_file = Path("ui/src/utils/translationUtils.ts")
    if utils_file.exists():
        try:
            content = utils_file.read_text(encoding="utf-8")

            
            content = re.sub(
                r"<div>Loading translations\.\.\.</div>",
                '"Loading translations..."',
                content,
            )
            content = re.sub(
                r"<Component \{\.\.\.props\} />", "// Component render removed", content
            )

            utils_file.write_text(content, encoding="utf-8")
            print(f"Fixed translation utils")
        except Exception as e:
            print(f"Error fixing translation utils: {e}")


if __name__ == "__main__":
    fix_automation_errors()
