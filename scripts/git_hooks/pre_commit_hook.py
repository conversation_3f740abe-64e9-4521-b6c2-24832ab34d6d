
"""
Pre-commit Hook

Integrated git pre-commit hook that runs code formatting and comment cleaning
optimized for speed and reliability in development workflows.
"""

import os
import sys
import time
import subprocess
from pathlib import Path


def get_staged_files():
    """Get list of staged files."""
    try:
        result = subprocess.run(
            ["git", "diff", "--cached", "--name-only"],
            capture_output=True,
            text=True,
            check=True,
            timeout=5
        )
        return [f.strip() for f in result.stdout.split('\n') if f.strip()]
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
        return []


def run_code_formatter(staged_files):
    """Run the Python code formatter on staged files."""
    script_dir = Path(__file__).parent.parent
    formatter_script = script_dir / "format_code" / "code_formatter.py"
    
    if not formatter_script.exists():
        print(f"⚠️  Formatter script not found: {formatter_script}")
        return True  
    
    
    relevant_files = [
        f for f in staged_files
        if f.endswith(('.py', '.js', '.ts', '.tsx', '.jsx', '.html', '.css', '.scss', '.yml', '.yaml'))
        and Path(f).exists()
    ]
    
    if not relevant_files:
        return True
    
    print(f"🔧 Formatting {len(relevant_files)} code files...")
    
    try:
        result = subprocess.run(
            ["python3", str(formatter_script), "--staged", "--quiet"],
            capture_output=True,
            text=True,
            timeout=8,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ Code formatting completed")
            return True
        else:
            print(f"⚠️  Code formatting had warnings: {result.stderr}")
            return True  
            
    except subprocess.TimeoutExpired:
        print("⚠️  Code formatting timed out, continuing with commit")
        return True
    except Exception as e:
        print(f"⚠️  Code formatting failed: {e}")
        return True


def run_comment_cleaner(staged_files):
    """Run the comment cleaner on staged files."""
    script_dir = Path(__file__).parent.parent
    cleaner_script = script_dir / "clean_comments" / "clean_comments.py"
    
    if not cleaner_script.exists():
        print(f"⚠️  Comment cleaner not found: {cleaner_script}")
        return True  
    
    
    relevant_files = [
        f for f in staged_files
        if f.endswith(('.py', '.js', '.ts', '.tsx', '.jsx', '.html', '.css', '.scss'))
        and Path(f).exists()
    ]
    
    if not relevant_files:
        return True
    
    print(f"🧹 Cleaning comments in {len(relevant_files)} files...")
    
    try:
        result = subprocess.run(
            ["python3", str(cleaner_script)],
            capture_output=True,
            text=True,
            timeout=5,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ Comment cleaning completed")
            return True
        else:
            print(f"⚠️  Comment cleaning had warnings: {result.stderr}")
            return True  
            
    except subprocess.TimeoutExpired:
        print("⚠️  Comment cleaning timed out, continuing with commit")
        return True
    except Exception as e:
        print(f"⚠️  Comment cleaning failed: {e}")
        return True


def restage_modified_files(original_staged_files):
    """Re-stage files that may have been modified by formatters."""
    try:
        
        result = subprocess.run(
            ["git", "diff", "--name-only"] + original_staged_files,
            capture_output=True,
            text=True,
            timeout=3,
            check=True
        )
        
        modified_files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
        
        if modified_files:
            print(f"📝 Re-staging {len(modified_files)} modified files...")
            subprocess.run(
                ["git", "add"] + modified_files,
                timeout=5,
                check=True
            )
            
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        print(f"⚠️  Failed to re-stage files: {e}")


def main():
    """Main pre-commit hook function."""
    start_time = time.time()
    
    print("🚀 Running pre-commit hooks...")
    
    
    staged_files = get_staged_files()
    if not staged_files:
        print("ℹ️  No staged files to process")
        return 0
    
    print(f"📁 Processing {len(staged_files)} staged files")
    
    
    success = True
    
    
    if not run_code_formatter(staged_files):
        success = False
    
    
    if not run_comment_cleaner(staged_files):
        success = False
    
    
    restage_modified_files(staged_files)
    
    execution_time = time.time() - start_time
    
    if success:
        print(f"✅ Pre-commit hooks completed successfully in {execution_time:.1f}s")
        return 0
    else:
        print(f"❌ Pre-commit hooks failed in {execution_time:.1f}s")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 