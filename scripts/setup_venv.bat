@echo off
:: <PERSON><PERSON><PERSON> to create and set up a virtual environment for scripts on Windows

echo Setting up Python virtual environment for scripts...

:: Check if Python is installed
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please install Python 3.9 or higher.
    exit /b 1
)

:: Check Python version
for /f "tokens=2" %%a in ('python --version 2^>^&1') do set PYTHON_VERSION=%%a
echo Found Python version: %PYTHON_VERSION%

:: Determine script directory
set SCRIPT_DIR=%~dp0
set SCRIPT_DIR=%SCRIPT_DIR:~0,-1%
set VENV_DIR=%SCRIPT_DIR%\venv

:: Create virtual environment if it doesn't exist
if not exist "%VENV_DIR%" (
    echo Creating virtual environment in %VENV_DIR%
    python -m venv "%VENV_DIR%"
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to create virtual environment.
        exit /b 1
    )
) else (
    echo Virtual environment already exists at %VENV_DIR%
)

:: Activate virtual environment
echo Activating virtual environment...
call "%VENV_DIR%\Scripts\activate.bat"

if %ERRORLEVEL% neq 0 (
    echo Failed to activate virtual environment.
    exit /b 1
)

:: Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

:: Install requirements
echo Installing dependencies from %SCRIPT_DIR%\requirements.txt
pip install -r "%SCRIPT_DIR%\requirements.txt"

if %ERRORLEVEL% neq 0 (
    echo Failed to install requirements.
    exit /b 1
)

:: Install development tools
set /p INSTALL_DEV="Do you want to install development dependencies? (y/n) "
if /i "%INSTALL_DEV%"=="y" (
    echo Installing development dependencies...
    pip install black isort mypy flake8 pytest pytest-cov
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to install development dependencies.
        exit /b 1
    )
)

:: Check for project-specific requirements
if exist "%SCRIPT_DIR%\translation\requirements.txt" (
    echo Installing translation module dependencies...
    pip install -r "%SCRIPT_DIR%\translation\requirements.txt"
)

echo Virtual environment setup complete!
echo.
echo To activate the virtual environment, run:
echo %VENV_DIR%\Scripts\activate
echo.
echo To deactivate the virtual environment, run:
echo deactivate

:: Keep the window open
pause 