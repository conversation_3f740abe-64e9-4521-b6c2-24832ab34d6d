"""
Convenience wrapper for the translation module.
This script allows running the translation functionality from anywhere.
"""

import os
import sys
import argparse
from pathlib import Path


SCRIPT_DIR = Path(__file__).parent.absolute()
if str(SCRIPT_DIR) not in sys.path:
    sys.path.insert(0, str(SCRIPT_DIR))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Translation utilities")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    string_parser = subparsers.add_parser("string", help="Translate a single string")
    string_parser.add_argument("--text", "-t", required=True, help="Text to translate")
    string_parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    string_parser.add_argument(
        "--target-language", "-l", default="ar", help="Target language code"
    )
    string_parser.add_argument("--model", "-m", help="Ollama model to use")
    string_parser.add_argument(
        "--no-cache", action="store_true", help="Disable translation caching"
    )
    string_parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    string_parser.add_argument(
        "--simulation", "-sim", action="store_true", help="Use simulation mode"
    )
    string_parser.add_argument(
        "--force-api", action="store_true", help="Force using real API"
    )

    file_parser = subparsers.add_parser("file", help="Translate a .po file")
    file_parser.add_argument("--file", "-f", required=True, help="Path to the .po file")
    file_parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    file_parser.add_argument("--target-language", "-l", help="Target language code")
    file_parser.add_argument(
        "--batch-size", "-b", type=int, default=10, help="Batch size"
    )
    file_parser.add_argument("--force", action="store_true", help="Force retranslation")
    file_parser.add_argument("--model", "-m", help="Ollama model to use")
    file_parser.add_argument("--no-cache", action="store_true", help="Disable cache")
    file_parser.add_argument(
        "--verbose", "-v", action="store_true", help="Verbose logging"
    )
    file_parser.add_argument(
        "--simulation", "-sim", action="store_true", help="Use simulation mode"
    )

    all_parser = subparsers.add_parser(
        "all", help="Translate all .po files for a language"
    )
    all_parser.add_argument(
        "--language", "-l", required=True, help="Target language code"
    )
    all_parser.add_argument("--base-dir", "-d", default=".", help="Base directory")
    all_parser.add_argument(
        "--source-language", "-s", default="en", help="Source language code"
    )
    all_parser.add_argument(
        "--batch-size", "-b", type=int, default=10, help="Batch size"
    )
    all_parser.add_argument("--force", action="store_true", help="Force retranslation")
    all_parser.add_argument("--model", "-m", help="Ollama model to use")
    all_parser.add_argument("--no-cache", action="store_true", help="Disable cache")
    all_parser.add_argument(
        "--verbose", "-v", action="store_true", help="Verbose logging"
    )
    all_parser.add_argument(
        "--simulation", "-sim", action="store_true", help="Use simulation mode"
    )

    args = parser.parse_args()

    if args.command == "string":

        from translation.translate_string import main as string_main

        sys.argv = [
            sys.argv[0],
            "--text",
            args.text,
            "--source-language",
            args.source_language,
            "--target-language",
            args.target_language,
        ]

        if args.model:
            sys.argv.extend(["--model", args.model])
        if args.no_cache:
            sys.argv.append("--no-cache")
        if args.verbose:
            sys.argv.append("--verbose")
        if args.simulation:
            sys.argv.append("--simulation")
        if args.force_api:
            sys.argv.append("--force-api")

        string_main()

    elif args.command == "file":

        from translation.translate_file import main as file_main

        sys.argv = [sys.argv[0], "--file", args.file]

        if args.source_language:
            sys.argv.extend(["--source-language", args.source_language])
        if args.target_language:
            sys.argv.extend(["--target-language", args.target_language])
        if args.batch_size:
            sys.argv.extend(["--batch-size", str(args.batch_size)])
        if args.force:
            sys.argv.append("--force")
        if args.model:
            sys.argv.extend(["--model", args.model])
        if args.no_cache:
            sys.argv.append("--no-cache")
        if args.verbose:
            sys.argv.append("--verbose")
        if args.simulation:

            os.environ["TRANSLATION_SIMULATED"] = "true"

        file_main()

    elif args.command == "all":

        from translation.translate_all import main as all_main

        sys.argv = [sys.argv[0], "--language", args.language]

        if args.base_dir:
            sys.argv.extend(["--base-dir", args.base_dir])
        if args.source_language:
            sys.argv.extend(["--source-language", args.source_language])
        if args.batch_size:
            sys.argv.extend(["--batch-size", str(args.batch_size)])
        if args.force:
            sys.argv.append("--force")
        if args.model:
            sys.argv.extend(["--model", args.model])
        if args.no_cache:
            sys.argv.append("--no-cache")
        if args.verbose:
            sys.argv.append("--verbose")
        if args.simulation:

            os.environ["TRANSLATION_SIMULATED"] = "true"

        all_main()

    else:
        parser.print_help()
        sys.exit(1)
