
"""
Component Restructure Script

This script converts all components in ui/src/components to follow the proper directory structure:
component-name/
├── component-name.tsx
├── component-name.stories.tsx
├── component-name.test.tsx
└── index.ts
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List


class ComponentRestructurer:
    def __init__(self, base_path: str = "ui/src/components"):
        self.base_path = Path(base_path)
        self.components_to_convert = []

    def find_components_to_convert(self) -> List[Dict[str, str]]:
        """Find all .tsx files that need to be converted to proper structure"""
        components = []

        
        target_dirs = [
            "forum",
            "mobile",
            "notifications",
            "performance",
            "pwa",
            "realtime",
            "debug",
            "hoc",
            "common",
            "user-card",
        ]

        for dir_name in target_dirs:
            dir_path = self.base_path / dir_name
            if dir_path.exists():
                for tsx_file in dir_path.rglob("*.tsx"):
                    
                    if not any(x in tsx_file.name for x in [".test.", ".stories."]):
                        component_name = self.get_component_name_from_file(tsx_file)
                        if component_name:
                            components.append(
                                {
                                    "original_path": str(tsx_file),
                                    "component_name": component_name,
                                    "kebab_name": self.to_kebab_case(component_name),
                                    "directory": dir_name,
                                }
                            )

        return components

    def get_component_name_from_file(self, file_path: Path) -> str:
        """Extract component name from file"""
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            
            matches = re.findall(r"export (?:function|const) (\w+)", content)
            if matches:
                return matches[0]

            
            return file_path.stem
        except:
            return file_path.stem

    def to_kebab_case(self, name: str) -> str:
        """Convert PascalCase to kebab-case"""
        
        s1 = re.sub("(.)([A-Z][a-z]+)", r"\1-\2", name)
        return re.sub("([a-z0-9])([A-Z])", r"\1-\2", s1).lower()

    def create_stories_content(self, component_name: str, kebab_name: str) -> str:
        """Generate Storybook stories content"""
        return f"""import type {{ Meta, StoryObj }} from '@storybook/react';
import {{ {component_name} }} from './{kebab_name}';

const meta: Meta<typeof {component_name}> = {{
  title: 'Components/{component_name}',
  component: {component_name},
  parameters: {{
    layout: 'padded',
    docs: {{
      description: {{
        component: '{component_name} component with Shadcn UI styling.',
      }},
    }},
  }},
  argTypes: {{
    className: {{
      control: 'text',
      description: 'Additional CSS classes to apply to the component',
    }},
  }},
}};

export default meta;
type Story = StoryObj<typeof {component_name}>;

export const Default: Story = {{
  args: {{}},
}};

export const WithCustomStyling: Story = {{
  args: {{
    className: 'border-2 border-dashed border-gray-300',
  }},
}};
"""

    def create_test_content(self, component_name: str, kebab_name: str) -> str:
        """Generate Vitest test content"""
        return f"""import {{ describe, it, expect }} from 'vitest';
import {{ render, screen }} from '@testing-library/react';
import {{ {component_name} }} from './{kebab_name}';

describe('{component_name}', () => {{
  it('renders without crashing', () => {{
    render(<{component_name} />);
    expect(screen.getByRole('generic')).toBeInTheDocument();
  }});

  it('applies custom className when provided', () => {{
    const customClass = 'test-custom-class';
    render(<{component_name} className={{customClass}} />);
    const element = screen.getByRole('generic');
    expect(element).toHaveClass(customClass);
  }});
}});
"""

    def create_index_content(self, component_name: str, kebab_name: str) -> str:
        """Generate index.ts content"""
        return f"export {{ {component_name} }} from './{kebab_name}';\n"

    def convert_component(self, component_info: Dict[str, str]) -> bool:
        """Convert a single component to proper structure"""
        try:
            original_path = Path(component_info["original_path"])
            component_name = component_info["component_name"]
            kebab_name = component_info["kebab_name"]

            
            new_dir = self.base_path / kebab_name
            new_dir.mkdir(exist_ok=True)

            
            new_component_path = new_dir / f"{kebab_name}.tsx"
            shutil.copy2(original_path, new_component_path)

            
            stories_path = new_dir / f"{kebab_name}.stories.tsx"
            if not stories_path.exists():
                with open(stories_path, "w", encoding="utf-8") as f:
                    f.write(self.create_stories_content(component_name, kebab_name))

            
            test_path = new_dir / f"{kebab_name}.test.tsx"
            if not test_path.exists():
                with open(test_path, "w", encoding="utf-8") as f:
                    f.write(self.create_test_content(component_name, kebab_name))

            
            index_path = new_dir / "index.ts"
            with open(index_path, "w", encoding="utf-8") as f:
                f.write(self.create_index_content(component_name, kebab_name))

            print(f"✅ Converted {component_name} -> {kebab_name}/")
            return True

        except Exception as e:
            print(f"❌ Failed to convert {component_info['component_name']}: {e}")
            return False

    def run_conversion(self):
        """Run the full conversion process"""
        print("🔍 Finding components to convert...")
        components = self.find_components_to_convert()

        if not components:
            print("✅ No components found to convert!")
            return

        print(f"📦 Found {len(components)} components to convert:")
        for comp in components:
            print(f"  - {comp['component_name']} ({comp['original_path']})")

        print("\n🚀 Starting conversion...")
        success_count = 0

        for component in components:
            if self.convert_component(component):
                success_count += 1

        print(
            f"\n✨ Conversion complete! {success_count}/{len(components)} components converted successfully."
        )


if __name__ == "__main__":
    restructurer = ComponentRestructurer()
    restructurer.run_conversion()
