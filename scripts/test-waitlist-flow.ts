import { PrismaClient } from "@prisma/client";
import { z } from "zod";

const prisma = new PrismaClient();

// Test data schema matching the waitlist schema
const testSchema = z.object({
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  healthConditions: z.array(z.string()).optional(),
  dietaryGoals: z.array(z.string()).optional(),
  currentDiet: z.string().optional(),
  referralSource: z.string().optional(),
  interests: z.array(z.string()).optional(),
  priority: z.number().min(1).max(5).optional(),
});

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  data?: any;
}

class WaitlistFlowTester {
  private results: TestResult[] = [];
  private testData = [
    // Test 1: Minimal data (email only)
    {
      name: "Minimal Data Test",
      data: {
        email: "<EMAIL>",
      },
    },
    // Test 2: Complete data
    {
      name: "Complete Data Test",
      data: {
        email: "<EMAIL>",
        firstName: "<PERSON>",
        lastName: "Doe",
        healthConditions: ["Diabetes", "High Blood Pressure"],
        dietaryGoals: ["Weight Loss", "Improve Energy"],
        currentDiet: "Standard American Diet",
        referralSource: "Google Search",
        interests: ["Meal Tracking", "Symptom Tracking"],
        priority: 3,
      },
    },
    // Test 3: Partial data
    {
      name: "Partial Data Test",
      data: {
        email: "<EMAIL>",
        firstName: "Jane",
        healthConditions: ["Food Allergies"],
        dietaryGoals: ["Better Digestion"],
        priority: 2,
      },
    },
  ];

  async runTest(testName: string, testData: any): Promise<TestResult> {
    try {
      console.log(`\n🧪 Running: ${testName}`);
      console.log("📊 Test Data:", JSON.stringify(testData, null, 2));

      // Step 1: Validate data structure
      const validatedData = testSchema.parse(testData);
      console.log("✅ Data validation passed");

      // Step 2: Check if email already exists
      const existingEntry = await prisma.waitlistEntry.findUnique({
        where: { email: validatedData.email },
      });

      if (existingEntry) {
        // Clean up existing entry for test
        await prisma.waitlistEntry.delete({
          where: { email: validatedData.email },
        });
        console.log("🧹 Cleaned up existing test data");
      }

      // Step 3: Create waitlist entry
      const waitlistEntry = await prisma.waitlistEntry.create({
        data: {
          email: validatedData.email,
          firstName: validatedData.firstName || "",
          lastName: validatedData.lastName || "",
          healthConditions: validatedData.healthConditions || [],
          dietaryGoals: validatedData.dietaryGoals || [],
          currentDiet: validatedData.currentDiet || "",
          referralSource: validatedData.referralSource || "",
          interests: validatedData.interests || [],
          priority: validatedData.priority || 1,
        },
      });

      console.log("✅ Database entry created with ID:", waitlistEntry.id);

      // Step 4: Verify the entry was saved correctly
      const savedEntry = await prisma.waitlistEntry.findUnique({
        where: { id: waitlistEntry.id },
      });

      if (!savedEntry) {
        throw new Error("Entry was not found after creation");
      }

      // Step 5: Validate saved data matches input
      const dataMatches =
        savedEntry.email === validatedData.email &&
        savedEntry.firstName === (validatedData.firstName || "") &&
        savedEntry.lastName === (validatedData.lastName || "") &&
        JSON.stringify(savedEntry.healthConditions) ===
          JSON.stringify(validatedData.healthConditions || []) &&
        JSON.stringify(savedEntry.dietaryGoals) ===
          JSON.stringify(validatedData.dietaryGoals || []) &&
        savedEntry.currentDiet === (validatedData.currentDiet || "") &&
        savedEntry.referralSource === (validatedData.referralSource || "") &&
        JSON.stringify(savedEntry.interests) ===
          JSON.stringify(validatedData.interests || []) &&
        savedEntry.priority === (validatedData.priority || 1);

      if (!dataMatches) {
        throw new Error("Saved data does not match input data");
      }

      console.log("✅ Data integrity verified");
      console.log("💾 Saved Entry:", {
        id: savedEntry.id,
        email: savedEntry.email,
        firstName: savedEntry.firstName,
        lastName: savedEntry.lastName,
        healthConditions: savedEntry.healthConditions,
        dietaryGoals: savedEntry.dietaryGoals,
        currentDiet: savedEntry.currentDiet,
        referralSource: savedEntry.referralSource,
        interests: savedEntry.interests,
        priority: savedEntry.priority,
        createdAt: savedEntry.createdAt,
      });

      return {
        testName,
        passed: true,
        data: savedEntry,
      };
    } catch (error: any) {
      console.log("❌ Test failed:", error.message);
      return {
        testName,
        passed: false,
        error: error.message,
      };
    }
  }

  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Waitlist Flow Integration Tests");
    console.log("=".repeat(60));

    // Run all test cases
    for (const testCase of this.testData) {
      const result = await this.runTest(testCase.name, testCase.data);
      this.results.push(result);
    }

    // Additional edge case tests
    await this.testValidationErrors();
    await this.testDatabaseConstraints();

    // Generate report
    this.generateReport();
  }

  async testValidationErrors(): Promise<void> {
    console.log("\n🔍 Testing Validation Errors");

    const invalidTestCases = [
      {
        name: "Invalid Email Format",
        data: { email: "invalid-email" },
      },
      {
        name: "Missing Email",
        data: { firstName: "Test" },
      },
      {
        name: "Invalid Priority Range",
        data: { email: "<EMAIL>", priority: 10 },
      },
    ];

    for (const testCase of invalidTestCases) {
      try {
        testSchema.parse(testCase.data);
        this.results.push({
          testName: testCase.name,
          passed: false,
          error: "Validation should have failed but passed",
        });
      } catch (error) {
        console.log(`✅ ${testCase.name}: Properly rejected`);
        this.results.push({
          testName: testCase.name,
          passed: true,
        });
      }
    }
  }

  async testDatabaseConstraints(): Promise<void> {
    console.log("\n🛡️  Testing Database Constraints");

    try {
      // Test email uniqueness constraint
      const testEmail = "<EMAIL>";

      // Clean up any existing entry
      await prisma.waitlistEntry.deleteMany({
        where: { email: testEmail },
      });

      // Create first entry
      await prisma.waitlistEntry.create({
        data: {
          email: testEmail,
          firstName: "First",
          lastName: "Entry",
        },
      });

      // Try to create duplicate - should fail
      try {
        await prisma.waitlistEntry.create({
          data: {
            email: testEmail,
            firstName: "Duplicate",
            lastName: "Entry",
          },
        });

        this.results.push({
          testName: "Database Unique Constraint",
          passed: false,
          error: "Duplicate email was allowed when it should be rejected",
        });
      } catch (constraintError: any) {
        if (constraintError.code === "P2002") {
          console.log("✅ Database unique constraint working properly");
          this.results.push({
            testName: "Database Unique Constraint",
            passed: true,
          });
        } else {
          throw constraintError;
        }
      }
    } catch (error: any) {
      this.results.push({
        testName: "Database Unique Constraint",
        passed: false,
        error: error.message,
      });
    }
  }

  generateReport(): void {
    console.log("\n📊 Test Results Summary");
    console.log("=".repeat(60));

    const passed = this.results.filter((r) => r.passed).length;
    const failed = this.results.filter((r) => !r.passed).length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log("\n❌ Failed Tests:");
      this.results
        .filter((r) => !r.passed)
        .forEach((result) => {
          console.log(`  • ${result.testName}: ${result.error}`);
        });
    }

    console.log("\n✅ Passed Tests:");
    this.results
      .filter((r) => r.passed)
      .forEach((result) => {
        console.log(`  • ${result.testName}`);
      });

    if (failed === 0) {
      console.log(
        "\n🎉 All tests passed! Waitlist functionality is working correctly.",
      );
      console.log("✅ Data is being properly saved to the database.");
      console.log("✅ Validation is working as expected.");
      console.log("✅ Database constraints are properly enforced.");
    } else {
      console.log("\n⚠️  Some tests failed. Please review the issues above.");
      process.exit(1);
    }
  }

  async cleanup(): Promise<void> {
    console.log("\n🧹 Cleaning up test data...");

    const testEmails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];

    for (const email of testEmails) {
      try {
        await prisma.waitlistEntry.deleteMany({
          where: { email },
        });
      } catch (error) {
        // Ignore cleanup errors
      }
    }

    console.log("✅ Cleanup completed");
  }
}

// Main execution
async function main() {
  const tester = new WaitlistFlowTester();

  try {
    await tester.runAllTests();
  } finally {
    await tester.cleanup();
    await prisma.$disconnect();
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Test interrupted by user");
  await prisma.$disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Test terminated");
  await prisma.$disconnect();
  process.exit(0);
});

if (require.main === module) {
  main().catch((error) => {
    console.error("💥 Test execution failed:", error);
    process.exit(1);
  });
}

export { WaitlistFlowTester };
