# Docker Management Utility

A comprehensive utility for managing Docker operations with pre-configured commands, environment handling, and integration with the development workflow. This tool simplifies Docker usage for Django/React applications.

## Features

- **Container Management**: Simplified commands for building, starting, and stopping containers
- **Environment Configuration**: Handles environment variables for different deployment scenarios
- **Docker Compose Integration**: Manages multi-container applications with compose commands
- **Volume Management**: Controls data persistence with volume commands
- **Image Cleanup**: Tools for cleaning up unused images and optimizing storage
- **Logs Management**: Centralized access to container logs
- **Health Checks**: Built-in container health monitoring

## Installation

No additional installation required beyond Python, PyYAML, and Docker.

## Usage

### Basic Usage

```bash
# Start all containers
python docker/docker.py up

# Build images
python docker/docker.py build

# Stop all containers
python docker/docker.py down

# View container logs
python docker/docker.py logs api
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--env`, `-e` | Environment to use (dev, test, prod) |
| `--service`, `-s` | Specific service to target |
| `--verbose`, `-v` | Show detailed output |
| `--detach`, `-d` | Run containers in detached mode |
| `--build` | Build images before starting containers |
| `--config`, `-c` | Path to custom configuration file |

## Commands

| Command | Description |
|---------|-------------|
| `up` | Start containers |
| `down` | Stop containers |
| `build` | Build container images |
| `restart` | Restart containers |
| `logs` | View container logs |
| `ps` | List containers |
| `exec` | Execute command in a container |
| `clean` | Remove unused containers/images |
| `volumes` | Manage volumes |
| `networks` | Manage networks |
| `health` | Check container health |

## Configuration

The Docker utility uses a YAML configuration file with settings for different environments:

```yaml
# Environment configurations
environments:
  dev:
    compose_file: "docker-compose.yml"
    env_file: ".env.dev"
    build_args:
      DEBUG: "true"
  prod:
    compose_file: "docker-compose.prod.yml"
    env_file: ".env.prod"
    build_args:
      DEBUG: "false"

# Service configurations
services:
  api:
    container_name: "api_container"
    health_check:
      endpoint: "/health/"
      expected_status: 200
  ui:
    container_name: "ui_container"
    health_check:
      endpoint: "/"
      expected_status: 200
  db:
    container_name: "db_container"
    volumes:
      - "db_data:/var/lib/postgresql/data"

# Volume configurations
volumes:
  db_data:
    driver: "local"
  media_files:
    driver: "local"

# Cleanup settings
cleanup:
  remove_orphans: true
  prune_volumes: false
  prune_images: true
  keep_latest: 3
```

### Configuration Sections

| Section | Description |
|---------|-------------|
| `environments` | Settings for different deployment environments |
| `services` | Container-specific configurations |
| `volumes` | Data persistence settings |
| `cleanup` | Image and container cleanup policies |

## Examples

### Start Development Environment

```bash
python docker/docker.py up --env dev
```

### Execute Command in API Container

```bash
python docker/docker.py exec api "python manage.py migrate"
```

### View Logs for UI Container

```bash
python docker/docker.py logs ui --tail 100
```

### Clean Up Docker Resources

```bash
python docker/docker.py clean --prune-images
```

## Common Workflows

### Initial Setup

```bash
# Build and start development environment
python docker/docker.py build --env dev
python docker/docker.py up --env dev
```

### Database Migration

```bash
python docker/docker.py exec api "python manage.py makemigrations"
python docker/docker.py exec api "python manage.py migrate"
```

### Deploying to Production

```bash
python docker/docker.py build --env prod
python docker/docker.py up --env prod --detach
```

## Integration with Other Scripts

This Docker utility integrates with other scripts in the collection:

1. Can be called by the commit script for pre-commit testing
2. Works with the venv script for consistent environment setup
3. Logs all activities to the central logging system

## Troubleshooting

If you encounter issues with Docker operations:

1. Check the log files in `scripts/logs/` directory
2. Verify Docker and Docker Compose are properly installed
3. Ensure all required environment variables are defined
4. Check container health with `docker/docker.py health`
5. Run with `--verbose` flag to see detailed Docker output 