


auto_rebuild: true
rebuild_on_file_change: true
cache_intermediate_layers: true


default_environment: development
environments:
  - name: development
    compose_file: docker-compose.yml
    build_args:
      NODE_ENV: development
      DJANGO_SETTINGS_MODULE: config.settings.dev
  - name: production
    compose_file: docker-compose.prod.yml
    build_args:
      NODE_ENV: production
      DJANGO_SETTINGS_MODULE: config.settings.prod


services:
  - name: backend
    auto_restart: true
    health_check: true
    port: 8747
  - name: frontend
    auto_restart: true
    health_check: true
    port: 3000
  - name: nginx
    auto_restart: true
    health_check: true
    port: 80


persist_volumes: true
clean_volumes_on_rebuild: false


log_level: info
capture_container_logs: true
log_rotation_size: 100M
max_log_files: 5
