generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model WaitlistEntry {
  id                String   @id @default(cuid())
  email             String   @unique
  firstName         String?
  lastName          String?
  healthConditions  String[]
  dietaryGoals      String[]
  currentDiet       String?
  referralSource    String?
  interests         String[]
  priority          Int      @default(1) // 1-5 priority score
  notificationsSent Int      @default(0)
  status            WaitlistStatus @default(ACTIVE)
  metadata          Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("waitlist_entries")
}

model WaitlistNotification {
  id          String   @id @default(cuid())
  email       String
  type        NotificationType
  subject     String
  content     String
  sentAt      DateTime @default(now())
  status      NotificationStatus @default(SENT)
  
  @@map("waitlist_notifications")
}

enum WaitlistStatus {
  ACTIVE
  INVITED
  CONVERTED
  UNSUBSCRIBED
}

enum NotificationType {
  WELCOME
  REMINDER
  LAUNCH_NOTIFICATION
  HEALTH_TIP
  PROGRESS_UPDATE
}

enum NotificationStatus {
  SENT
  FAILED
  PENDING
} 