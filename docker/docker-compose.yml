version: "3.8"

services:
  landing-page:
    container_name: landing_page_${NODE_ENV:-development}
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: ${TARGET:-development}
    ports:
      - "${PORT:-3222}:3222"
    volumes:
      - ../:/app
      - /app/node_modules
      - /app/.next
      - /app/.prisma
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-http://localhost:3222}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8747}
      - DATABASE_URL=****************************************************/landing_db?schema=public
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
      - CLERK_SECRET_KEY=
      - NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/sign-in
      - NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/sign-up
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard/overview
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard/overview
      - NEXT_PUBLIC_SENTRY_DISABLED=true
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - MAILGUN_SENDER_EMAIL=${MAILGUN_SENDER_EMAIL}
      - MAILGUN_DOMAIN=${MAILGUN_DOMAIN}
      - MAILGUN_API_HOST=${MAILGUN_API_HOST:-api.mailgun.net}
      - EMAIL_MOCK_MODE=${EMAIL_MOCK_MODE:-true}
      - WAITLIST_WELCOME_EMAIL_ENABLED=${WAITLIST_WELCOME_EMAIL_ENABLED:-true}
      - WAITLIST_NOTIFICATION_EMAIL=${WAITLIST_NOTIFICATION_EMAIL}
    restart: unless-stopped
    networks:
      - landing_network
    depends_on:
      landing_postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test:
        ["CMD", "wget", "--spider", "http://localhost:3222/api/health-check"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 45s

  landing_postgres:
    container_name: landing_postgres
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=landing_db
    ports:
      - "5434:5432"
    volumes:
      - landing_postgres_data:/var/lib/postgresql/data
    networks:
      - landing_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d landing_db"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:alpine
    container_name: landing_redis
    command: redis-server --appendonly yes
    volumes:
      - landing_redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - landing_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 3
      start_period: 10s

volumes:
  landing_node_modules:
    driver: local
  landing_postgres_data:
    driver: local
  landing_redis_data:
    driver: local

networks:
  landing_network:
    driver: bridge
    name: landing_network
