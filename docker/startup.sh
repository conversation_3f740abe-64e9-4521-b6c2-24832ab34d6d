#!/bin/bash
set -e

echo "🚀 Starting application with automatic database initialization..."

# Function to wait for database to be ready
wait_for_db() {
    echo "⏳ Waiting for database to be ready..."
    
    until npx prisma db push --skip-generate 2>/dev/null; do
        echo "⚠️  Database not ready yet, retrying in 5 seconds..."
        sleep 5
    done
    
    echo "✅ Database is ready and schema is synchronized!"
}

# Function to initialize database
init_database() {
    echo "🗄️  Initializing database..."
    
    # Generate Prisma client
    echo "📦 Generating Prisma client..."
    npx prisma generate
    
    # Wait for database and sync schema
    wait_for_db
    
    echo "✅ Database initialization completed!"
}

# Initialize database
init_database

# Start the application based on environment
if [ "$NODE_ENV" = "production" ]; then
    echo "🚀 Starting production server..."
    exec pnpm start
else
    echo "🚀 Starting development server..."
    exec pnpm dev
fi 