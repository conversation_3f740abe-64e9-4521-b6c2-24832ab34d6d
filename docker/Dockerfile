# syntax=docker/dockerfile:1.4

# === Base Stage ===
FROM node:18-bullseye-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openssl \
    wget \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# === Dependencies Stage ===
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json ./

# Install dependencies
RUN pnpm install

# === Development Stage ===
FROM base AS development
WORKDIR /app

# Copy deps from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Copy startup script and make it executable
COPY docker/startup.sh /usr/local/bin/startup.sh
RUN chmod +x /usr/local/bin/startup.sh

# Generate Prisma client (will be regenerated by startup script)
RUN npx prisma generate

# Expose port
EXPOSE 3222

# Use startup script instead of direct pnpm dev
CMD ["/usr/local/bin/startup.sh"]

# === Builder Stage ===
FROM base AS builder
WORKDIR /app

# Copy deps from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
ENV NEXT_TELEMETRY_DISABLED 1
RUN pnpm build

# === Production Stage ===
FROM base AS production
WORKDIR /app

# Copy package files and lock file from builder
COPY --from=builder /app/package.json ./
COPY --from=builder /app/pnpm-lock.yaml ./

# Install production dependencies
RUN pnpm install --prod --frozen-lockfile

# Copy built assets from builder stage
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/prisma ./prisma

# Copy startup script and make it executable
COPY docker/startup.sh /usr/local/bin/startup.sh
RUN chmod +x /usr/local/bin/startup.sh

# Generate Prisma client for production (will be regenerated by startup script)
RUN npx prisma generate

# Set environment variables
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Expose port
EXPOSE 3222

# Use startup script instead of direct pnpm start
CMD ["/usr/local/bin/startup.sh"] 