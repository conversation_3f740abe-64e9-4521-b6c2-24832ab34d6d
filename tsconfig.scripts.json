{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "outDir": "./dist", "rootDir": ".", "declaration": false, "sourceMap": true, "types": ["node"]}, "include": ["scripts/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "src/**/*", ".next/**/*"]}