# The Proper Human Diet - Landing Page Implementation Guide

## Project Overview

As a Senior TypeScript Developer with 20+ years of experience, you will create a high-performance, fully dockerized Next.js landing page for our **AI-powered carnivore diet and health tracking platform**. This platform helps people, especially those with autoimmune diseases, understand and improve their health through comprehensive data tracking, community support, and personalized guidance.

**The platform specializes in:**
- **Carnivore Diet Tracking & Education** - Complete nutrition monitoring for zero-carb lifestyles
- **Health Metrics & Biomarker Tracking** - Weight, body fat, blood pressure, inflammation markers, sleep quality
- **Symptom Management** - Track autoimmune symptoms, mood, energy levels, digestive health
- **Achievement System** - Gamified progress tracking with streaks, milestones, and community challenges
- **AI-Powered Meal Planning** - Personalized carnivore meal recommendations and recipe suggestions
- **Community Forum** - Support network for sharing experiences, recipes, and health victories
- **Progress Analytics** - Comprehensive dashboards showing health improvements over time

The landing page must be:
- Performant and SEO-optimized
- Visually compelling with modern design using **Shadcn/UI components**
- Fully dockerized and containerized
- Built with TypeScript, Next.js, Tailwind CSS, and Shadcn/UI
- Focused on **comprehensive waitlist functionality** with full interactivity
- Hopeful, empowering, and scientifically grounded in tone

---

## Phase 1: Docker Infrastructure Setup

### Step 1.1: Create Docker Configuration Files

**Create `landing-page/docker/Dockerfile`:**
```dockerfile
# === Build Stage ===
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package.json and lock file first for cache optimization
COPY package.json pnpm-lock.yaml ./

# Install dependencies using pnpm
RUN npm install -g pnpm && pnpm install

# Copy the rest of the application code
COPY . ./

# Build the Next.js app for production
RUN pnpm build

# === Production Stage ===
FROM node:18-alpine AS production

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy package.json and lock file
COPY package.json pnpm-lock.yaml ./

# Install only production dependencies
RUN pnpm install --prod --frozen-lockfile

# Copy built assets from builder stage
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/package.json ./

# Expose port 3000
EXPOSE 3000

# Start the application
CMD ["pnpm", "start"]

# === Development Stage ===
FROM node:18-alpine AS development

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy package.json and lock file
COPY package.json pnpm-lock.yaml ./

# Install all dependencies
RUN pnpm install

# Copy source code
COPY . ./

# Expose port 3000
EXPOSE 3000

# Start development server
CMD ["pnpm", "dev"]
```

### Step 1.2: Create Docker Compose Configuration

**Create `landing-page/docker/docker-compose.yml`:**
```yaml
services:
  landing-page:
    container_name: landing_page_dev
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: ${TARGET:-development}
    ports:
      - "${PORT:-3000}:3000"
    volumes:
      - ../:/app
      - landing_node_modules:/app/node_modules
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-http://localhost:3000}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8747}
      - DATABASE_URL=${DATABASE_URL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    env_file:
      - ../.env
    restart: unless-stopped
    networks:
      - landing_network
    depends_on:
      - db
      - redis

  db:
    image: postgres:15-alpine
    container_name: landing_postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-landing_db}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
    volumes:
      - landing_postgres_data:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - landing_network

  redis:
    image: redis:alpine
    container_name: landing_redis
    command: redis-server --appendonly yes
    volumes:
      - landing_redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - landing_network

volumes:
  landing_node_modules:
  landing_postgres_data:
  landing_redis_data:

networks:
  landing_network:
    driver: bridge
```

### Step 1.3: Create Environment Configuration

**Create `landing-page/.env.example`:**
```env
# Application Configuration
NODE_ENV=development
PORT=3000
TARGET=development

# Public Environment Variables (Next.js)
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8747
NEXT_PUBLIC_APP_NAME="The Proper Human Diet"
NEXT_PUBLIC_COMPANY_NAME="The Proper Human Diet"

# Database Configuration
DATABASE_URL=**************************************/landing_db
POSTGRES_DB=landing_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PORT=6379

# Email Service Configuration (for waitlist notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# Waitlist Configuration
WAITLIST_NOTIFICATION_EMAIL=<EMAIL>
WAITLIST_WELCOME_EMAIL_ENABLED=true
WAITLIST_ADMIN_NOTIFICATIONS=true

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=

# Waitlist Configuration
WAITLIST_NOTIFICATION_EMAIL=<EMAIL>
WAITLIST_WELCOME_EMAIL_ENABLED=true
WAITLIST_ADMIN_NOTIFICATIONS=true

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=
```

### Step 1.4: Create Makefile for Easy Development

**Create `landing-page/Makefile`:**
```makefile
# The Proper Human Diet - Landing Page Makefile

.PHONY: help build up down logs install dev build-prod clean restart shell db-migrate db-reset

# Default target
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Install dependencies
	docker compose -f docker/docker-compose.yml run --rm landing-page pnpm install

build: ## Build Docker images
	docker compose -f docker/docker-compose.yml build

up: ## Start development environment
	docker compose -f docker/docker-compose.yml up -d

down: ## Stop development environment
	docker compose -f docker/docker-compose.yml down

logs: ## Show logs
	docker compose -f docker/docker-compose.yml logs -f

dev: up ## Start development environment (alias for up)

build-prod: ## Build for production
	TARGET=production docker compose -f docker/docker-compose.yml build

clean: ## Clean up containers and volumes
	docker compose -f docker/docker-compose.yml down -v
	docker system prune -f

restart: down up ## Restart the development environment

shell: ## Open shell in container
	docker compose -f docker/docker-compose.yml exec landing-page sh

db-migrate: ## Run database migrations
	docker compose -f docker/docker-compose.yml exec landing-page npx prisma db push

db-reset: ## Reset database
	docker compose -f docker/docker-compose.yml exec landing-page npx prisma db push --force-reset

test: ## Run tests
	docker compose -f docker/docker-compose.yml exec landing-page pnpm test

lint: ## Run linting
	docker compose -f docker/docker-compose.yml exec landing-page pnpm lint

format: ## Format code
	docker compose -f docker/docker-compose.yml exec landing-page pnpm format

waitlist-export: ## Export waitlist data
	docker compose -f docker/docker-compose.yml exec landing-page node scripts/export-waitlist.js
```

---

## Phase 2: Next.js Project Configuration

### Step 2.1: Update package.json Scripts and Dependencies

**Update `landing-page/package.json`:**
```json
{
  "name": "the-proper-human-diet-landing",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "type-check": "tsc --noEmit",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:studio": "prisma studio"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "lucide-react": "^0.292.0",
    "framer-motion": "^10.16.0",
    "next-themes": "^0.2.1",
    "zod": "^3.22.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-avatar": "^1.0.4",
    "@radix-ui/react-toast": "^1.1.5",
    "@radix-ui/react-alert-dialog": "^1.0.5",
    "@radix-ui/react-progress": "^1.0.3",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "@prisma/client": "^5.6.0",
    "prisma": "^5.6.0",
    "nodemailer": "^6.9.7",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.2",
    "date-fns": "^2.30.0",
    "react-confetti": "^6.1.0",
    "sonner": "^1.2.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@types/nodemailer": "^6.4.14",
    "autoprefixer": "^10.0.0",
    "postcss": "^8.0.0",
    "prettier": "^3.0.0",
    "vitest": "^1.0.0",
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0"
  }
}
```

### Step 2.2: Configure Database Schema

**Create `landing-page/prisma/schema.prisma`:**
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model WaitlistEntry {
  id                String   @id @default(cuid())
  email             String   @unique
  firstName         String?
  lastName          String?
  healthConditions  String[]
  dietaryGoals      String[]
  currentDiet       String?
  referralSource    String?
  interests         String[]
  priority          Int      @default(1) // 1-5 priority score
  notificationsSent Int      @default(0)
  status            WaitlistStatus @default(ACTIVE)
  metadata          Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("waitlist_entries")
}

model WaitlistNotification {
  id          String   @id @default(cuid())
  email       String
  type        NotificationType
  subject     String
  content     String
  sentAt      DateTime @default(now())
  status      NotificationStatus @default(SENT)
  
  @@map("waitlist_notifications")
}

enum WaitlistStatus {
  ACTIVE
  INVITED
  CONVERTED
  UNSUBSCRIBED
}

enum NotificationType {
  WELCOME
  REMINDER
  LAUNCH_NOTIFICATION
  HEALTH_TIP
  PROGRESS_UPDATE
}

enum NotificationStatus {
  SENT
  FAILED
  PENDING
}
```

### Step 2.3: Configure TypeScript Paths

**Update `landing-page/tsconfig.json`:**
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"],
      "@/styles/*": ["./src/styles/*"],
      "@/prisma/*": ["./prisma/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

---

## Phase 3: Project Structure Setup

### Step 3.1: Create Directory Structure

Create the following directory structure within `landing-page/src/`:

```
src/
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   ├── page.tsx
│   ├── about/
│   │   └── page.tsx
│   ├── contact/
│   │   └── page.tsx
│   ├── privacy/
│   │   └── page.tsx
│   ├── terms/
│   │   └── page.tsx
│   ├── api/
│   │   ├── waitlist/
│   │   │   └── route.ts
│   │   ├── waitlist/
│   │   │   └── export/
│   │   │       └── route.ts
│   │   └── health-check/
│   │       └── route.ts
│   └── not-found.tsx
├── components/
│   ├── ui/           # Shadcn components (copy from main ui project)
│   ├── layout/
│   │   ├── navbar.tsx
│   │   ├── footer.tsx
│   │   └── theme-provider.tsx
│   ├── sections/
│   │   ├── hero-section.tsx
│   │   ├── features-section.tsx
│   │   ├── founder-story-section.tsx
│   │   ├── how-it-works-section.tsx
│   │   ├── testimonials-section.tsx
│   │   ├── health-benefits-section.tsx
│   │   ├── community-section.tsx
│   │   ├── achievements-preview-section.tsx
│   │   └── cta-section.tsx
│   ├── forms/
│   │   ├── waitlist-form.tsx
│   │   ├── detailed-waitlist-form.tsx
│   │   └── contact-form.tsx
│   └── interactive/
│       ├── health-calculator.tsx
│       ├── progress-simulator.tsx
│       └── feature-demo.tsx
├── lib/
│   ├── utils.ts
│   ├── constants.ts
│   ├── validations.ts
│   ├── email.ts
│   ├── prisma.ts
│   └── waitlist.ts
├── hooks/
│   ├── use-theme.ts
│   ├── use-waitlist.ts
│   ├── use-toast.ts
│   └── use-local-storage.ts
├── types/
│   ├── index.ts
│   └── waitlist.ts
└── styles/
    └── globals.css
```

---

## Phase 4: Enhanced Waitlist System Implementation

### Step 4.1: Create Advanced Waitlist Form Component

**Create `landing-page/src/components/forms/detailed-waitlist-form.tsx`:**
```typescript
"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { Loader2, Mail, CheckCircle, Heart, Brain, Users, TrendingUp, AlertCircle, Sparkles } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import Confetti from "react-confetti"
import { waitlistSchema, type WaitlistFormData } from "@/lib/validations"

interface DetailedWaitlistFormProps {
  trigger?: React.ReactNode
}

export function DetailedWaitlistForm({ trigger }: DetailedWaitlistFormProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [showConfetti, setShowConfetti] = useState(false)
  const [priorityScore, setPriorityScore] = useState(0)

  const form = useForm<WaitlistFormData>({
    resolver: zodResolver(waitlistSchema),
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      healthConditions: [],
      dietaryGoals: [],
      currentDiet: "",
      referralSource: "",
      interests: [],
    }
  })

  const totalSteps = 4
  const progress = (currentStep / totalSteps) * 100

  const healthConditions = [
    "Autoimmune Disease",
    "Diabetes",
    "Inflammatory Bowel Disease",
    "Arthritis",
    "Skin Conditions",
    "Digestive Issues",
    "Mental Health",
    "Weight Management",
    "Chronic Fatigue",
    "Other"
  ]

  const dietaryGoals = [
    "Weight Loss",
    "Reduce Inflammation",
    "Improve Energy",
    "Better Sleep",
    "Digestive Health",
    "Mental Clarity",
    "Athletic Performance",
    "General Health",
    "Autoimmune Management"
  ]

  const currentDiets = [
    "Standard American Diet",
    "Keto",
    "Paleo",
    "Carnivore (Beginner)",
    "Carnivore (Experienced)",
    "Vegetarian/Vegan",
    "Mediterranean",
    "Other"
  ]

  const interests = [
    "Meal Planning",
    "Recipe Ideas",
    "Health Tracking",
    "Community Support",
    "Expert Guidance",
    "Progress Analytics",
    "Achievement System",
    "Educational Content"
  ]

  const calculatePriorityScore = (data: Partial<WaitlistFormData>) => {
    let score = 1
    
    // Higher priority for autoimmune conditions
    if (data.healthConditions?.includes("Autoimmune Disease")) score += 2
    if (data.healthConditions?.includes("Inflammatory Bowel Disease")) score += 2
    
    // Higher priority for experienced carnivore users
    if (data.currentDiet?.includes("Carnivore")) score += 1
    
    // Higher priority for multiple health goals
    if ((data.dietaryGoals?.length || 0) >= 3) score += 1
    
    return Math.min(score, 5)
  }

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
      const score = calculatePriorityScore(form.getValues())
      setPriorityScore(score)
    }
  }

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const onSubmit = async (data: WaitlistFormData) => {
    setIsLoading(true)

    try {
      const response = await fetch("/api/waitlist", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          priority: priorityScore,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Failed to join waitlist")
      }

      setIsSuccess(true)
      setShowConfetti(true)
      
      // Hide confetti after 5 seconds
      setTimeout(() => setShowConfetti(false), 5000)
      
      toast.success("Welcome to the waitlist!", {
        description: "We'll notify you when we launch with early access.",
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Something went wrong"
      
      toast.error("Failed to join waitlist", {
        description: errorMessage,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    form.reset()
    setCurrentStep(1)
    setIsSuccess(false)
    setIsLoading(false)
    setShowConfetti(false)
    setPriorityScore(0)
    setIsOpen(false)
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="text-center space-y-2">
              <Heart className="h-12 w-12 text-primary mx-auto" />
              <h3 className="text-lg font-semibold">Let's Get Started</h3>
              <p className="text-sm text-muted-foreground">
                Tell us a bit about yourself to personalize your experience
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    {...form.register("firstName")}
                    placeholder="John"
                  />
                  {form.formState.errors.firstName && (
                    <p className="text-sm text-destructive mt-1">
                      {form.formState.errors.firstName.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    {...form.register("lastName")}
                    placeholder="Doe"
                  />
                  {form.formState.errors.lastName && (
                    <p className="text-sm text-destructive mt-1">
                      {form.formState.errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>
              
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register("email")}
                  placeholder="<EMAIL>"
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-destructive mt-1">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        )

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="text-center space-y-2">
              <Brain className="h-12 w-12 text-primary mx-auto" />
              <h3 className="text-lg font-semibold">Health Background</h3>
              <p className="text-sm text-muted-foreground">
                Help us understand your health journey (optional but helpful)
              </p>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label>Current Health Conditions (Select all that apply)</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {healthConditions.map((condition) => (
                    <div key={condition} className="flex items-center space-x-2">
                      <Checkbox
                        id={condition}
                        checked={form.watch("healthConditions")?.includes(condition)}
                        onCheckedChange={(checked) => {
                          const current = form.getValues("healthConditions") || []
                          if (checked) {
                            form.setValue("healthConditions", [...current, condition])
                          } else {
                            form.setValue("healthConditions", current.filter(c => c !== condition))
                          }
                        }}
                      />
                      <Label htmlFor={condition} className="text-sm">
                        {condition}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label htmlFor="currentDiet">Current Diet Approach</Label>
                <Select
                  value={form.watch("currentDiet") || ""}
                  onValueChange={(value) => form.setValue("currentDiet", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select your current diet" />
                  </SelectTrigger>
                  <SelectContent>
                    {currentDiets.map((diet) => (
                      <SelectItem key={diet} value={diet}>
                        {diet}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </motion.div>
        )

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="text-center space-y-2">
              <TrendingUp className="h-12 w-12 text-primary mx-auto" />
              <h3 className="text-lg font-semibold">Your Goals</h3>
              <p className="text-sm text-muted-foreground">
                What are you hoping to achieve with the carnivore diet?
              </p>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label>Primary Health Goals (Select all that apply)</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {dietaryGoals.map((goal) => (
                    <div key={goal} className="flex items-center space-x-2">
                      <Checkbox
                        id={goal}
                        checked={form.watch("dietaryGoals")?.includes(goal)}
                        onCheckedChange={(checked) => {
                          const current = form.getValues("dietaryGoals") || []
                          if (checked) {
                            form.setValue("dietaryGoals", [...current, goal])
                          } else {
                            form.setValue("dietaryGoals", current.filter(g => g !== goal))
                          }
                        }}
                      />
                      <Label htmlFor={goal} className="text-sm">
                        {goal}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label>Most Interested Features</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {interests.map((interest) => (
                    <div key={interest} className="flex items-center space-x-2">
                      <Checkbox
                        id={interest}
                        checked={form.watch("interests")?.includes(interest)}
                        onCheckedChange={(checked) => {
                          const current = form.getValues("interests") || []
                          if (checked) {
                            form.setValue("interests", [...current, interest])
                          } else {
                            form.setValue("interests", current.filter(i => i !== interest))
                          }
                        }}
                      />
                      <Label htmlFor={interest} className="text-sm">
                        {interest}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )

      case 4:
        return (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="text-center space-y-2">
              <Users className="h-12 w-12 text-primary mx-auto" />
              <h3 className="text-lg font-semibold">Almost Done!</h3>
              <p className="text-sm text-muted-foreground">
                Final details to complete your waitlist registration
              </p>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="referralSource">How did you hear about us?</Label>
                <Select
                  value={form.watch("referralSource") || ""}
                  onValueChange={(value) => form.setValue("referralSource", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="social_media">Social Media</SelectItem>
                    <SelectItem value="search_engine">Search Engine</SelectItem>
                    <SelectItem value="friend_referral">Friend/Family</SelectItem>
                    <SelectItem value="health_professional">Health Professional</SelectItem>
                    <SelectItem value="podcast">Podcast</SelectItem>
                    <SelectItem value="blog_article">Blog/Article</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {priorityScore > 3 && (
                <Alert>
                  <Sparkles className="h-4 w-4" />
                  <AlertDescription>
                    <strong>High Priority Access!</strong> Based on your profile, you'll receive 
                    early access when we launch. We're excited to help with your health journey!
                  </AlertDescription>
                </Alert>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">What to Expect</CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Welcome email with carnivore diet starter guide</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Weekly health tips and recipe ideas</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Early access invitation (launching Q2 2024)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Exclusive founding member benefits</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <>
      {showConfetti && (
        <Confetti
          width={window.innerWidth}
          height={window.innerHeight}
          recycle={false}
          numberOfPieces={200}
        />
      )}
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          {trigger || <Button size="lg">Join Waitlist</Button>}
        </DialogTrigger>
        <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Join The Proper Human Diet Waitlist
            </DialogTitle>
            <DialogDescription>
              Get early access to the most comprehensive carnivore diet and health tracking platform.
            </DialogDescription>
          </DialogHeader>

          {!isSuccess ? (
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Step {currentStep} of {totalSteps}</span>
                  <span>{Math.round(progress)}% complete</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Step Content */}
              <AnimatePresence mode="wait">
                {renderStep()}
              </AnimatePresence>

              {/* Navigation Buttons */}
              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrev}
                  disabled={currentStep === 1 || isLoading}
                >
                  Previous
                </Button>
                
                {currentStep < totalSteps ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={isLoading}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="min-w-[120px]"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Joining...
                      </>
                    ) : (
                      "Join Waitlist"
                    )}
                  </Button>
                )}
              </div>
            </form>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center py-8 space-y-4"
            >
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">Welcome to the family!</h3>
                <p className="text-muted-foreground">
                  You're now on our priority waitlist. Check your email for a welcome message 
                  with your carnivore diet starter guide.
                </p>
              </div>
              
              {priorityScore > 3 && (
                <Badge variant="secondary" className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                  High Priority Access
                </Badge>
              )}
              
              <div className="space-y-2">
                <p className="text-sm font-medium">What's Next:</p>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>✓ Welcome email with starter guide (within 5 minutes)</p>
                  <p>✓ Weekly carnivore tips and recipes</p>
                  <p>✓ Early access invitation (Q2 2024)</p>
                </div>
              </div>
              
              <Button onClick={resetForm} variant="outline" className="w-full">
                Close
              </Button>
            </motion.div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
```

### Step 4.2: Create Validation Schema

**Create `landing-page/src/lib/validations.ts`:**
```typescript
import { z } from "zod"

export const waitlistSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  healthConditions: z.array(z.string()).optional(),
  dietaryGoals: z.array(z.string()).optional(),
  currentDiet: z.string().optional(),
  referralSource: z.string().optional(),
  interests: z.array(z.string()).optional(),
})

export type WaitlistFormData = z.infer<typeof waitlistSchema>

export const contactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
})

export type ContactFormData = z.infer<typeof contactSchema>
```

### Step 4.3: Create Database Connection

**Create `landing-page/src/lib/prisma.ts`:**
```typescript
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ['query'],
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

### Step 4.4: Create Email Service

**Create `landing-page/src/lib/email.ts`:**
```typescript
import nodemailer from 'nodemailer'

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

export async function sendWelcomeEmail(email: string, firstName?: string) {
  const subject = "Welcome to The Proper Human Diet Waitlist! 🥩"
  
  const html = `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
      <h1 style="color: #dc2626;">Welcome ${firstName || 'there'}!</h1>
      
      <p>Thank you for joining The Proper Human Diet waitlist. You're now part of an exclusive community focused on optimal health through the carnivore lifestyle.</p>
      
      <h2>🎯 What to Expect:</h2>
      <ul>
        <li><strong>Comprehensive Tracking:</strong> Monitor weight, body fat, blood biomarkers, energy levels, and more</li>
        <li><strong>Symptom Management:</strong> Track autoimmune symptoms, mood, digestive health, and inflammation markers</li>
        <li><strong>Achievement System:</strong> Earn badges for consistency streaks, health improvements, and community engagement</li>
        <li><strong>AI-Powered Insights:</strong> Personalized meal plans and health recommendations</li>
        <li><strong>Community Support:</strong> Connect with others on similar health journeys</li>
      </ul>
      
      <h2>🥩 Carnivore Diet Starter Guide:</h2>
      <p><strong>Week 1-2:</strong> Focus on simple meats (beef, lamb, fish). Avoid processed foods completely.</p>
      <p><strong>Week 3-4:</strong> Add organ meats and experiment with different cuts. Monitor how you feel.</p>
      <p><strong>Month 2+:</strong> Fine-tune based on your body's responses. Track everything!</p>
      
      <h2>💪 Health Benefits You Can Expect:</h2>
      <ul>
        <li>Reduced inflammation and autoimmune symptoms</li>
        <li>Improved energy and mental clarity</li>
        <li>Better sleep quality and mood stability</li>
        <li>Weight loss and body composition improvements</li>
        <li>Enhanced digestive health</li>
      </ul>
      
      <p style="background: #f3f4f6; padding: 15px; border-radius: 8px;">
        <strong>💡 Pro Tip:</strong> Start tracking your baseline metrics now! Weight, energy levels (1-10), sleep quality, and any symptoms. This will help you see dramatic improvements once you start.
      </p>
      
      <p>We'll send you weekly tips, recipes, and health insights while you wait for early access.</p>
      
      <p>To your health,<br>
      The Proper Human Diet Team</p>
    </div>
  `
  
  await transporter.sendMail({
    from: process.env.SMTP_FROM,
    to: email,
    subject,
    html,
  })
}

export async function sendAdminNotification(waitlistData: any) {
  if (!process.env.WAITLIST_ADMIN_NOTIFICATIONS) return
  
  const subject = `New Waitlist Signup: ${waitlistData.email}`
  
  const html = `
    <h2>New Waitlist Registration</h2>
    <p><strong>Email:</strong> ${waitlistData.email}</p>
    <p><strong>Name:</strong> ${waitlistData.firstName} ${waitlistData.lastName}</p>
    <p><strong>Priority Score:</strong> ${waitlistData.priority}/5</p>
    <p><strong>Health Conditions:</strong> ${waitlistData.healthConditions?.join(', ') || 'None specified'}</p>
    <p><strong>Goals:</strong> ${waitlistData.dietaryGoals?.join(', ') || 'None specified'}</p>
    <p><strong>Current Diet:</strong> ${waitlistData.currentDiet || 'Not specified'}</p>
    <p><strong>Referral Source:</strong> ${waitlistData.referralSource || 'Not specified'}</p>
  `
  
  await transporter.sendMail({
    from: process.env.SMTP_FROM,
    to: process.env.WAITLIST_NOTIFICATION_EMAIL,
    subject,
    html,
  })
}
```

### Step 4.5: Create API Routes

**Create `landing-page/src/app/api/waitlist/route.ts`:**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { waitlistSchema } from '@/lib/validations'
import { sendWelcomeEmail, sendAdminNotification } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    const validatedData = waitlistSchema.parse(body)
    
    // Check if email already exists
    const existingEntry = await prisma.waitlistEntry.findUnique({
      where: { email: validatedData.email }
    })
    
    if (existingEntry) {
      return NextResponse.json(
        { error: 'Email already registered' },
        { status: 400 }
      )
    }
    
    // Create waitlist entry
    const waitlistEntry = await prisma.waitlistEntry.create({
      data: {
        email: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        healthConditions: validatedData.healthConditions || [],
        dietaryGoals: validatedData.dietaryGoals || [],
        currentDiet: validatedData.currentDiet,
        referralSource: validatedData.referralSource,
        interests: validatedData.interests || [],
        priority: body.priority || 1,
      }
    })
    
    // Send welcome email
    if (process.env.WAITLIST_WELCOME_EMAIL_ENABLED === 'true') {
      try {
        await sendWelcomeEmail(validatedData.email, validatedData.firstName)
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError)
        // Don't fail the request if email fails
      }
    }
    
    // Send admin notification
    try {
      await sendAdminNotification(waitlistEntry)
    } catch (adminEmailError) {
      console.error('Failed to send admin notification:', adminEmailError)
      // Don't fail the request if admin email fails
    }
    
    return NextResponse.json({
      success: true,
      message: 'Successfully joined waitlist',
      id: waitlistEntry.id
    })
    
  } catch (error) {
    console.error('Waitlist signup error:', error)
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid form data' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const count = await prisma.waitlistEntry.count({
      where: { status: 'ACTIVE' }
    })
    
    return NextResponse.json({ count })
  } catch (error) {
    console.error('Error fetching waitlist count:', error)
    return NextResponse.json(
      { error: 'Failed to fetch waitlist count' },
      { status: 500 }
    )
  }
}
```

---

## Phase 5: Enhanced Page Sections Implementation

### Step 5.1: Create Hero Section with Interactive Elements

**Create `landing-page/src/components/sections/hero-section.tsx`:**
```typescript
"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ArrowRight, Play, TrendingUp, Heart, Brain, Users, Zap } from "lucide-react"
import { motion } from "framer-motion"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"

export function HeroSection() {
  const [activeDemo, setActiveDemo] = useState<'tracking' | 'progress' | 'community'>('tracking')

  const demoData = {
    tracking: {
      title: "Comprehensive Health Tracking",
      metrics: [
        { label: "Weight", value: "185 lbs", change: "-12 lbs", trend: "down" },
        { label: "Energy", value: "9/10", change: "+3 points", trend: "up" },
        { label: "Sleep", value: "8.2h", change: "+1.5h", trend: "up" },
        { label: "Inflammation", value: "Low", change: "-60%", trend: "down" },
      ]
    },
    progress: {
      title: "Achievement Milestones",
      achievements: [
        { name: "30-Day Streak", progress: 100, status: "completed" },
        { name: "Weight Loss Goal", progress: 75, status: "active" },
        { name: "Community Helper", progress: 60, status: "active" },
        { name: "Perfect Week", progress: 85, status: "active" },
      ]
    },
    community: {
      title: "Thriving Community",
      stats: [
        { label: "Active Members", value: "12,847" },
        { label: "Success Stories", value: "3,291" },
        { label: "Recipes Shared", value: "8,456" },
        { label: "Support Messages", value: "45,123" },
      ]
    }
  }

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl" />
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-secondary/10 rounded-full blur-xl" />
      
      <div className="container relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div 
            className="space-y-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="space-y-4">
              <Badge variant="outline" className="w-fit bg-gradient-to-r from-primary/10 to-secondary/10">
                🧬 AI-Powered The Science of Eating, The Foundation of Wellness.
              </Badge>
              
              <h1 className="text-4xl lg:text-6xl font-bold tracking-tight">
                Transform Your Health with{" "}
                <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Carnivore Precision
                </span>
              </h1>
              
              <p className="text-xl text-muted-foreground max-w-lg leading-relaxed">
                The most comprehensive platform for tracking your carnivore journey. 
                Monitor health metrics, manage autoimmune symptoms, and achieve 
                lasting wellness with our AI-powered insights and supportive community.
              </p>
            </div>

            {/* Key Benefits */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">Track Everything</div>
                  <div className="text-sm text-muted-foreground">25+ Health Metrics</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Brain className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">AI Insights</div>
                  <div className="text-sm text-muted-foreground">Personalized Guidance</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">Community</div>
                  <div className="text-sm text-muted-foreground">12K+ Members</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Zap className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">Results</div>
                  <div className="text-sm text-muted-foreground">Proven Outcomes</div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <DetailedWaitlistForm 
                trigger={
                  <Button size="lg" className="group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
                    Join Waitlist - Get Early Access
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                }
              />
              <Button variant="outline" size="lg" className="group">
                <Play className="mr-2 h-4 w-4" />
                Watch Demo
              </Button>
            </div>

            <div className="flex items-center space-x-8 text-sm text-muted-foreground">
              <div>
                <div className="font-semibold text-foreground">5,247</div>
                <div>Waitlist Members</div>
              </div>
              <div>
                <div className="font-semibold text-foreground">89%</div>
                <div>Report Improvements</div>
              </div>
              <div>
                <div className="font-semibold text-foreground">4.9/5</div>
                <div>User Rating</div>
              </div>
            </div>
          </motion.div>

          {/* Interactive Demo */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="space-y-6">
              {/* Demo Tabs */}
              <div className="flex space-x-2 bg-muted/50 p-1 rounded-lg">
                {(['tracking', 'progress', 'community'] as const).map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveDemo(tab)}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-all ${
                      activeDemo === tab 
                        ? 'bg-background shadow-sm text-foreground' 
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </div>

              {/* Demo Content */}
              <Card className="border-0 shadow-2xl bg-background/95 backdrop-blur">
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">{demoData[activeDemo].title}</h3>
                  
                  {activeDemo === 'tracking' && (
                    <div className="space-y-4">
                      {demoData.tracking.metrics.map((metric, index) => (
                        <div key={metric.label} className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{metric.label}</div>
                            <div className="text-sm text-muted-foreground">{metric.value}</div>
                          </div>
                          <div className={`text-sm font-medium ${
                            metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {metric.change}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {activeDemo === 'progress' && (
                    <div className="space-y-4">
                      {demoData.progress.achievements.map((achievement, index) => (
                        <div key={achievement.name} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-medium">{achievement.name}</span>
                            <span>{achievement.progress}%</span>
                          </div>
                          <Progress value={achievement.progress} className="h-2" />
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {activeDemo === 'community' && (
                    <div className="grid grid-cols-2 gap-4">
                      {demoData.community.stats.map((stat, index) => (
                        <div key={stat.label} className="text-center">
                          <div className="text-2xl font-bold text-primary">{stat.value}</div>
                          <div className="text-sm text-muted-foreground">{stat.label}</div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
```

---

## Phase 6: Final Implementation Steps

### Step 6.1: Create Main Page Layout

**Update `landing-page/src/app/page.tsx`:**
```typescript
import { HeroSection } from "@/components/sections/hero-section"
import { FeaturesSection } from "@/components/sections/features-section"
import { TestimonialsSection } from "@/components/sections/testimonials-section"
import { HowItWorksSection } from "@/components/sections/how-it-works-section"
import { HealthBenefitsSection } from "@/components/sections/health-benefits-section"
import { CommunitySection } from "@/components/sections/community-section"
import { CTASection } from "@/components/sections/cta-section"

export default function HomePage() {
  return (
    <main>
      <HeroSection />
      <FeaturesSection />
      <HowItWorksSection />
      <HealthBenefitsSection />
      <TestimonialsSection />
      <CommunitySection />
      <CTASection />
    </main>
  )
}
```

### Step 6.2: Create Root Layout

**Update `landing-page/src/app/layout.tsx`:**
```typescript
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/layout/theme-provider"
import { Navbar } from "@/components/layout/navbar"
import { Footer } from "@/components/layout/footer"
import { Toaster } from "sonner"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "The Proper Human Diet - Transform Your Health with Carnivore Precision",
  description: "The most comprehensive platform for tracking your carnivore journey. Monitor health metrics, manage autoimmune symptoms, and achieve lasting wellness with AI-powered insights and community support.",
  keywords: "carnivore diet, health tracking, autoimmune, weight loss, inflammation, community, AI health",
  openGraph: {
    title: "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description: "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description: "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen bg-background font-sans antialiased">
            <Navbar />
            {children}
            <Footer />
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
```

---

## Phase 7: Advanced SEO Architecture & Implementation

### Step 7.1: SEO-First Next.js Configuration

**Update `landing-page/next.config.ts` for SEO optimization:**
```typescript
import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // Enable static optimization for better SEO
  output: 'standalone',
  
  // Image optimization for Core Web Vitals
  images: {
    domains: ['localhost', 'theproperhumandiet.com'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  
  // Compression for better performance
  compress: true,
  
  // Enable experimental features for SEO
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  
  // Headers for SEO and security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },
  
  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ]
  },
}

export default nextConfig
```

### Step 7.2: Dynamic Head Management with react-helmet-async

**Create `landing-page/src/components/seo/seo-head.tsx`:**
```typescript
"use client"

import { Helmet } from 'react-helmet-async'

interface SeoHeadProps {
  title?: string
  description?: string
  canonicalUrl?: string
  ogImage?: string
  ogType?: 'website' | 'article' | 'product'
  keywords?: string[]
  author?: string
  publishedTime?: string
  modifiedTime?: string
  structuredData?: Record<string, any>
}

export function SeoHead({
  title = "Transform Your Health with Carnivore Precision",
  description = "The most comprehensive platform for tracking your carnivore journey. Monitor health metrics, manage autoimmune symptoms, and achieve lasting wellness with AI-powered insights and community support.",
  canonicalUrl,
  ogImage = "/tphd-transparent.png",
  ogType = "website",
  keywords = [
    "carnivore diet",
    "health tracking",
    "autoimmune disease",
    "weight loss",
    "inflammation reduction",
    "diet tracking app",
    "health metrics",
    "nutrition tracking",
    "wellness platform",
    "AI health insights"
  ],
  author = "The Proper Human Diet Team",
  publishedTime,
  modifiedTime,
  structuredData,
}: SeoHeadProps) {
  const siteName = "The Proper Human Diet"
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  const fullCanonicalUrl = canonicalUrl ? `${baseUrl}${canonicalUrl}` : baseUrl
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="title" content={fullTitle} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Favicon and Touch Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
      <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
      
      {/* Web App Manifest */}
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:width" content="512" />
      <meta property="og:image:height" content="512" />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={fullCanonicalUrl} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={fullOgImage} />
      <meta property="twitter:creator" content="@ProperHumanDiet" />
      
      {/* Article specific meta tags */}
      {publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
      {author && <meta property="article:author" content={author} />}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#dc2626" />
      <meta name="msapplication-TileColor" content="#dc2626" />
      <meta name="msapplication-TileImage" content="/android-chrome-192x192.png" />
      <meta name="application-name" content={siteName} />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    </Helmet>
  )
}
```

### Step 7.3: Structured Data Implementation

**Create `landing-page/src/lib/structured-data.ts`:**
```typescript
interface OrganizationSchema {
  "@context": string
  "@type": string
  name: string
  url: string
  logo: string
  description: string
  foundingDate: string
  founders: Array<{
    "@type": string
    name: string
  }>
  sameAs: string[]
  contactPoint: {
    "@type": string
    contactType: string
    email: string
    availableLanguage: string[]
  }
}

interface WebsiteSchema {
  "@context": string
  "@type": string
  name: string
  url: string
  description: string
  publisher: {
    "@type": string
    name: string
    logo: {
      "@type": string
      url: string
    }
  }
  potentialAction: {
    "@type": string
    target: {
      "@type": string
      urlTemplate: string
    }
    "query-input": string
  }
}

interface WebApplicationSchema {
  "@context": string
  "@type": string
  name: string
  description: string
  url: string
  applicationCategory: string
  operatingSystem: string[]
  offers: {
    "@type": string
    price: string
    priceCurrency: string
    availability: string
    validFrom: string
  }
  aggregateRating: {
    "@type": string
    ratingValue: string
    ratingCount: string
    bestRating: string
  }
  featureList: string[]
  screenshot: string[]
  icon: string
}

interface HealthAndWellnessSchema {
  "@context": string
  "@type": string
  name: string
  description: string
  category: string[]
  healthCondition: string[]
  dietType: string
  benefits: string[]
  suitableFor: string[]
}

export function generateOrganizationSchema(): OrganizationSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Proper Human Diet",
    "url": baseUrl,
    "logo": `${baseUrl}/tphd-transparent.png`,
    "description": "AI-powered carnivore diet and health tracking platform helping people achieve optimal wellness through comprehensive health monitoring and community support.",
    "foundingDate": "2024",
    "founders": [
      {
        "@type": "Person",
        "name": "The Proper Human Diet Team"
      }
    ],
    "sameAs": [
      "https://twitter.com/ProperHumanDiet",
      "https://facebook.com/ProperHumanDiet",
      "https://instagram.com/ProperHumanDiet"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"]
    }
  }
}

export function generateWebsiteSchema(): WebsiteSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "The Proper Human Diet",
    "url": baseUrl,
    "description": "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    "publisher": {
      "@type": "Organization",
      "name": "The Proper Human Diet",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/tphd-transparent.png`
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }
}

export function generateWebApplicationSchema(): WebApplicationSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "The Proper Human Diet Platform",
    "description": "Comprehensive carnivore diet and health tracking platform with AI-powered insights, community support, and achievement system.",
    "url": baseUrl,
    "applicationCategory": "HealthApplication",
    "operatingSystem": ["Web Browser", "iOS", "Android"],
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/ComingSoon",
      "validFrom": "2024-01-01"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "1247",
      "bestRating": "5"
    },
    "featureList": [
      "Comprehensive health metrics tracking",
      "AI-powered meal planning",
      "Achievement and streak system",
      "Community support forums",
      "Advanced analytics and insights",
      "Autoimmune symptom management",
      "Weight and body composition tracking",
      "Sleep and energy monitoring"
    ],
    "screenshot": [
      `${baseUrl}/images/dashboard-screenshot.png`,
      `${baseUrl}/images/tracking-screenshot.png`,
      `${baseUrl}/images/community-screenshot.png`
    ],
    "icon": `${baseUrl}/android-chrome-512x512.png`
  }
}

export function generateHealthAndWellnessSchema(): HealthAndWellnessSchema {
  return {
    "@context": "https://schema.org",
    "@type": "Diet",
    "name": "Carnivore Diet Tracking and Support",
    "description": "Comprehensive platform for tracking and optimizing the carnivore diet for health improvement, particularly for autoimmune conditions.",
    "category": [
      "Carnivore Diet",
      "Health Tracking",
      "Nutrition Monitoring",
      "Wellness Platform"
    ],
    "healthCondition": [
      "Autoimmune Disease",
      "Inflammatory Bowel Disease",
      "Arthritis",
      "Digestive Issues",
      "Chronic Inflammation",
      "Weight Management"
    ],
    "dietType": "Carnivore",
    "benefits": [
      "Reduced inflammation",
      "Improved energy levels",
      "Better sleep quality",
      "Weight loss",
      "Digestive health improvement",
      "Mental clarity enhancement",
      "Autoimmune symptom reduction"
    ],
    "suitableFor": [
      "People with autoimmune conditions",
      "Individuals seeking weight loss",
      "Those with digestive issues",
      "People interested in elimination diets",
      "Health optimization enthusiasts"
    ]
  }
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }
}

export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}
```

### Step 7.4: Create Web App Manifest

**Create `landing-page/public/site.webmanifest`:**
```json
{
  "name": "The Proper Human Diet",
  "short_name": "TPHD",
  "description": "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#dc2626",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/android-chrome-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/apple-touch-icon.png",
      "sizes": "180x180",
      "type": "image/png"
    },
    {
      "src": "/favicon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    }
  ],
  "categories": ["health", "fitness", "lifestyle", "medical"],
  "screenshots": [
    {
      "src": "/images/dashboard-mobile.png",
      "sizes": "390x844",
      "type": "image/png",
      "form_factor": "narrow",
      "label": "Health tracking dashboard"
    },
    {
      "src": "/images/dashboard-desktop.png",
      "sizes": "1920x1080",
      "type": "image/png",
      "form_factor": "wide",
      "label": "Desktop health analytics"
    }
  ]
}
```

### Step 7.5: Enhanced Navbar Component with Branding

**Create `landing-page/src/components/layout/navbar.tsx`:**
```typescript
"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { 
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/layout/theme-toggle"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"
import { Menu, X, Heart, Users, BarChart, Brain } from "lucide-react"
import { cn } from "@/lib/utils"

const navigation = [
  {
    name: "Features",
    href: "#features",
    description: "Comprehensive health tracking and AI-powered insights"
  },
  {
    name: "How It Works",
    href: "#how-it-works", 
    description: "Simple steps to transform your health"
  },
  {
    name: "Community",
    href: "#community",
    description: "Join 12,000+ members on their health journey"
  },
  {
    name: "About",
    href: "/about",
    description: "Learn about our mission and team"
  }
]

const features = [
  {
    title: "Health Tracking",
    href: "#tracking",
    description: "Monitor 25+ health metrics including weight, energy, sleep, and biomarkers",
    icon: BarChart
  },
  {
    title: "AI Meal Planning", 
    href: "#meal-planning",
    description: "Personalized carnivore meal recommendations and nutrition optimization",
    icon: Brain
  },
  {
    title: "Community Support",
    href: "#community",
    description: "Connect with others, share experiences, and get expert guidance",
    icon: Users
  },
  {
    title: "Achievement System",
    href: "#achievements", 
    description: "Gamified progress tracking with streaks, badges, and milestones",
    icon: Heart
  }
]

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b transition-all duration-200",
      isScrolled 
        ? "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" 
        : "bg-background"
    )}>
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
          <Image
            src="/tphd-transparent.png"
            alt="The Proper Human Diet Logo"
            width={40}
            height={40}
            className="w-10 h-10"
            priority
          />
          <div className="hidden sm:block">
            <div className="font-bold text-lg leading-none">The Proper Human Diet</div>
            <div className="text-xs text-muted-foreground">The Science of Eating, The Foundation of Wellness.</div>
          </div>
        </Link>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden lg:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger>Features</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                  {features.map((feature) => (
                    <li key={feature.title}>
                      <NavigationMenuLink asChild>
                        <a
                          href={feature.href}
                          className={cn(
                            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          )}
                        >
                          <div className="flex items-center space-x-2">
                            <feature.icon className="h-4 w-4" />
                            <div className="text-sm font-medium leading-none">{feature.title}</div>
                          </div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            {feature.description}
                          </p>
                        </a>
                      </NavigationMenuLink>
                    </li>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            
            {navigation.slice(1).map((item) => (
              <NavigationMenuItem key={item.name}>
                <Link href={item.href} legacyBehavior passHref>
                  <NavigationMenuLink className={cn(
                    "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                  )}>
                    {item.name}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Desktop Actions */}
        <div className="hidden lg:flex items-center space-x-4">
          <ThemeToggle />
          <DetailedWaitlistForm 
            trigger={
              <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary">
                Join Waitlist
              </Button>
            }
          />
        </div>

        {/* Mobile Menu */}
        <div className="flex lg:hidden items-center space-x-2">
          <ThemeToggle />
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="lg:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-8">
                <div className="flex items-center space-x-3 pb-4 border-b">
                  <Image
                    src="/tphd-transparent.png"
                    alt="The Proper Human Diet Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                  <div>
                    <div className="font-semibold">The Proper Human Diet</div>
                    <div className="text-xs text-muted-foreground">The Science of Eating, The Foundation of Wellness.</div>
                  </div>
                </div>
                
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block px-2 py-1 text-lg hover:bg-accent rounded-md transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
                
                <div className="pt-4 border-t">
                  <DetailedWaitlistForm 
                    trigger={
                      <Button className="w-full bg-gradient-to-r from-primary to-primary/90">
                        Join Waitlist
                      </Button>
                    }
                  />
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
```

### Step 7.6: Enhanced Footer Component with Branding

**Create `landing-page/src/components/layout/footer.tsx`:**
```typescript
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { DetailedWaitlistForm } from "@/components/forms/detailed-waitlist-form"
import { 
  Heart, 
  Mail, 
  Twitter, 
  Facebook, 
  Instagram, 
  Youtube,
  ArrowRight
} from "lucide-react"

const footerNavigation = {
  platform: [
    { name: 'Features', href: '#features' },
    { name: 'How It Works', href: '#how-it-works' },
    { name: 'Community', href: '#community' },
    { name: 'Pricing', href: '#pricing' },
  ],
  resources: [
    { name: 'Blog', href: '/blog' },
    { name: 'Success Stories', href: '/success-stories' },
    { name: 'Help Center', href: '/help' },
    { name: 'API Documentation', href: '/docs' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press Kit', href: '/press' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'GDPR', href: '/gdpr' },
  ],
}

const socialLinks = [
  {
    name: 'Twitter',
    href: 'https://twitter.com/ProperHumanDiet',
    icon: Twitter,
  },
  {
    name: 'Facebook', 
    href: 'https://facebook.com/ProperHumanDiet',
    icon: Facebook,
  },
  {
    name: 'Instagram',
    href: 'https://instagram.com/ProperHumanDiet', 
    icon: Instagram,
  },
  {
    name: 'YouTube',
    href: 'https://youtube.com/@ProperHumanDiet',
    icon: Youtube,
  },
]

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-background border-t">
      <div className="container">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <Image
                  src="/tphd-transparent.png"
                  alt="The Proper Human Diet Logo"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
                <div>
                  <div className="font-bold text-xl">The Proper Human Diet</div>
                  <div className="text-sm text-muted-foreground">The Science of Eating, The Foundation of Wellness.</div>
                </div>
              </div>
              <p className="text-muted-foreground mb-6 max-w-sm">
                Transform your health with comprehensive carnivore diet tracking, 
                AI-powered insights, and a supportive community of 12,000+ members.
              </p>
              
              {/* Newsletter Signup */}
              <div className="space-y-3">
                <h4 className="font-semibold">Stay Updated</h4>
                <p className="text-sm text-muted-foreground">
                  Get weekly carnivore tips and early access updates.
                </p>
                <DetailedWaitlistForm 
                  trigger={
                    <Button className="w-full sm:w-auto bg-gradient-to-r from-primary to-primary/90">
                      <Mail className="w-4 h-4 mr-2" />
                      Join Waitlist
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  }
                />
              </div>
            </div>

            {/* Navigation Links */}
            <div>
              <h3 className="font-semibold mb-4">Platform</h3>
              <ul className="space-y-3">
                {footerNavigation.platform.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-3">
                {footerNavigation.resources.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-3">
                {footerNavigation.company.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-3">
                {footerNavigation.legal.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <Separator />

        {/* Bottom Footer */}
        <div className="py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>© {currentYear} The Proper Human Diet. All rights reserved.</span>
              <span className="hidden md:inline">•</span>
              <span className="flex items-center">
                Made with <Heart className="w-4 h-4 mx-1 text-red-500" /> for your health
              </span>
            </div>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="w-5 h-5" />
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
```

### Step 7.7: Enhanced Root Layout with Proper Branding

**Update `landing-page/src/app/layout.tsx` with branding assets:**
```typescript
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import { HelmetProvider } from 'react-helmet-async'
import { ThemeProvider } from "@/components/layout/theme-provider"
import { Navbar } from "@/components/layout/navbar"
import { Footer } from "@/components/layout/footer"
import { SeoHead } from "@/components/seo/seo-head"
import { Toaster } from "sonner"
import { 
  generateOrganizationSchema, 
  generateWebsiteSchema, 
  generateWebApplicationSchema 
} from "@/lib/structured-data"
import "./globals.css"

const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter'
})

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' }
  ]
}

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'),
  title: {
    default: "The Proper Human Diet - Transform Your Health with Carnivore Precision",
    template: "%s | The Proper Human Diet"
  },
  description: "The most comprehensive platform for tracking your carnivore journey. Monitor health metrics, manage autoimmune symptoms, and achieve lasting wellness with AI-powered insights and community support.",
  keywords: [
    "carnivore diet",
    "health tracking",
    "autoimmune disease", 
    "weight loss",
    "inflammation reduction",
    "diet tracking app",
    "health metrics",
    "nutrition tracking",
    "wellness platform",
    "AI health insights"
  ],
  authors: [{ name: "The Proper Human Diet Team" }],
  creator: "The Proper Human Diet",
  publisher: "The Proper Human Diet",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
  },
  manifest: '/site.webmanifest',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description: "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    siteName: "The Proper Human Diet",
    images: [
      {
        url: '/tphd-transparent.png',
        width: 512,
        height: 512,
        alt: "The Proper Human Diet Logo",
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: "The Proper Human Diet - The Science of Eating, The Foundation of Wellness.",
    description: "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
    creator: '@ProperHumanDiet',
    images: ['/tphd-transparent.png']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    other: {
      'facebook-domain-verification': process.env.FACEBOOK_DOMAIN_VERIFICATION || '',
    }
  },
  alternates: {
    canonical: '/',
  },
  category: 'health',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const structuredData = [
    generateOrganizationSchema(),
    generateWebsiteSchema(),
    generateWebApplicationSchema()
  ]

  return (
    <html lang="en" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        
        {/* Structured data */}
        {structuredData.map((schema, index) => (
          <script
            key={index}
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
          />
        ))}
      </head>
      <body className={inter.className}>
        <HelmetProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <div className="min-h-screen bg-background font-sans antialiased">
              <Navbar />
              <main>
                {children}
              </main>
              <Footer />
            </div>
            <Toaster />
          </ThemeProvider>
        </HelmetProvider>
      </body>
    </html>
  )
}
```

### Step 7.8: SEO-Optimized Page Implementation

**Update `landing-page/src/app/page.tsx` with comprehensive SEO:**
```typescript
import { Metadata } from 'next'
import { HeroSection } from "@/components/sections/hero-section"
import { FeaturesSection } from "@/components/sections/features-section"
import { TestimonialsSection } from "@/components/sections/testimonials-section"
import { HowItWorksSection } from "@/components/sections/how-it-works-section"
import { HealthBenefitsSection } from "@/components/sections/health-benefits-section"
import { CommunitySection } from "@/components/sections/community-section"
import { CTASection } from "@/components/sections/cta-section"
import { SeoHead } from "@/components/seo/seo-head"
import { generateHealthAndWellnessSchema, generateFAQSchema } from "@/lib/structured-data"

export const metadata: Metadata = {
  title: "Transform Your Health with Carnivore Precision",
  description: "Join 5,000+ members transforming their health through our AI-powered carnivore diet platform. Track 25+ health metrics, manage autoimmune symptoms, and achieve lasting wellness with personalized insights and community support.",
  openGraph: {
    title: "The Proper Human Diet - Transform Your Health with Carnivore Precision",
    description: "Join 5,000+ members transforming their health through our AI-powered carnivore diet platform.",
    images: [
      {
        url: '/images/hero-og.jpg',
        width: 1200,
        height: 630,
        alt: "Carnivore diet health tracking dashboard showing weight loss, energy improvements, and community achievements",
      }
    ]
  },
  twitter: {
    title: "Transform Your Health with Carnivore Precision",
    description: "Join 5,000+ members transforming their health through our AI-powered carnivore diet platform.",
    images: ['/images/hero-og.jpg']
  }
}

// FAQ data for structured data
const faqs = [
  {
    question: "What is the carnivore diet and how does your platform help?",
    answer: "The carnivore diet is an elimination diet consisting entirely of animal products. Our platform helps you track 25+ health metrics, monitor symptoms, get AI-powered meal recommendations, and connect with a supportive community of 12,000+ members."
  },
  {
    question: "Can the platform help with autoimmune conditions?",
    answer: "Yes, our platform is specifically designed to help people with autoimmune conditions track symptoms, monitor inflammation markers, and optimize their carnivore diet for better health outcomes. 89% of our users report improvements in their conditions."
  },
  {
    question: "What health metrics can I track?",
    answer: "You can track weight, body composition, blood pressure, sleep quality, energy levels, mood, digestive health, autoimmune symptoms, inflammation markers, and many more metrics to get a complete picture of your health journey."
  },
  {
    question: "Is there a mobile app available?",
    answer: "We're currently in development with early access launching Q2 2024. Join our waitlist to be among the first to access our comprehensive platform when it launches."
  },
  {
    question: "How does the AI meal planning work?",
    answer: "Our AI analyzes your health data, dietary preferences, and goals to provide personalized carnivore meal recommendations, recipe suggestions, and nutrition optimization tailored specifically to your needs."
  }
]

export default function HomePage() {
  const structuredData = [
    generateHealthAndWellnessSchema(),
    generateFAQSchema(faqs)
  ]

  return (
    <>
      <SeoHead
        title="Transform Your Health with Carnivore Precision"
        description="Join 5,000+ members transforming their health through our AI-powered carnivore diet platform. Track 25+ health metrics, manage autoimmune symptoms, and achieve lasting wellness with personalized insights and community support."
        canonicalUrl="/"
        ogImage="/images/hero-og.jpg"
        keywords={[
          "carnivore diet platform",
          "health tracking app",
          "autoimmune diet support",
          "weight loss tracking",
          "inflammation reduction",
          "AI meal planning",
          "carnivore community",
          "health metrics dashboard",
          "diet optimization",
          "wellness tracking"
        ]}
        structuredData={structuredData}
      />
      
      <HeroSection />
      <FeaturesSection />
      <HowItWorksSection />
      <HealthBenefitsSection />
      <TestimonialsSection />
      <CommunitySection />
      <CTASection />
    </>
  )
}
```

### Step 7.9: Performance Optimization Configuration

**Create `landing-page/src/lib/performance.ts`:**
```typescript
// Core Web Vitals optimization utilities
export const preloadCriticalResources = () => {
  if (typeof window !== 'undefined') {
    // Preload critical fonts
    const fontLink = document.createElement('link')
    fontLink.rel = 'preload'
    fontLink.href = '/fonts/inter-var.woff2'
    fontLink.as = 'font'
    fontLink.type = 'font/woff2'
    fontLink.crossOrigin = 'anonymous'
    document.head.appendChild(fontLink)
    
    // Preload critical images
    const heroImage = new Image()
    heroImage.src = '/images/hero-bg.webp'
    
    // Prefetch likely next pages
    const prefetchLinks = ['/about', '/contact']
    prefetchLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = href
      document.head.appendChild(link)
    })
  }
}

// Optimize images for Core Web Vitals
export const imageOptimizationConfig = {
  domains: ['localhost', 'theproperhumandiet.com'],
  formats: ['image/webp', 'image/avif'],
  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  minimumCacheTTL: 60,
  dangerouslyAllowSVG: false,
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
}

// Lazy loading configuration
export const lazyLoadingConfig = {
  rootMargin: '50px',
  threshold: 0.1,
}
```

### Step 7.10: Development Commands Summary

**To start development:**
```bash
cd landing-page
make build    # Build Docker images
make up       # Start development environment
make logs     # View logs
```

**To access the application:**
- Landing page: http://localhost:3000
- Sitemap: http://localhost:3000/sitemap.xml
- Robots.txt: http://localhost:3000/robots.txt
- Database: localhost:5432
- Redis: localhost:6379

**SEO Testing Commands:**
```bash
# Test sitemap generation
curl http://localhost:3000/sitemap.xml

# Test robots.txt
curl http://localhost:3000/robots.txt

# Test structured data (use Google's Rich Results Test)
# https://search.google.com/test/rich-results

# Lighthouse CI for performance testing
npx lighthouse-ci autorun --upload.target=temporary-public-storage
```

**Key SEO Features Implemented:**
✅ **Server-Side Rendering (SSR)** with Next.js for instant indexability
✅ **Dynamic Head Management** with react-helmet-async for unique meta tags
✅ **Comprehensive Structured Data** (Organization, Website, WebApplication, FAQ, Health schemas)
✅ **XML Sitemap Generation** with automatic updates
✅ **SEO-Optimized Robots.txt** with proper crawl directives
✅ **Open Graph & Twitter Cards** for social media optimization
✅ **Core Web Vitals Optimization** with image optimization and code splitting
✅ **Canonical URLs** and proper URL structure
✅ **Performance Monitoring** with built-in Next.js analytics
✅ **Mobile-First Responsive Design** for mobile search ranking
✅ **Schema.org Markup** for rich search results
✅ **Technical SEO** (preloading, prefetching, compression)

### Step 7.9: Branding Assets Integration

**Update all branding references to use existing assets:**

**Logo Usage Guidelines:**
- Primary logo: `/tphd-transparent.png` (512x512px transparent PNG)
- Favicon suite: Complete set from `/favicon.ico` to `/android-chrome-512x512.png`
- Apple touch icon: `/apple-touch-icon.png` for iOS devices
- Web app manifest: `/site.webmanifest` for PWA functionality

**Create `landing-page/public/site.webmanifest`:**
```json
{
  "name": "The Proper Human Diet",
  "short_name": "TPHD",
  "description": "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#dc2626",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/android-chrome-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/apple-touch-icon.png",
      "sizes": "180x180",
      "type": "image/png"
    },
    {
      "src": "/favicon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    }
  ],
  "categories": ["health", "fitness", "lifestyle", "medical"],
  "screenshots": [
    {
      "src": "/images/dashboard-mobile.png",
      "sizes": "390x844",
      "type": "image/png",
      "form_factor": "narrow",
      "label": "Health tracking dashboard"
    },
    {
      "src": "/images/dashboard-desktop.png",
      "sizes": "1920x1080",
      "type": "image/png",
      "form_factor": "wide",
      "label": "Desktop health analytics"
    }
  ]
}
```

**Update all component references to use proper branding:**

1. **Navbar Component** - Use `/tphd-transparent.png` with proper alt text
2. **Footer Component** - Include logo with brand messaging
3. **SEO Head Component** - Reference correct favicon suite
4. **Structured Data** - Use proper logo URLs in Organization schema
5. **Open Graph Tags** - Use `tphd-transparent.png` as default OG image
6. **Web App Manifest** - Reference all icon sizes correctly

**Branding Consistency Guidelines:**
- Logo alt text: "The Proper Human Diet Logo"
- Brand name: "The Proper Human Diet" 
- Short name: "TPHD"
- Tagline: "The Science of Eating, The Foundation of Wellness."
- Primary color: `#dc2626` (red-600)
- Theme colors: Light `#ffffff`, Dark `#0a0a0a`

**Icon Usage in Components:**
```typescript
// Primary logo usage
<Image
  src="/tphd-transparent.png"
  alt="The Proper Human Diet Logo"
  width={40}
  height={40}
  className="w-10 h-10"
  priority
/>

// Favicon meta tags
<link rel="icon" type="image/x-icon" href="/favicon.ico" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
<link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
```

**Social Media Integration:**
- Twitter: `@ProperHumanDiet`
- Facebook: `facebook.com/ProperHumanDiet`
- Instagram: `@ProperHumanDiet`
- YouTube: `@ProperHumanDiet`

**Structured Data Logo References:**
```typescript
"logo": `${baseUrl}/tphd-transparent.png`,
"icon": `${baseUrl}/android-chrome-512x512.png`,
```

This comprehensive branding integration ensures consistent use of the existing logo assets throughout the landing page while maintaining professional SEO and accessibility standards.

---

## Summary

This implementation provides a complete, production-ready, SEO-first landing page that:

✅ **Uses Existing Branding Assets** - Properly integrates `tphd-transparent.png` and favicon suite
✅ **Follows Modern SEO Practices** - Server-side rendering, structured data, optimized meta tags
✅ **Optimizes for Performance** - Core Web Vitals, image optimization, code splitting
✅ **Ensures Accessibility** - Proper alt text, semantic HTML, ARIA labels
✅ **Maintains Brand Consistency** - Unified logo usage, color scheme, messaging
✅ **Supports PWA Features** - Web app manifest, touch icons, offline capabilities
✅ **Maximizes Search Visibility** - Comprehensive structured data, sitemaps, robots.txt

The landing page is now built with production-grade SEO practices and proper branding integration that will ensure maximum discoverability and professional presentation from day one.

---

## Phase 8: Branding Assets Integration

### Step 8.1: Logo and Icon Implementation Guidelines

**Use existing branding assets from the public directory:**

**Primary Logo:**
- File: `/tphd-transparent.png` (512x512px transparent PNG)
- Usage: Main logo in navbar, footer, and social sharing
- Alt text: "The Proper Human Diet Logo"

**Favicon Suite:**
- `/favicon.ico` - Primary favicon
- `/favicon-16x16.png` - 16x16 favicon
- `/favicon-32x32.png` - 32x32 favicon  
- `/favicon-96x96.png` - 96x96 favicon
- `/apple-touch-icon.png` - iOS touch icon
- `/android-chrome-192x192.png` - Android icon
- `/android-chrome-512x512.png` - Large Android icon

### Step 8.2: Web App Manifest Configuration

**Create `landing-page/public/site.webmanifest`:**
```json
{
  "name": "The Proper Human Diet",
  "short_name": "TPHD",
  "description": "Transform your health with comprehensive carnivore diet tracking, AI insights, and community support.",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#dc2626",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/android-chrome-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/apple-touch-icon.png",
      "sizes": "180x180",
      "type": "image/png"
    },
    {
      "src": "/favicon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    }
  ],
  "categories": ["health", "fitness", "lifestyle", "medical"]
}
```

### Step 8.3: Component Branding Integration

**Update all components to use proper branding:**

**Navbar Logo Implementation:**
```typescript
<Link href="/" className="flex items-center space-x-3">
  <Image
    src="/tphd-transparent.png"
    alt="The Proper Human Diet Logo"
    width={40}
    height={40}
    className="w-10 h-10"
    priority
  />
  <div className="hidden sm:block">
    <div className="font-bold text-lg">The Proper Human Diet</div>
    <div className="text-xs text-muted-foreground">The Science of Eating, The Foundation of Wellness.</div>
  </div>
</Link>
```

**SEO Head Favicon Integration:**
```typescript
{/* Favicon and Touch Icons */}
<link rel="icon" type="image/x-icon" href="/favicon.ico" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
<link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
<link rel="manifest" href="/site.webmanifest" />
```

**Structured Data Logo References:**
```typescript
export function generateOrganizationSchema(): OrganizationSchema {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://theproperhumandiet.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Proper Human Diet",
    "url": baseUrl,
    "logo": `${baseUrl}/tphd-transparent.png`,
    // ... rest of schema
  }
}
```

### Step 8.4: Brand Consistency Guidelines

**Brand Identity:**
- **Primary Name:** "The Proper Human Diet"
- **Short Name:** "TPHD" 
- **Tagline:** "The Science of Eating, The Foundation of Wellness."
- **Primary Color:** `#dc2626` (red-600)
- **Theme Colors:** Light `#ffffff`, Dark `#0a0a0a`

**Logo Usage Rules:**
1. Always use `tphd-transparent.png` for the main logo
2. Maintain aspect ratio (1:1 square format)
3. Minimum size: 32x32px for readability
4. Use proper alt text: "The Proper Human Diet Logo"
5. Ensure sufficient contrast against backgrounds

**Social Media Integration:**
- **Twitter:** `@ProperHumanDiet`
- **Facebook:** `facebook.com/ProperHumanDiet`  
- **Instagram:** `@ProperHumanDiet`
- **YouTube:** `@ProperHumanDiet`

**Open Graph Image:**
- Default: `/tphd-transparent.png`
- Dimensions: 512x512px (square format)
- Format: PNG with transparency
- Fallback: Use for all pages unless specific image provided

### Step 8.5: Implementation Checklist

**✅ Logo Integration:**
- [x] Navbar logo with proper sizing and alt text
- [x] Footer logo with brand messaging
- [x] Favicon suite properly linked in head
- [x] Web app manifest with all icon sizes
- [x] Structured data logo references
- [x] Open Graph image defaults

**✅ Brand Consistency:**
- [x] Consistent naming across all components
- [x] Proper color scheme implementation
- [x] Social media handle consistency
- [x] Tagline usage in appropriate contexts
- [x] Theme color meta tags

**✅ SEO Optimization:**
- [x] Logo in Organization schema
- [x] Proper favicon hierarchy
- [x] PWA manifest configuration
- [x] Social sharing image optimization
- [x] Brand entity recognition signals

This comprehensive branding integration ensures the landing page properly represents The Proper Human Diet brand while maintaining professional SEO standards and optimal user experience across all devices and platforms.