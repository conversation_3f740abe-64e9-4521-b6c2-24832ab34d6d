import { waitlistSchema } from './src/lib/validations';

const testData = {
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  healthConditions: ['Skin Conditions'],
  dietaryGoals: ['Autoimmune Management'],
  currentDiet: 'Vegetarian/Vegan',
  referralSource: null,
  interests: ['Health Tracking'],
  monthlyBudget: '50-100'
};

console.log('🔍 Original test data:');
console.log(JSON.stringify(testData, null, 2));

try {
  const validated = waitlistSchema.parse(testData);
  console.log('\n✅ Validated data:');
  console.log(JSON.stringify(validated, null, 2));
  
  console.log('\n🔍 Checking specific fields:');
  console.log('firstName:', validated.firstName, typeof validated.firstName);
  console.log('lastName:', validated.lastName, typeof validated.lastName);
} catch (error) {
  console.error('❌ Validation error:', error);
}

// Test with empty strings
const testDataWithEmptyStrings = {
  email: '<EMAIL>',
  firstName: '',
  lastName: '',
  healthConditions: ['Skin Conditions'],
  dietaryGoals: ['Autoimmune Management'],
  currentDiet: 'Vegetarian/Vegan',
  referralSource: null,
  interests: ['Health Tracking'],
  monthlyBudget: '50-100'
};

console.log('\n🔍 Test data with empty strings:');
console.log(JSON.stringify(testDataWithEmptyStrings, null, 2));

try {
  const validated2 = waitlistSchema.parse(testDataWithEmptyStrings);
  console.log('\n✅ Validated data with empty strings:');
  console.log(JSON.stringify(validated2, null, 2));
  
  console.log('\n🔍 Checking specific fields:');
  console.log('firstName:', validated2.firstName, typeof validated2.firstName);
  console.log('lastName:', validated2.lastName, typeof validated2.lastName);
} catch (error) {
  console.error('❌ Validation error:', error);
}
