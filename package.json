{"name": "the-proper-human-diet-landing", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3222", "build": "next build", "start": "next start -p 3222", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "db:generate": "prisma generate", "db:push": "prisma db push", "export:waitlist": "ts-node --project tsconfig.scripts.json scripts/export-waitlist.ts", "test:waitlist": "ts-node --project tsconfig.scripts.json scripts/test-waitlist-flow.ts"}, "dependencies": {"@clerk/nextjs": "^6.23.1", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.6.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-toast": "^1.1.5", "@sentry/nextjs": "^7.108.0", "@tabler/icons-react": "^3.34.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "form-data": "^4.0.3", "framer-motion": "^10.16.0", "kbar": "^0.1.0-beta.45", "lenis": "^1.3.4", "lucide-react": "^0.292.0", "mailgun.js": "^12.0.3", "next": "^14.2.30", "next-themes": "^0.2.1", "node-fetch": "^3.3.2", "nodemailer": "^6.9.7", "prisma": "^5.6.0", "react": "^18.0.0", "react-confetti": "^6.1.0", "react-day-picker": "^9.7.0", "react-dom": "^18.0.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.47.0", "recharts": "^3.0.2", "sonner": "^1.2.0", "tailwind-merge": "^2.0.0", "typescript": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.0.0", "@types/nodemailer": "^6.4.14", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "msw": "^2.10.2", "postcss": "^8.0.0", "postcss-nesting": "^12.1.5", "prisma-mock": "^0.10.4", "supertest": "^7.1.1", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "vitest": "^3.2.4"}}